#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
1、查询恶意标签
2、用于持续性事件标签增强
3、每5分钟跑一次
"""
import argparse
import datetime
import json
import os
import sys
import time
import traceback
from collections import OrderedDict
from django.db.models import F, Func, Q

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.base_model import ContinuousEventsTagModel, ContinuousEventsConfModel, EventDetailsModel
from cncert_kj.models.continuous_events_tag_models import ContinuousEventsTag
from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import event_other_tag_name
from cncert_kj.utils.time_trans import timestamp2format
from cncert_kj.utils.request_malicious import RequestMalicious

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('event_malicious_tag_script')
SRC_TAG_TYPE = 1
DST_TAG_TYPE = 2
MALICIOUS_TAG = event_other_tag_name["恶意情报"]


class EventMaliciousTag(object):
    """
    1、默认token长期有效
    2、目前只增强到事件中  不打到告警表
    3、就只取接口返回的maliciousTagSe字段  # 恶意标签列表
    4、目前只查ipv4的
    5、源目ip都查
    """

    def __init__(self):
        self.tclog_conf_key = "tclog_malicious_tag_timestamp"
        self.netflow_conf_key = "netflow_malicious_tag_timestamp"
        self.step = 10
        self.request_client = RequestMalicious()

    def query_malicious_tag_timestamp(self, report_type):
        """
        获取上次处理的时间
        第一次执行时默认当前时间
        """
        conf_key = self.tclog_conf_key if report_type == 1 else self.netflow_conf_key

        queryset = ContinuousEventsConfModel.objects.filter(conf_key=conf_key).first()
        if queryset:
            value = queryset.conf_value
        else:
            value = int(time.time())
            ContinuousEventsConfModel.objects.create(conf_key=conf_key, conf_value=value)
        return value

    def _build_event_query(self, last_end_time, analysis_tech, start_time, end_time, event_type, report_type):
        """
        构建事件查询 SQL 和参数
        """
        sql = """
            SELECT event_id, src_ip, dst_ip, start_time, end_time, dst_info, src_info 
            FROM internal_app_bsa_gjk.continuous_events
            WHERE ipv6 = False
        """
        params = []

        if analysis_tech == 2:
            if not all([start_time, end_time]):
                today = datetime.date.today()
                start_time = int(time.mktime(today.timetuple())) - 86400
                end_time = int(time.mktime(today.timetuple()))
            sql += " AND analysis_tech = %s AND end_time > %s AND end_time <= %s"
            params.extend([analysis_tech, start_time, end_time])
            if event_type:
                if len(event_type) == 1:
                    event_type *= 2
                sql += " AND event_type IN %s"
                params.append(tuple(event_type))
        else:
            sql += " AND end_time > %s AND report_type = %s"
            params.extend([last_end_time, report_type])

        sql += " ORDER BY end_time ASC"
        return sql, params, start_time, end_time

    def _extract_event_data(self, result, existing_event_ids):
        """
        处理查询结果，提取 IP 和事件信息
        """
        event_ip_list = []
        event_info = OrderedDict()
        event_max_time = 0

        for item in result:
            event_id = item[0]
            if event_id in existing_event_ids:
                continue

            src_ip, dst_ip, end_time, dst_info, src_info = item[1], item[2], item[4], item[5], item[6]

            if dst_ip:
                event_ip_list.append(dst_ip)
            if src_ip:
                event_ip_list.append(src_ip)

            event_info[event_id] = [src_ip, dst_ip, end_time, dst_info, src_info]
            if end_time > event_max_time:
                event_max_time = end_time

        return event_info, list(set(event_ip_list)), event_max_time

    def get_event(self, last_end_time=None, analysis_tech=None, start_time=None, end_time=None, event_type=None,
                  report_type=1):
        """
        获取本次需要更新的事件
        """

        sql, params, start_time, end_time = self._build_event_query(
            last_end_time, analysis_tech, start_time, end_time, event_type, report_type
        )

        result = json.loads(CFunction.execute(CPgSqlParam(sql, params=tuple(params))))
        mlog.info("最新生成的事件数量:{}".format(len(result)))

        # 获取已经查询过的事件id
        base_event_ids = [i[0] for i in result]
        event_ids = self.get_event_details(base_event_ids)
        mlog.info("本次事件里已存在事件详情数量:{}".format(len(event_ids)))

        return self._extract_event_data(result, event_ids)

    def get_event2(self, report_type, event_ids):
        """
        获取本次需要更新的事件
        """

        sql = """
                    SELECT event_id, src_ip, dst_ip, start_time, end_time, dst_info, src_info 
                    FROM internal_app_bsa_gjk.continuous_events
                    WHERE ipv6 = False AND report_type = %s AND event_id in %s
                """
        params = [report_type, tuple(event_ids)]
        result = json.loads(CFunction.execute(CPgSqlParam(sql, params=tuple(params))))
        mlog.info("最新生成的事件数量:{}".format(len(result)))

        # 获取已经查询过的事件id
        base_event_ids = [i[0] for i in result]
        event_ids = self.get_event_details(base_event_ids)
        mlog.info("本次事件里已存在事件详情数量:{}".format(len(event_ids)))

        return self._extract_event_data(result, event_ids)

    def get_malicious_tag_type(self, ip_list):
        """
        批量查询恶意标签信息
        """
        data = {}
        try:
            if not ip_list:
                mlog.info("ip列表为空")
                return data
            res = self.request_client.get_malicious_tag_info(ip_list)
            if not res or res.get("code") != 200:
                mlog.info("查询恶意标签信息失败:{}".format(res))
                return data
            result = res.get("data", [])
            if not result:
                mlog.info("查询恶意标签信息为空 data:{}".format(result))
                return data
            for obj in result:
                # 恶意标签列表
                malicious_tag_set = obj.get("maliciousTagSet", [])
                # 恶意标签详情列表
                malicious_tag_detail_vo_list = obj.get("maliciousTagDetailVOList", [])
                # 正常标签列表
                normal_tag_set = obj.get("normalTagSet", [])
                # 正常标签详情列表
                normal_tag_detail_vo_list = obj.get("normalTagDetailVOList", [])
                ip = obj.get("ip")
                if not ip:
                    continue
                data[ip] = {"malicious_tag_set": malicious_tag_set,
                            "malicious_tag_detail_vo_list": malicious_tag_detail_vo_list,
                            "normal_tag_set": normal_tag_set,
                            "normal_tag_detail_vo_list": normal_tag_detail_vo_list}
            return data
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())
            return data

    def insert_or_update_data(self, params):
        """
        数据已存在则更新， 不存在则插入
        """
        step = 1000
        for i in range(0, len(params), step):
            mlog.info("--{}--".format(i))
            param = params[i: i + step]
            sql = """
            INSERT INTO internal_app_bsa_gjk.event_details 
            (event_id, src_malicious_tag_detail, dst_malicious_tag_detail, create_time, update_time)
            VALUES {format}
            ON CONFLICT (event_id) DO 
            UPDATE SET 
                src_malicious_tag_detail = EXCLUDED.src_malicious_tag_detail,
                dst_malicious_tag_detail = EXCLUDED.dst_malicious_tag_detail,
                update_time=EXCLUDED.update_time;
            """.format(format=",".join(["%s"] * len(param)))
            CFunction.execute(CPgSqlParam(sql, params=tuple(param)))
        mlog.info("插入成功事件详情:{}".format(len(params)))

    def update_ddos_timestamp(self, end_time, report_type):
        conf_key = self.tclog_conf_key if report_type == 1 else self.netflow_conf_key
        ret = ContinuousEventsConfModel.objects.filter(conf_key=conf_key).update(conf_value=end_time)
        mlog.info("结果：{}；更新记录时间为{} - {}".format(ret, end_time, timestamp2format(end_time)))

    def get_event_details(self, base_event_ids):
        """
        获取事件详情里查询过过恶意标签的event_id
        """
        qs = (
            EventDetailsModel.objects.filter(event_id__in=base_event_ids)
            .annotate(
                src_len=Func(
                    F('src_malicious_tag_detail'),
                    function='json_array_length'
                ),
                dst_len=Func(
                    F('dst_malicious_tag_detail'),
                    function='json_array_length'
                ),
            )
            .filter(
                Q(src_len__gt=0) |
                Q(dst_len__gt=0)
            )
            .values_list('event_id', flat=True)
        )

        # 如果需要得到列表：
        event_ids = set(qs)
        return event_ids

    def get_malicious_tag_info(self, msg, ip_list):
        malicious_tag_info = {}

        begin_time = int(time.time())

        for i in range(0, len(ip_list), self.step):
            current_ips = ip_list[i:i + self.step]

            mlog.info("{}查询标签信息中, 索引: {}, 查询IP数量: {}".format(msg, i, len(current_ips)))

            ret = self.get_malicious_tag_type(current_ips)
            if not ret:
                continue

            malicious_tag_info.update(ret)

        stop_time = int(time.time())
        mlog.info("{} 查询恶意标签信息完成, 耗时: {} s".format(msg, stop_time - begin_time))
        return malicious_tag_info

    def _prepare_tag_data(self, event_info, tag_info, max_end_time):
        create_params = []
        tag_detail_params = []
        event_ids = []

        for event_id, (src_ip, dst_ip, end_time, _, _) in event_info.items():
            s_tags, s_details = self._get_combined_tags(tag_info.get(src_ip, {}))
            d_tags, d_details = self._get_combined_tags(tag_info.get(dst_ip, {}))
            max_end_time = max(max_end_time, end_time)

            if not (s_tags or d_tags):
                continue

            event_ids.append(event_id)
            now = int(time.time())

            create_params += [(event_id, SRC_TAG_TYPE, MALICIOUS_TAG, tag) for tag in s_tags]
            create_params += [(event_id, DST_TAG_TYPE, MALICIOUS_TAG, tag) for tag in d_tags]

            if s_details or d_details:
                tag_detail_params.append(
                    (
                        event_id, json.dumps(s_details, ensure_ascii=False), json.dumps(d_details, ensure_ascii=False),
                        now,
                        now)
                )

        return create_params, tag_detail_params, event_ids, max_end_time

    def _get_combined_tags(self, tag_data):
        malicious_tags = tag_data.get("malicious_tag_set") or []
        normal_tags = tag_data.get("normal_tag_set") or []
        tag_list = [t for t in (malicious_tags + normal_tags) if t is not None]

        malicious_details = tag_data.get("malicious_tag_detail_vo_list") or []
        normal_details = tag_data.get("normal_tag_detail_vo_list") or []
        detail_list = (malicious_details + normal_details)

        return tag_list, detail_list

    def _insert_tag_data_in_batches(self, create_params, event_ids):
        BATCH_SIZE = 1000
        for i in range(0, len(create_params), BATCH_SIZE):
            batch = create_params[i:i + BATCH_SIZE]
            mlog.info("插入恶意标签: 批次 {}，条数: {}".format(i // BATCH_SIZE + 1, len(batch)))
            ContinuousEventsTagModel.objects.filter(
                event_id__in=event_ids,
                tag_type__in=[SRC_TAG_TYPE, DST_TAG_TYPE],
                tag_name=MALICIOUS_TAG
            ).delete()
            ContinuousEventsTag().bulk_create(batch)

    def run(self, analysis_tech=None, start_time=None, end_time=None, event_type=None, report_type=1, event_ids=None):
        """
        检查新事件的恶意标签信息
        """
        msg = ""
        try:
            mlog.info("==========================开始恶意标签信息增强任务============================")
            # 事件的最大结束事件，在没用查到三方系统信息时用于更新时间戳
            event_max_time = 0
            last_end_time = max_end_time = self.query_malicious_tag_timestamp(report_type)
            mlog.info("查询脚本上次执行时间: malicious_tag_timestamp:{}  - {}".format(last_end_time,
                                                                                      timestamp2format(last_end_time)))

            mlog.info("获取新生成事件信息")
            if event_ids is None:
                if analysis_tech:
                    msg = "动态基线"
                    event_info, ip_list, _ = self.get_event(analysis_tech=analysis_tech, start_time=start_time,
                                                            end_time=end_time, event_type=event_type)
                else:
                    report_str = "通联日志" if report_type == 1 else "netflow"
                    msg = "静态基线：{}".format(report_str)
                    event_info, ip_list, event_max_time = self.get_event(last_end_time=last_end_time,
                                                                         report_type=report_type)
            else:
                event_info, ip_list, event_max_time = self.get_event2(report_type, event_ids)
            mlog.info("{} 需要查询的ip数量:{}".format(msg, len(ip_list)))
            mlog.info("{} 获取恶意标签信息".format(msg))

            malicious_tag_info = self.get_malicious_tag_info(msg, ip_list)

            self.request_client.close_session()

            if not malicious_tag_info:
                mlog.info("{} 事件数量:{}, 标签数量:{}-不更新malicious_tag_timestamp时间".format(msg, len(event_info),
                                                                                                 len(malicious_tag_info)))
                # 如果此次任务没有任何标签信息，则没有继续的必要，直接结束,如果有事件，把时间戳改为事件的最大结束时间
                if event_info and event_max_time and analysis_tech != 2:
                    self.update_ddos_timestamp(event_max_time, report_type)
                return False
            create_params, tag_detail_params, event_ids, max_end_time = self._prepare_tag_data(
                event_info, malicious_tag_info, max_end_time
            )

            # 分批插入恶意标签
            self._insert_tag_data_in_batches(create_params, event_ids)

            # 更新事件详情表
            self.insert_or_update_data(tag_detail_params)

            if analysis_tech == 2:
                mlog.info("动态基线的事件增强不更新时间")
                return
            # 更新时间
            self.update_ddos_timestamp(max_end_time, report_type)
        except Exception as e:
            mlog.error("{}恶意标签任务出错".format(msg))
            mlog.error(e)
            mlog.error(traceback.format_exc())
        mlog.info("=========================={} 恶意标签增强任务结束============================".format(msg))


if __name__ == '__main__':
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument("--type", "-t", help="1通联日志，2netflow, 默认通联，", type=int, default=1)
        args = parser.parse_args()
        mlog.info("参数:type：{};{}".format(args.type, "通联日志" if args.type == 1 else "netflow"))

        obj = EventMaliciousTag()
        obj.run(report_type=args.type)
    except Exception as e:
        mlog.error("运行失败")
        mlog.error(traceback.format_exc())
