#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    统计一天范围内所有事件,一个源ip对应出现了多少个不同对端ip,以及流量是多少
"""
import os
import time

import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from datetime import datetime, timedelta
from collections import defaultdict

from django.db import models, connection
from django.db.models import Q

from cncert_kj.models.base_model import ContinuousEvents
from cncert_kj.utils import logger

mlog = logger.init_logger('ip_count_script')


def ensure_tmp_table():
    """
    检查并创建临时表，如果不存在则自动创建
    """
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT to_regclass('internal_app_bsa_gjk.ip_count_table_tmp');
        """)
        exists = cursor.fetchone()[0]
        if not exists:
            cursor.execute("""
                CREATE TABLE internal_app_bsa_gjk.ip_count_table_tmp (
                    id bigserial PRIMARY KEY,
                    ip varchar(64),
                    ip_count integer,
                    flow bigint,
                    ip_type integer
                );
            """)
            mlog.info("已自动创建临时表 internal_app_bsa_gjk.ip_count_table_tmp")
        else:
            mlog.info("临时表 internal_app_bsa_gjk.ip_count_table_tmp 已存在")


class IpCount(object):
    def get_timestamp(self):
        """优化后：保持日期日与当前相同，若目标月份无该日则取该月最后一天"""
        current_time = datetime.now()

        try:
            one_month_ago = current_time.replace(month=current_time.month - 1)
        except ValueError:
            # 计算目标月份的最后一天
            new_month = current_time.month - 1
            new_year = current_time.year
            if new_month == 0:
                new_month = 12
                new_year -= 1

            # 计算下个月份和年份
            next_month = new_month + 1
            next_year = new_year
            if next_month > 12:
                next_month = 1
                next_year += 1

            # 获取下个月1日并减1天得到最后一天
            last_day = datetime(next_year, next_month, 1) - timedelta(days=1)

            # 构造合法日期（保留原时间的时分秒）
            one_month_ago = datetime(
                new_year, new_month,
                last_day.day,
                current_time.hour, current_time.minute, current_time.second
            )
        timestamp = int(time.mktime(one_month_ago.timetuple()))
        mlog.info("一月前的时间：{}，{}".format(one_month_ago, timestamp))
        return timestamp

    def get_event_data(self):
        """获取一月前所有事件"""
        data = ContinuousEvents.objects.filter(end_time__gte=self.get_timestamp(),
                                               # data = ContinuousEvents.objects.filter(end_time__gte=1745222991,
                                               src_ip__isnull=False,
                                               dst_ip__isnull=False
                                               ).exclude(Q(src_ip="") | Q(dst_ip="")).values('src_ip', 'dst_ip'
                                                                                             ).annotate(

            up_bytes_sum=models.Sum('up_bytesall')
        )
        mlog.info("查询语句：{}".format(data.query))
        return data

    def dispose_data(self, data):
        src_ip_dic = defaultdict(list)
        dst_ip_dic = defaultdict(list)

        for item in data:
            src_ip_dic[item['src_ip']].append(item)
            dst_ip_dic[item['dst_ip']].append(item)

        insert_list = []
        for src_ip, item in src_ip_dic.items():
            ip_count = 0
            flow = 0
            for i in item:
                ip_count += 1
                flow += i['up_bytes_sum']

            insert_list.append((src_ip, ip_count, flow, 1))

        for dst_ip, item in dst_ip_dic.items():
            ip_count = 0
            flow = 0
            for i in item:
                ip_count += 1
                flow += i['up_bytes_sum']

            insert_list.append((dst_ip, ip_count, flow, 2))

        return insert_list

    def _switch_table_with_retry(self):
        """
        尝试将临时表切换为正式表，失败时等待10秒后重试一次。
        """
        import time
        attempt = 0
        max_attempts = 2
        while attempt < max_attempts:
            try:
                with connection.cursor() as cursor:
                    mlog.info("准备执行表切换")
                    # 关闭自动提交，手动控制事务
                    old_autocommit = connection.autocommit
                    connection.autocommit = False

                    # 开始一个事务
                    cursor.execute("BEGIN;")
                    mlog.info("设置锁超时时间为2秒")
                    # 在当前事务中设置锁超时
                    cursor.execute("SET LOCAL lock_timeout = '2s';")
                    mlog.info("设置锁超时时间完成; 如果存在bak表，删除。")
                    cursor.execute("""
                        DROP TABLE IF EXISTS internal_app_bsa_gjk.ip_count_table_bak;
                    """)
                    mlog.info("开始表切换")
                    cursor.execute("""
                        ALTER TABLE IF EXISTS internal_app_bsa_gjk.ip_count_table 
                        RENAME TO ip_count_table_bak;
                    """)
                    mlog.info("原表重命名为备份表（bak）完成")
                    mlog.info("临时表（tmp）重命名为正式表")
                    cursor.execute("""
                        ALTER TABLE internal_app_bsa_gjk.ip_count_table_tmp 
                        RENAME TO ip_count_table;
                    """)
                    mlog.info("表切换完成")
                    mlog.info("开始删除旧的备份表（bak）")
                    cursor.execute("""
                        DROP TABLE IF EXISTS internal_app_bsa_gjk.ip_count_table_bak;
                    """)
                    mlog.info("删除旧的备份表完成")

                    # 显式提交事务
                    cursor.execute("COMMIT;")
                    connection.autocommit = old_autocommit
                return  # 成功后直接返回
            except Exception as e:
                # 显式回滚事务
                try:
                    with connection.cursor() as cursor:
                        cursor.execute("ROLLBACK;")
                    connection.autocommit = old_autocommit
                except Exception as _:
                    pass

                attempt += 1
                if attempt < max_attempts:
                    mlog.error(u"表切换失败，第%d次尝试，错误信息：%s，10秒后重试..." % (attempt, str(e)))
                    time.sleep(10)
                else:
                    mlog.error(u"表切换重试仍失败，错误信息：%s" % str(e))
                    raise

    def run(self):
        try:
            mlog.info("----开始统计当月所有事件")
            event_data = self.get_event_data()
            mlog.info("获取当天所有事件数据,数据条数:{}".format(len(event_data)))
            if not event_data:
                mlog.info("----当天没有事件，无需统计")
                return
            insert_list = self.dispose_data(event_data)
            mlog.info("处理后数据条数:{}".format(len(insert_list)))

            # 将Model类移到外面，改用原生SQL操作临时表
            with connection.cursor() as cursor:
                # 确保临时表存在，如果不存在则创建临时表
                ensure_tmp_table()

                # 清空临时表
                cursor.execute("TRUNCATE TABLE internal_app_bsa_gjk.ip_count_table_tmp;")

                # 批量插入数据
                batch_size = 10000
                n = 0
                all_n = len(insert_list) // batch_size + 1
                for i in range(0, len(insert_list), batch_size):
                    n += 1
                    batch = insert_list[i:i + batch_size]

                    # >> 构建占位符
                    placeholders = ','.join(['%s'] * len(batch))

                    # 构建SQL语句
                    sql = """
                        INSERT INTO internal_app_bsa_gjk.ip_count_table_tmp
                        (ip, ip_count, flow, ip_type)
                        VALUES {};
                    """.format(placeholders)

                    # 执行参数化查询
                    cursor.execute(sql, params=tuple(batch))
                    mlog.info("写入批次：{}/{} ; 本次写入 {} 条数据到临时表".format(n, all_n, len(batch)))

            mlog.info("ip_count临时表新增数据完成，准备切换到正式表")

            # 3. 切换数据到正式表（通过重命名实现瞬时切换，不用清空正式表），添加重试机制
            self._switch_table_with_retry()

            #>> 【修改二】: 使用 CREATE INDEX CONCURRENTLY 并且在事务外执行
            # CONCURRENTLY 选项可以在不阻塞写入操作的情况下创建索引，是避免锁表的关键。
            # 注意：CREATE INDEX CONCURRENTLY 不能在事务块内部执行，所以我们为它单独创建一个 cursor。
            # 给新表创建索引
            mlog.info("给新表创建索引")
            with connection.cursor() as cursor:
                # >> Django的cursor会自动处理事务，但CONCURRENTLY需要非事务环境，
                # Django的connection在autocommit模式下可以满足此要求。
                connection.autocommit = True
                cursor.execute("""
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS ip_count_table_ip_index ON internal_app_bsa_gjk.ip_count_table (ip);
                """)
                cursor.execute("""
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS ip_count_table_ip_type_index ON internal_app_bsa_gjk.ip_count_table 
                    (ip_type);
                """)
                connection.autocommit = False  # 恢复默认行为
                mlog.info("索引创建完成")

            mlog.info("----ip一对多统计表新增成功")
        except Exception as e:
            # 增加对锁超时的专门捕获和日志记录
            error_msg = str(e)
            if "lock_timeout" in error_msg:
                mlog.exception("----表切换失败：获取锁超时。请检查是否有其他长事务锁定了 ip_count_table。")
            elif "deadlock" in error_msg.lower():
                mlog.exception("----表切换失败：检测到死锁情况。")
            else:
                mlog.exception("----ip一对多统计表新增失败")

            # 确保在失败时也清理临时表
            try:
                with connection.cursor() as cursor:
                    cursor.execute("DROP TABLE IF EXISTS internal_app_bsa_gjk.ip_count_table_tmp;")
            except Exception as _:
                pass


if __name__ == '__main__':
    IpCount().run()
