#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    补充研判数据库事件（一天以前未研判的事件）
    每天凌晨零点运行一次
"""
import datetime
import time

from cncert_kj.models.continuous_events_tag_models import ContinuousEventsTag
from cncert_kj.script.cont_event_judge import AutoJudge
from cncert_kj.script.ftp_judge import FtpJudge
from cncert_kj.utils import logger
from cncert_kj.utils.request_asset import RequestAsset

mlog = logger.init_logger('cont_evnet_judge_ago')


def judge_ago_event():
    """
        二次研判探测任务下发未返回数据的事件
    """
    mlog.info("========开始重要数据库补充研判：{}".format(datetime.datetime.now()))
    cet_obj = ContinuousEventsTag()
    aj = AutoJudge()
    req_asset = RequestAsset()
    event_type_name = "数据库默认端口"
    ftp_types = aj.get_types(event_type_name)
    filter_judge_status = [1]

    et = int(time.time())
    st = et - 3600 * 24 * 30
    # 查询持续性事件
    filter_res = aj.filter_cont_event(st, et, ftp_types, filter_judge_status)
    mlog.info("持续性事件数量：{}".format(len(filter_res)))

    record_id = 0
    try:
        if len(filter_res) == 0:
            mlog.info("=======无事件，研判结束：{}".format(datetime.datetime.now()))
            return

        for i in filter_res:
            if i:
                event_id = i.get("event_id")
                # mlog.info("当前事件id:{}".format(event_id))
                record_id = event_id
                event_type = i.get("event_type")

                src_ip = i.get("src_ip")
                src_info = i.get("src_info")
                src_service_list = [k for k in i.get("src_service") if k]
                src_service = src_service_list[0] if src_service_list else ""

                dst_ip = i.get("dst_ip")
                dst_info = i.get("dst_info")
                dst_service_list = [k for k in i.get("dst_service") if k]
                dst_service = dst_service_list[0] if dst_service_list else ""

                judge_info = i.get("judge_info")

                # 主动类型的查目的ip，目的信息 区分主被动类型
                upload_types, download_type = aj.up_or_down_types(ftp_types)
                mlog.info(download_type)
                if event_type in upload_types:
                    ip_ = dst_ip
                    sql_service = dst_service
                # 被动类型的查源ip，源信息
                else:
                    ip_ = src_ip
                    sql_service = src_service

                if not sql_service:
                    mlog.info("id:{}, ip:{},数据库类型为空，跳过".format(event_id, ip_))
                    continue

                asset_res = req_asset.do_asset_url(ip_)
                mlog.info("sql_service:{}, {}".format(sql_service, type(sql_service)))
                is_true_service, port = aj.is_true_service_func(asset_res, sql_service.encode("utf-8"))

                if is_true_service:
                    mlog.info("ip:{},是真实数据库, 数据库类型：{}，端口：{}".format(ip_, sql_service, port))
                    judge_status = 7  # 自动研判，未通报
                    # 是真实数据库，但是，没有弱口令，不改变处置状态
                    if judge_info:
                        judge_info += "\n" + "是否是真实数据库：【是】"
                    else:
                        judge_info = "是否是真实数据库：【是】"
                else:
                    judge_status = 6  # 误报
                    mlog.info("ip:{},研判结果为：误报".format(ip_))
                    # 误报，直接修改处置状态为 6，换下一个事件
                    aj.update_judge_info(event_id, judge_status)
                    continue

                # 主动类型的查目的ip，目的信息
                if event_type in upload_types:
                    dst_info += '&真实数据库'
                    aj.update_judge_info(event_id, judge_status, judge_info=judge_info, dst_info=dst_info)
                    # 同步到事件扩展表中
                    cet_obj.filter_and_insert(event_id, 2, "real_db", "真实数据库")
                # 被动类型的查源ip，源信息
                else:
                    src_info += '&真实数据库'
                    aj.update_judge_info(event_id, judge_status, judge_info=judge_info, src_info=src_info)
                    # 同步到事件扩展表中
                    cet_obj.filter_and_insert(event_id, 1, "real_db", "真实数据库")

        mlog.info("=======补充研判结束：{}".format(datetime.datetime.now()))
    except Exception as e:
        mlog.exception("补充研判失败，记录当前id:{}，失败原因：{}".format(record_id, e))
    req_asset.close_session()


if __name__ == '__main__':
    judge_ago_event()  # 重要数据库研判
    FtpJudge().run()  # ftp研判
    mlog.info("=================结束任务=================")
