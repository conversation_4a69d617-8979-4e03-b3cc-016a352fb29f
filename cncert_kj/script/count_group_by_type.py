#!/usr/bin/env python
# -*- coding:utf-8 -*-
import datetime
import hashlib
import json
import os
import sys
import time
import traceback

from collections import OrderedDict, defaultdict

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction

from cncert_kj.utils import logger
from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.lib import openpyxl
from cncert_kj.lib.openpyxl.styles import Font, Alignment, Border, Side, colors, PatternFill

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('count_group_by_type')

APT_FORMAT = "%APT%"

def get_alert_template_data():
    """
    获取告警模版里的数据，用以后续数据比对获取
    """
    current_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    file_path = os.path.join(current_path, "template", "KJ告警模型梳理与统计.xlsx")
    # 打开Excel文件
    workbook = openpyxl.load_workbook(file_path)
    # 获取所有工作表对象
    sheets = workbook.worksheets
    sheet = sheets[1]
    is_tclog = True
    last_alert_category = ""
    last_data_category = ""
    # 初始化字典
    model_principles = {"TCLOG": {"5min": {}, "day": {}}, "NETFLOW": {"5min": {}, "day": {}}}
    for row in sheet.iter_rows(min_row=3, values_only=True):
        data_category, alert_category, alert_name, model_principle, detect_type, detect_cycle, type_value = row[1:8]
        last_data_category = data_category if data_category and data_category != last_data_category else last_data_category
        last_alert_category = alert_category if alert_category and alert_category != last_alert_category else last_alert_category
        if row[0] == "NETFLOW":
            is_tclog = False
        key = "APT_{}".format(type_value) if row[7] == 91 and "APT" in alert_name else type_value
        # 判断数据源
        cycle_key = "5min" if detect_cycle == "5min" else "day"
        data_source = "TCLOG" if is_tclog else "NETFLOW"
        model_principles[data_source][cycle_key][key] = {
            "data_source": data_source,
            "data_category": last_data_category,
            "alert_category": last_alert_category,
            "alert_name": alert_name,
            "model_principle": model_principle,
            "detect_type": detect_type,
            "detect_cycle": detect_cycle,
            "type_value": type_value
        }
    return (model_principles["TCLOG"]["5min"], model_principles["TCLOG"]["day"], model_principles["NETFLOW"]["5min"],
            model_principles["NETFLOW"]["day"])


def merge_tllog(report_type, start_timestamp, end_timestamp, analysis_tech, belong):
    """创建临时表，内容是
    检测手段， 静态基线检测-5min：1， 动态基线检测-天模型：2"""
    if report_type == 1:
        alert_table_name = "internal_app_bsa_gjk.traffic_alert"
    elif report_type == 2:
        alert_table_name = "internal_app_bsa_gjk.netflow_alert_5min"
    else:
        mlog.info("report_type错误")
        return []
    if analysis_tech == 1:
        _rule_table_name = "internal_app_bsa_gjk.detect_rule"
    else:
        _rule_table_name = "internal_app_bsa_gjk.dynamic_detect_rule"
    params = [belong, start_timestamp, end_timestamp, start_timestamp, end_timestamp, analysis_tech, report_type,
              start_timestamp, end_timestamp, start_timestamp, end_timestamp, start_timestamp, end_timestamp,
              analysis_tech]
    create_temp_table = """
    DROP TABLE IF EXISTS temp_type_conf;
    DROP TABLE IF EXISTS temp_tllog;
    CREATE TEMP TABLE temp_tllog as select type,threshold, detect_name from {_rule_table_name} where belong=%s and type != 91;

    select t.type, t.threshold, t1.alert_count, t2.event_count,t.detect_name from temp_tllog as t
    left join (select type,count(*) as alert_count from {alert_table_name}
    where 
        (
        (end_time BETWEEN  %s AND %s) OR (start_time BETWEEN  %s AND %s)
        ) and analysis_tech=%s  
    group by type) as t1 on t1.type=t.type
    left join (select event_type,count(*) as event_count from internal_app_bsa_gjk.continuous_events 
    where 
        report_type=%s AND 
        (
        (end_time BETWEEN  %s AND %s) OR (start_time BETWEEN  %s AND %s)  OR (start_time <= %s  AND end_time >= %s)
        ) and analysis_tech=%s 
    group by event_type) as t2 on t2.event_type = t.type
    order by t.type;
    """.format(_rule_table_name=_rule_table_name, alert_table_name=alert_table_name)

    res = json.loads(CFunction.execute(CPgSqlParam(create_temp_table, params=params)))
    """
    由于境内向境外APT组织非常用端口上传告警和境内重点单位向境外非常用端口上传告警的type 都是91 所以要分开处理，
    """
    params2 = [APT_FORMAT, APT_FORMAT, start_timestamp, end_timestamp, start_timestamp, end_timestamp, analysis_tech, APT_FORMAT,
               APT_FORMAT, report_type, start_timestamp, end_timestamp, start_timestamp, end_timestamp, start_timestamp,
               end_timestamp, analysis_tech,
               belong]
    _sql = """
        WITH alert_counts AS (
        SELECT
            type,
            COUNT(CASE WHEN name LIKE %s THEN 1 END) AS alert_count_1,
            COUNT(CASE WHEN name NOT LIKE %s THEN 1 END) AS alert_count_2
        FROM
            {alert_table_name}
        WHERE
            (
            (end_time BETWEEN  %s AND %s) or (start_time BETWEEN  %s AND %s)
            ) and analysis_tech=%s and type=91
        GROUP BY
            type
            ),
        event_counts AS (
            SELECT
                event_type,
                COUNT(CASE WHEN c_e_tag.dst_info LIKE %s THEN 1 END) AS event_count_1,
                COUNT(CASE WHEN c_e_tag.dst_info NOT LIKE %s OR c_e_tag.dst_info IS NULL THEN 1 END) AS event_count_2
            FROM
                internal_app_bsa_gjk.continuous_events AS e1
                left join (
                        SELECT event_id, string_agg(case when tag_type=2 then tag_content end,'&') dst_info
                        FROM internal_app_bsa_gjk.continuous_events_tag cet
                        GROUP BY event_id
                                    ) c_e_tag on e1.event_id = c_e_tag.event_id
            WHERE
                report_type=%s AND (
                (end_time BETWEEN  %s AND %s) OR 
                (start_time BETWEEN  %s AND %s) OR 
                (start_time <= %s  AND end_time >= %s)
                ) AND analysis_tech=%s AND event_type=91
            GROUP BY
                event_type
        )
        SELECT
            t.type,
            t.threshold,
            COALESCE(a.alert_count_1, 0) AS alert_count_1,
            COALESCE(a.alert_count_2, 0) AS alert_count_2,
            COALESCE(e.event_count_1, 0) AS event_count_1,
            COALESCE(e.event_count_2, 0) AS event_count_2,
            t.detect_name
        FROM
            {_rule_table_name} AS t
        LEFT JOIN
            alert_counts AS a ON a.type = t.type
        LEFT JOIN
            event_counts AS e ON e.event_type = t.type
        WHERE
            t.belong = %s
            AND t.type = 91
        ORDER BY
            t.type;
        """.format(alert_table_name=alert_table_name, _rule_table_name=_rule_table_name)
    res2 = json.loads(CFunction.execute(CPgSqlParam(_sql, params=tuple(params2))))
    for i in res2:
        if "APT" in i[6].upper():
            # APT类型的告警
            res.append([i[0], i[1], i[2], i[4], i[6]])
        else:
            res.append([i[0], i[1], i[3], i[5], i[6]])
    if report_type == 2 and analysis_tech == 1:
        # 只有netflow在静态基线下才走到这里
        _sql2 = """
            CREATE TEMP TABLE temp_type_conf as
            select type_value, type_name from internal_app_bsa_gjk.type_conf
            where is_tclog=false and type_value ::integer IN (12, 32, 42);
            select
                t.type_value,
                0 AS threshold,
                t1.alert_count,
                t2.event_count,
                t.type_name || '告警' AS type_name_with_alert
            from temp_type_conf as t
            left join (select type,count(*) as alert_count from {alert_table_name}
            where
                (
            (end_time BETWEEN  %s AND %s) or (start_time BETWEEN  %s AND %s)
            ) and analysis_tech=%s AND type IN (12, 32, 42)
            group by type) as t1 on t1.type=t.type_value::integer
            left join (select event_type,count(*) as event_count from internal_app_bsa_gjk.continuous_events
            where
                report_type=%s AND (
            (end_time BETWEEN  %s AND %s) or (start_time BETWEEN  %s AND %s) OR (start_time <= %s  AND end_time >= %s)
            ) and analysis_tech=%s AND event_type IN (12, 32, 42)
            group by event_type) as t2 on t2.event_type = t.type_value::integer
            order by t.type_value;
            """.format(alert_table_name=alert_table_name)
        res3 = json.loads(CFunction.execute(CPgSqlParam(_sql2, params=params[1:])))
        res.extend(res3)
    return res


def datetime_to_timestamp(dt):
    """datetime类型转时间戳"""
    return int(time.mktime(dt.timetuple()))


def dispose_data(_map, data):
    alert_total = 0
    event_total = 0
    for i in data:
        if not i[0]:
            continue
        alert_name = i[4]
        alert_count_ = i[2] if i[2] else 0
        event_count_ = i[3] if i[3] else 0
        if event_count_ > alert_count_:
            event_count_ = alert_count_ / 2
        alert_total += alert_count_
        event_total += event_count_

        if alert_name in _map:
            _map[alert_name]["_count"].append(alert_count_)
            _map[alert_name]["_count"].append(event_count_)
    mlog.info("alert_total: {}, event_total: {}".format(alert_total, event_total))
    return _map


def print_table(array):
    # 计算每列的最大宽度
    col_widths = [max(len(str(item)) for item in col) for col in zip(*array)]

    # 格式化表格的行
    def format_row(row_):
        return " | ".join("{:{}}".format(item, col_widths[idx]) for idx, item in enumerate(row_))

    # 打印表头
    header = array[0]
    mlog.info(format_row(header))
    mlog.info("-+-".join("-" * width for width in col_widths))
    # 打印数据行
    for row in array[1:]:
        mlog.info(format_row(row))


def get_all_detect_rule(belong):
    """
     获取detect_rule表中所有告警
     belong neflow为1,tclog为2
     """
    try:
        static_baseline_sql = '''
            SELECT
                rule_id,
                type,
                detect_name,
                threshold,
                status
            FROM internal_app_bsa_gjk.detect_rule
            WHERE belong = %s AND type NOT IN (0,1,51)
            ORDER BY type ASC
        '''
        static_baseline_param = CPgSqlParam(static_baseline_sql, params=(belong,))
        static_baseline_res_list = json.loads(CFunction.execute(static_baseline_param))
        return static_baseline_res_list
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return []


def get_all_dynamic_detect_rule(belong):
    """
     获取dynamic_detect_rule表中所有告警
     belong neflow为1,tclog为2
     """
    try:
        _sql = '''
            SELECT
                id,
                type,
                detect_name,
                threshold,
                status
            FROM internal_app_bsa_gjk.dynamic_detect_rule
            WHERE belong = %s
            ORDER BY type ASC
        '''
        _param = CPgSqlParam(_sql, params=(belong,))
        _res_list = json.loads(CFunction.execute(_param))
        return _res_list
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return []


def handle_alert_data(data, _map):
    def create_alert_entry(item_arr, key, alert_name=None):
        """创建告警条目"""
        entry = {
            "data_source": _map.get(key, {}).get("data_source"),
            "data_category": _map.get(key, {}).get("data_category"),
            "alert_category": _map.get(key, {}).get("alert_category"),
            "detect_type": _map.get(key, {}).get("detect_type"),
            "detect_cycle": _map.get(key, {}).get("detect_cycle"),
            "detect_name": alert_name if alert_name else item_arr[2],
            "threshold": long2unit(item_arr[3]),
            "status": "启用" if item_arr[4] else "停用",
            "type_value": _map.get(key, {}).get("type_value", item_arr[1]),
            "model_principle": _map.get(key, {}).get("model_principle"),
            "_count": [],
        }
        return entry

    # 定义特殊类型的映射
    special_types = {11: 12, 31: 32, 41: 42}
    res_map = defaultdict(dict)

    for item in data:
        # 生成 key
        key_ = "APT_{}".format(item[1]) if "APT" in item[2] and item[1] == 91 else item[1]

        # 添加主告警条目
        res_map[item[2]] = create_alert_entry(item, key_)

        # 处理特殊类型
        if item[1] in special_types:
            mapped_key = special_types[item[1]]
            alert_name_ = _map.get(mapped_key, {}).get("alert_name")
            res_map[alert_name_] = create_alert_entry(item, key_, alert_name_)

    # 按照 type_value 升序排序
    sorted_data = OrderedDict(sorted(res_map.items(), key=lambda i: i[1]['type_value']))
    return sorted_data


def append_to_map(result_map, key1, key2, value):
    result_map.setdefault(key1, {}).setdefault(key2, []).append(value)


def handle_excel_data(_map, result_map):
    """
    构造最后写入excel的数据
    按照数据源、检测周期、数据类别等进行组织，填充到 result_map
    顺序是：
    TCLOG：
        5min
            重点目标
                重要数据服务，物联网，重点单位，数据库，FTP，非常用端口，Web传输，远程登录，恶意IP，云盘云笔记，扫描行为
            7类资产
                数据库，OA，Web邮箱，远程桌面，FTP，视频设备，VPN
        天模型
            重点目标
                流量倍数，重点监控目标，物联网，非常用端口，恶意IP
            7类资产
                数据库，OA，Web邮箱，远程桌面，FTP，视频设备，VPN
    NETFLOW：
        5min
            重点目标
                重点单位，FTP
            全网
                大流量，重要数据服务， 物联网，数据库，非常用端口，Web传输，远程登录，恶意IP，云盘云笔记
            7类资产
                数据库，OA，Web邮箱，远程桌面，FTP，视频设备，VPN
        天模型
            重点目标
                重点监控目标
            全网
                非常用端口，物联网，恶意IP
            7类资产
                数据库，OA，Web邮箱，远程桌面，FTP，视频设备，VPN
    """
    for key, value in _map.items():
        _row = [
            value["type_value"], value["data_source"], value["data_category"], value["alert_category"],
            value["detect_name"], value["model_principle"], value["detect_type"], value["detect_cycle"],
            value["threshold"], value["status"],
        ]
        _row.extend(value["_count"])
        if value["data_source"] == "TCLOG":
            cycle_key = "tllog"
        else:
            cycle_key = "netflow"
        append_to_map(result_map[cycle_key], value["data_category"], value["alert_category"], _row)
    # print(json.dumps(result_map, ensure_ascii=False))
    return result_map


def write_excel(sheet, _map):
    grouped_data = OrderedDict()

    # 遍历数据，将相同告警名称的记录放在一起
    for category, alarms in _map.items():
        for asset_type, records in alarms.items():
            for record in records:
                if int(record[0]) == 91:
                    alarm_name = record[4]  # 告警名称总是位于索引 4
                else:
                    alarm_name = record[0]
                # 如果告警名称还没有添加过，先创建一个空的列表
                if alarm_name not in grouped_data:
                    grouped_data[alarm_name] = []
                # 将记录追加到对应的告警名称的列表中
                grouped_data[alarm_name].append(record)

    # 打印分类后的数据
    for alarm_name, grouped_records in grouped_data.items():
        for record in grouped_records:
            sheet.append(record[1:])


def _set_column_width(sheet):
    # 设置列宽
    columns_to_modify = ['A', 'B', 'C', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S',
                         'T', 'U', 'V', 'W', 'X']
    for column in columns_to_modify:
        sheet.column_dimensions[column].width = 15
    sheet.column_dimensions['D'].width = 60
    sheet.column_dimensions['E'].width = 80


def _apply_common_style(sheet):
    # 设置字体
    font = Font(bold=False, color=colors.BLACK, size=14, name="Microsoft YaHei")

    # 设置对齐方式
    alignment = Alignment(horizontal='center', vertical='center')

    # 设置边框
    border = Border(left=Side(border_style="thin", color="000000"),
                    right=Side(border_style="thin", color="000000"),
                    top=Side(border_style="thin", color="000000"),
                    bottom=Side(border_style="thin", color="000000"))

    # 遍历工作表的所有单元格并设置居中对齐
    for row in sheet.iter_rows():
        for cell in row:
            cell.alignment = alignment
            cell.border = border
            cell.font = font
    # 设置冻结窗格至第二行
    sheet.freeze_panes = 'A3'
    # 设置筛选区域
    sheet.auto_filter.ref = "I1:I" + str(sheet.max_row)


def excel_styles(sheet):
    """
    设置表格格式
    """

    _set_column_width(sheet)
    # 合并前 9 列和最后一列的上下两行
    for col in [1, 2, 3, 4, 5, 6, 7, 8, 9, 24]:
        sheet.merge_cells(start_row=1, start_column=col, end_row=2, end_column=col)
    # 合并7天数据的首行单元格
    columns_to_merge = [(22, 23), (20, 21), (18, 19), (16, 17), (14, 15), (12, 13), (10, 11)]
    # 遍历需要合并的列对
    for col1, col2 in columns_to_merge:
        # 保留第二个列（即col2）单元格的内容
        value = sheet.cell(row=1, column=col2).value
        # 合并指定列的第一行
        sheet.merge_cells(start_row=1, start_column=col1, end_row=1, end_column=col2)
        # 将第二列的内容放回合并后的单元格中
        sheet.cell(row=1, column=col1).value = value

    # 定义绿色填充  绿色（十六进制颜色代码）
    green_fill = PatternFill(start_color="319B62", end_color="319B62", fill_type="solid")

    # 设置第 1 行和第 2 行的填充色
    for row in sheet.iter_rows(min_row=1, max_row=2):  # 遍历第 1 行和第 2 行
        for cell in row:  # 遍历行中的每个单元格
            cell.fill = green_fill

    # 假设第一列的内容从第3行开始（第1行是表头）
    start_row = 3
    # 处理第一列、第二列和第三列的合并
    for col in range(1, 4):  # 第一列到第三列
        current_value = None
        start_merge_row = None
        # 遍历列中的所有行
        for row in range(start_row, sheet.max_row + 1):
            cell = sheet.cell(row=row, column=col)
            # 如果当前单元格的值与前一个单元格不同，则处理合并
            if cell.value != current_value:
                if current_value is not None and start_merge_row != row - 1:
                    # 合并单元格
                    merge_and_center(sheet, start_merge_row, col, row - 1, col)
                # 更新当前值和起始合并行
                current_value = cell.value
                start_merge_row = row
        # 最后一段相同的值也需要合并
        if start_merge_row is not None:
            merge_and_center(sheet, start_merge_row, col, sheet.max_row, col)

    _apply_common_style(sheet)


def merge_and_center(sheet, start_row, start_col, end_row, end_col):
    # 合并单元格
    sheet.merge_cells(start_row=start_row, start_column=start_col, end_row=end_row, end_column=end_col)


def run():
    TCLOG_MODEL_PRINCIPLE, DAY_TCLOG_MODEL_PRINCIPLE, NETFLOW_MODEL_PRINCIPLE, DAY_NETFLOW_MODEL_PRINCIPLE = get_alert_template_data()
    current_time = datetime.datetime.now()
    workbook = openpyxl.Workbook()
    # 删除默认创建的空白工作表
    workbook.remove(workbook.active)

    sheet = workbook.create_sheet(title=u"模型和事件量")

    one_row = ["数据来源", "数据分类", "告警大类", "告警类型", "模型原理", "检测类型", "检测周期", "阈值",
               "启用状态"]
    second_row = ["", "", "", "", "", "", "", "", "", "告警数量", "事件数量", "告警数量", "事件数量", "告警数量",
                  "事件数量", "告警数量", "事件数量", "告警数量", "事件数量", "告警数量", "事件数量", "告警数量",
                  "事件数量", ""]
    _res_list = get_all_detect_rule(1)
    day_res_list = get_all_dynamic_detect_rule(1)
    netflow_map = handle_alert_data(_res_list, NETFLOW_MODEL_PRINCIPLE)
    # netflow 天模型的
    day_netflow_map = handle_alert_data(day_res_list, DAY_NETFLOW_MODEL_PRINCIPLE)
    tllog_res_list = get_all_detect_rule(2)
    day_tllog_res_list = get_all_dynamic_detect_rule(2)
    tllog_map = handle_alert_data(tllog_res_list, TCLOG_MODEL_PRINCIPLE)
    # 通联 天模型的
    day_tllog_map = handle_alert_data(day_tllog_res_list, DAY_TCLOG_MODEL_PRINCIPLE)
    s = time.time()
    for i in range(7, 0, -1):
        start_time = (current_time - datetime.timedelta(days=i)).replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = (current_time - datetime.timedelta(days=i)).replace(hour=23, minute=59, second=59, microsecond=0)
        st = datetime_to_timestamp(start_time)
        et = datetime_to_timestamp(end_time)
        key = start_time.strftime("%Y/%m/%d")
        one_row.extend(["", key])
        mlog.info("==========================通联日志==========================：")
        tllog_event_res = merge_tllog(1, st, et, 1, 2)
        dispose_data(tllog_map, tllog_event_res)
        mlog.info("==========================通联日志天模型告警==========================：")
        day_tllog_event_res = merge_tllog(1, st, et, 2, 2)
        dispose_data(day_tllog_map, day_tllog_event_res)

        mlog.info("==========================netflow==========================：")
        netflow_event_res = merge_tllog(2, st, et, 1, 1)
        dispose_data(netflow_map, netflow_event_res)
        mlog.info("==========================netflow天模型告警==========================：")
        day_netflow_event_res = merge_tllog(2, st, et, 2, 1)
        dispose_data(day_netflow_map, day_netflow_event_res)
    mlog.info("==========================sql查询时间{}==========================".format(time.time() - s))
    one_row.append("分析情况")
    sheet.append(one_row)
    sheet.append(second_row)

    result_map = {"tllog": {}, "netflow": {}}
    # 构造通联日志数据
    handle_excel_data(tllog_map, result_map)
    handle_excel_data(day_tllog_map, result_map)
    # 构造netflow数据
    handle_excel_data(netflow_map, result_map)
    handle_excel_data(day_netflow_map, result_map)
    # 写入sheet
    write_excel(sheet, result_map["tllog"])
    write_excel(sheet, result_map["netflow"])
    # 设置样式
    excel_styles(sheet)
    output_file = '七天事件与告警各类型数量统计.xlsx'
    # 保存工作簿到指定文件路径
    workbook.save(output_file)


if __name__ == '__main__':
    run()
