#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    项目包打包脚本
"""

import json
import argparse
import os
import shutil
import sys
import time
import traceback
import zipfile
import threading

from encrypt_tool.encrypt_app import CryptApp

default_encoding = 'utf-8'
if sys.getdefaultencoding() != default_encoding:
    reload(sys)
    sys.setdefaultencoding(default_encoding)

_APP_NAME = "cncert_kj"

VERSION = 'V1.0R00F01'


_CUR_PATH = os.path.dirname(os.path.realpath(__file__))

# 源码工作空间根目录
_WORKSPACE_PATH = os.path.dirname(os.path.dirname(_CUR_PATH))

_BACKEND_PATH = os.path.join(_WORKSPACE_PATH, _APP_NAME)

_MAIN_PATH = os.path.join(_WORKSPACE_PATH, "cncert_kj_frontend")


_WORK_PATH = os.path.normpath(os.path.join("/tmp", _APP_NAME + str(int(time.time() * 1000))))

# 配置不需要加密的文件/文件夹
PYC_NOT_ENCRYPT_LIST = {
    'files': [
        os.path.join(_WORK_PATH, _APP_NAME, 'install.py'),
    ],
    'paths': [
        os.path.join(_WORK_PATH, _APP_NAME, 'target'),
        os.path.join(_WORK_PATH, _APP_NAME, 'static'),
        os.path.join(_WORK_PATH, _APP_NAME, 'bin'),
        os.path.join(_WORK_PATH, _APP_NAME, 'models'),
    ],
}


class Package(object):
    """
    打包逻辑
    """

    REPOSITORY_ = {
        "name": "s-sdc-1-raw",
        "password": "67c3ad90d6032cc1e3aaa2532ebd03f0",
        "standard_dir": "PDC-BZP202401230003-CNCERT/kj_flow_detect",
        "address": "https://idun-wh.inone.nsfocus.com/ia/artifact/raw"
    }

    def __init__(self, branch, customer):
        self.work_target_path = os.path.join(_WORK_PATH, _APP_NAME)
        self.package_name = None
        self.package_version = VERSION
        self.version = time.strftime("%y%m%d%H%M%S")
        self.branch = branch
        self.customer = customer
        self.repository = None

    def run(self):
        """
        执行打包逻辑
        """
        start_time = int(time.time())
        # 创建工作空间
        os.makedirs(_WORK_PATH)
        try:
            self.hand_backend()
            self.hand_frontend()
            self.gen_project_package()
        except Exception:
            self._error("***********打包异常，请排查***********")
            self._error(traceback.format_exc())
            sys.exit(1)
        finally:
            # 删除工作空间
            shutil.rmtree(_WORK_PATH)
        print("打包结束，共耗时%d" % (int(time.time()) - start_time))

    def hand_backend(self):
        """
        处理后端
        """
        branch = self.branch if self.branch else "main"
        cmd = "cd {} && git checkout {} && git pull origin {}".format(_WORKSPACE_PATH, branch, branch)
        self._execute_cmd(cmd)

        cmd = "cp {} {} -R".format(_BACKEND_PATH, _WORK_PATH)
        self._execute_cmd(cmd)

        os.path.normpath(os.path.join(self.work_target_path, "target", "encrypt_tool", "pyprotect.py"))
        # >>
        # encrypt_py = os.path.normpath(os.path.join(self.work_target_path, "target", "encrypt_tool", "pyprotect.py"))
        # for root, dirs, files in os.walk(self.work_target_path):
        #     if root in PYC_NOT_ENCRYPT_LIST['paths']:
        #         continue
        #     for item in files:
        #         py_path = os.path.normpath(os.path.join(root, item))
        #         if not item.endswith('.py'):
        #             continue
        #         if py_path in PYC_NOT_ENCRYPT_LIST['files']:
        #             continue
        #         cmd = "python '%s' '%s'" % (encrypt_py, py_path)
        #         os.system(cmd)
        #         os.remove(py_path)
        print("完成加密")

    def hand_frontend(self):
        """
        处理前端
        """
        branch = self.branch if self.branch else "main"
        cmd = "cd {} && git checkout {} && git pull origin {}".format(_MAIN_PATH, branch, branch)
        self._execute_cmd(cmd)
        cmd = "cp {} {} -R".format(_MAIN_PATH, _WORK_PATH)
        self._execute_cmd(cmd)

        work_frontend_path = os.path.join(_WORK_PATH, "cncert_kj_frontend")
        work_backend_path = os.path.join(_WORK_PATH, _APP_NAME)

        self._execute_cmd('cd %s && npm run build' % work_frontend_path)

        self._execute_cmd(' cp -r {} {} '.format(os.path.join(work_frontend_path, "static"), os.path.join(work_backend_path)))

    def gen_project_package(self):
        """
        生成安装包
        逻辑：
            1.获取项目安装包名称
            2.更新package.json文件，写入版本信息
            3.更新案例升级配置文件,判断是否是案例升级包
            4.打包加密成dat文件
            5.上传制品库2
        """
        self._get_package_name()
        self._update_package_info()
        self._run_encrypt()
        self._upload_repository()

    def _run_encrypt(self):
        """
        将编译后的目录压缩并加密成dat
        """
        os.chdir(_WORK_PATH)
        install_py = "install.pyc" if os.path.exists(os.path.join(_WORK_PATH, "install.pyc")) else "install.py"
        if not os.path.exists(os.path.join(_WORK_PATH, install_py)):
            cmd = "mv %s/%s ./" % (_APP_NAME, install_py)
            self._execute_cmd(cmd)

        tar_name = self.package_name + ".tar.gz"
        cmd = "tar -zcf %s %s %s --exclude=.git  --exclude=%s/target > /dev/null" % (
            tar_name, _APP_NAME, install_py, _APP_NAME)
        print("压缩命令：%s" % cmd)
        self._execute_cmd(cmd)

        print("修改为dat包，并加密")
        tar_name = self.package_name + ".tar.gz"
        output_name = self.package_name + ".dat"
        do_encrypt = CryptApp(tar_name, output_name)
        do_encrypt.encrypt_app()
        self._execute_cmd("mv %s %s -f" % (output_name, _CUR_PATH))
        self.output_name = output_name

    def _get_package_name(self):
        """
        获取包名称
        """
        self.package_name = _APP_NAME + "." + "C" + "." + self.customer + "." + self.package_version + "." + self.version
        print("包名称:%s" % self.package_name)
        return self.package_name

    def _update_package_info(self):
        """
        更新package.json，写入组件类型
        """
        package_info_path = os.path.join(self.work_target_path, "package.json")
        with open(package_info_path, "r+") as f:
            package_info = json.load(f)
            package_info['version'] = self.package_version + "." + self.version

        with open(package_info_path, "w+") as f:
            json.dump(package_info, f, ensure_ascii=False, indent=4)

        build_version_path = os.path.join(self.work_target_path, 'conf', 'buildversion')
        with open(build_version_path, 'w+') as f:
            f.write(self.version)

    def _upload_repository(self):
        """
        上传到制品库
        """
        if self.repository and isinstance(self.repository, str):
            self.REPOSITORY_['name'], self.REPOSITORY_['password'], self.REPOSITORY_['address'] = self.repository.split(
                ",")
        cmd = '''curl -v -u {}:{} --upload-file {} "{}/{}/{}/{}"'''.format(self.REPOSITORY_['name'],
                                                                           self.REPOSITORY_['password'],
                                                                           os.path.join(_CUR_PATH, self.output_name),
                                                                           self.REPOSITORY_['address'],
                                                                           self.REPOSITORY_['name'],
                                                                           self.REPOSITORY_['standard_dir'],
                                                                           self.output_name)
        print("上传至制品库：{}".format(cmd))
        self._execute_cmd(cmd)

    def _execute_cmd(self, cmd):
        """
        该函数的返回值是一个整数，表示命令的退出状态。通常，如果命令执行成功，则返回值为 0；如果命令执行失败，则返回非零值
        若失败，抛出异常，结束打包
        """
        ret = os.system(cmd)
        if ret == 0:
            self._info('cmd:{}, ret :{}'.format(cmd, ret))
        else:
            self._error('cmd:{}, ret:{}'.format(cmd, ret))
            raise ValueError('cmd:{}, ret :{}'.format(cmd, ret))
        return ret

    @staticmethod
    def _info(content):
        """
        信息
        """
        print("\033[92m{}\033[0m".format(content))

    @staticmethod
    def _warn(content):
        """
        告警
        """
        print("\033[93m{}\033[0m".format(content))

    @staticmethod
    def _error(content):
        """
        错误
        """
        print("\033[91m{}\033[0m".format(content))

    @staticmethod
    def _unzip_file(case_zip_path, import_path):
        unzip_file_obj = zipfile.ZipFile(case_zip_path)
        unzip_file_obj.extractall(path=import_path)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="打包脚本")
    parser.add_argument("--branch", default="main", help="代码分支")
    parser.add_argument("--customer", default="CNCERT_KJ", help="客户方")

    args = parser.parse_args()
    print("接收参数：", args)
    obj = Package(args.branch, args.customer)
    obj.run()

