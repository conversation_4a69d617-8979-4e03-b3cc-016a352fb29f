#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@author:
@file:   iot_ddos_judge.py
@time:
@desc:  物联网DDOS事件自动研判--需要查询原始日志，效率太慢，所以只有静态基线查询原始日志，动态基线直接匹配端口
    1. 静态基线事件：
        1. 从配置表中获取上次处理时间
        2. 根据上次处理时间，查询物联网事件类型
        3. 根据物联网事件类型，查询目标事件
        4. 遍历目标事件，！！查询原始日志！！，判断是否包含UDP协议
        5. 如果包含UDP协议，则更新事件状态为7，自动研判未通报，研判信息为“疑似ddos反射攻击”
    2. 动态基线事件：
        ...根据端口查询事件
        4. 遍历目标事件，更新事件状态为7，研判信息为“疑似ddos反射攻击”
"""
import os
import sys
import time
import traceback
from datetime import datetime

import django

# 设置Django环境
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from django.db.models import Q

from cncert_kj.models.base_model import ContinuousEventsConfModel, ContinuousEvents, TypeConfModel
from cncert_kj.utils import logger, conf_util
from cncert_kj.views.cont_event import get_port_traffic

# 初始化日志
mlog = logger.init_logger('auto_judge_iot_ddos')

# 设置默认编码
reload(sys)
sys.setdefaultencoding('utf-8')

# 配置常量
TCLOG_CONF_KEY = "tclog_iot_ddos_judge_timestamp"
NETFLOW_CONF_KEY = "netflow_iot_ddos_judge_timestamp"
TARGET_PORTS = ["123", "1900", "37020", "3702"]
JUDGE_STATUS = 7  # 自动研判未通报
JUDGE_INFO_APPEND = "疑似ddos反射攻击"
REPORT_TYPE_MAP = {
    1: "tclog",
    2: "netflow"
}


class IotDdosJudge:
    """物联网DDOS事件自动研判"""

    def __init__(self, report_type, start_time=0, end_time=0, analysis_tech=1):
        self.conf_model = ContinuousEventsConfModel
        self.common_conf = conf_util.CommonConf()
        self.report_type = report_type
        self.start_time = start_time
        self.end_time = end_time
        self.conf_key = self.get_conf_key()
        self.analysis_tech = analysis_tech
        self.last_timestamp = self.get_last_time()

    def get_conf_key(self):
        """获取配置键"""
        conf_key_map = {
            1: TCLOG_CONF_KEY,
            2: NETFLOW_CONF_KEY
        }

        conf_key = conf_key_map.get(self.report_type)
        return conf_key

    def get_last_time(self):
        """获取上次处理时间"""
        _conf_value = int(time.time()) - 60 * 60 * 24 * 3
        try:
            conf = self.conf_model.objects.get(conf_key=self.conf_key)
            return int(conf.conf_value)
        except Exception:
            # >> 初试时间为三天前
            # 不存在则创建初始记录
            mlog.info("第一次运行，初始时间为三天前：{}".format(_conf_value))
            self.conf_model.objects.create(
                conf_key=self.conf_key,
                conf_value=_conf_value
            )
            return _conf_value

    def update_last_time(self, new_time):
        """更新处理时间"""
        try:
            self.conf_model.objects.filter(
                conf_key=self.conf_key
            ).update(conf_value=int(new_time))
        except Exception as e:
            mlog.error("更新处理时间失败: %s\n%s", str(e), traceback.format_exc())

    def get_iot_event_types(self):
        """获取物联网事件类型"""
        try:
            event_types = TypeConfModel.objects.filter(type_name__contains='物联网').values_list('type_value', flat=True)
            mlog.info("物联网事件类型: {}".format(event_types))
            # 需要的下载类型的，类型值为偶数
            event_types_list = [int(x) for x in set(event_types) if int(x) % 2 == 0]
            return event_types_list
        except Exception as e:
            mlog.error("获取物联网事件类型失败: %s\n%s", str(e), traceback.format_exc())
            return []

    def get_target_events(self, event_types):
        """获取目标事件"""
        try:
            # 获取时间范围
            if self.start_time and self.end_time:
                start_time = self.start_time
                end_time = self.end_time
            else:
                start_time = self.last_timestamp
                end_time = int(time.time())

            mlog.info("查询时间范围: {} - {}".format(
                datetime.fromtimestamp(start_time).strftime(DATE_TIME_FORMAT),
                datetime.fromtimestamp(end_time).strftime(DATE_TIME_FORMAT)
            ))

            # 构建端口查询条件
            port_conditions = Q()
            for port in TARGET_PORTS:
                port_conditions |= Q(src_port__contains=',{},'.format(port))

            res = ContinuousEvents.objects.filter(
                end_time__gt=start_time,
                end_time__lte=end_time,
                event_type__in=event_types,
                report_type=self.report_type,
                analysis_tech=self.analysis_tech,
                judge_status=1  # 未研判
            ).filter(port_conditions).values(
                'event_id', 'end_time', 'related_alerts',
                'report_type', 'judge_info'
            ).order_by('end_time')
            # 打印语句
            mlog.info("查询语句：{}".format(res.query))
            return res
        except Exception as e:
            mlog.error("获取目标事件失败: %s\n%s", str(e), traceback.format_exc())
            return []

    def _update_event_as_ddos(self, event):
        """将事件更新为DDOS事件"""
        try:
            # 更新事件状态
            judge_info = event['judge_info'] or ""
            if judge_info and not judge_info.endswith('\n'):
                judge_info += '\n'
            judge_info += JUDGE_INFO_APPEND

            ContinuousEvents.objects.filter(
                event_id=event['event_id']
            ).update(
                judge_status=JUDGE_STATUS,
                judge_info=judge_info
            )
            if self.analysis_tech == 2:
                analysis_tech_tag = "动态基线事件"
            else:
                analysis_tech_tag = "静态基线事件"
            mlog.info("{}:{} 更新为DDOS事件".format(analysis_tech_tag, event['event_id']))
            return True
        except Exception as e:
            mlog.error("更新事件 %d 失败: %s\n%s",
                       event['event_id'], str(e), traceback.format_exc())
            return False

    def _judge_dynamic_events(self, event_types):
        """处理动态基线事件"""
        mlog.info("--动态基线事件处理开始")

        events = self.get_target_events(event_types)
        if not events:
            mlog.info("未获取到需要处理的动态基线事件")
        else:
            success_count = 0
            for event in events:
                if self._update_event_as_ddos(event):
                    success_count += 1

            mlog.info("--动态基线事件处理完成，共处理 %d 个事件", success_count)
        return True

    def _judge_static_events(self, event_types):
        """处理静态基线事件"""
        # 获取目标事件
        events = self.get_target_events(event_types)
        if not events:
            mlog.info("未获取到需要处理的事件")
            return
        mlog.info("获取到 %d 条待处理事件", len(events))

        # 处理事件
        max_end_time = self.last_timestamp
        n = 0
        # with transaction.atomic():
        for event in events:
            try:
                n += 1
                mlog.info("处理第 %d 事件，事件id： %d", n, event['event_id'])
                # 获取协议信息
                alert_ids = event['related_alerts'].split(';')
                protocols = get_port_traffic(
                    alert_ids=alert_ids,
                    report_type=int(event['report_type'])
                ).get('protocol', "").split(',')
                mlog.info("事件 %d 协议信息: %s", event['event_id'], protocols)

                # 判断是否包含UDP协议
                if any(p.lower() == 'udp' for p in protocols):
                    # 更新事件状态
                    judge_info = event['judge_info'] or ""
                    if judge_info and not judge_info.endswith('\n'):
                        judge_info += '\n'
                    judge_info += JUDGE_INFO_APPEND

                    ContinuousEvents.objects.filter(
                        event_id=event['event_id']
                    ).update(
                        judge_status=JUDGE_STATUS,
                        judge_info=judge_info
                    )
                    mlog.info("事件 %d 更新为DDOS事件", event['event_id'])

                # 记录最大结束时间
                max_end_time = max(max_end_time, event['end_time'])

            except Exception as e:
                mlog.error("处理事件 %d 失败: %s\n%s",
                           event['event_id'], str(e), traceback.format_exc())
                continue

        # 更新处理时间
        if max_end_time > self.last_timestamp:
            self.update_last_time(max_end_time)
            mlog.info("更新处理时间为: %s", max_end_time)
        return True

    def judge_events(self):
        """执行事件研判"""
        try:
            # 获取物联网事件类型
            event_types = self.get_iot_event_types()
            if not event_types:
                mlog.error("未获取到物联网事件下载类型")
                return
            mlog.info("物联网事件下载类型: %s", event_types)

            # 动态基线事件处理
            if self.analysis_tech == 2:
                return self._judge_dynamic_events(event_types)
            # 静态基线事件处理
            else:
                return self._judge_static_events(event_types)
        except Exception as e:
            mlog.error("执行事件研判失败: %s\n%s", str(e), traceback.format_exc())
            return False

    def _judge_events(self):
        """执行事件研判"""
        try:
            # 获取物联网事件类型
            event_types = self.get_iot_event_types()
            if not event_types:
                mlog.error("未获取到物联网事件下载类型")
                return
            mlog.info("物联网事件下载类型: %s", event_types)

            # 动态基线事件
            if self.analysis_tech == 2:
                mlog.info("--动态基线事件处理开始")

                for event in self.get_target_events(event_types):
                    # 更新事件状态
                    judge_info = event['judge_info'] or ""
                    if judge_info and not judge_info.endswith('\n'):
                        judge_info += '\n'
                    judge_info += JUDGE_INFO_APPEND

                    ContinuousEvents.objects.filter(
                        event_id=event['event_id']
                    ).update(
                        judge_status=JUDGE_STATUS,
                        judge_info=judge_info
                    )
                    mlog.info("动态基线事件 %d 更新为DDOS事件", event['event_id'])
                mlog.info("--动态基线事件处理完成")
                return
            # 静态基线事件
            else:
                # 获取目标事件
                events = self.get_target_events(event_types)
                if not events:
                    mlog.info("未获取到需要处理的事件")
                    return
                mlog.info("获取到 %d 条待处理事件", len(events))

                # 处理事件
                max_end_time = self.last_timestamp
                n = 0
                # with transaction.atomic():
                for event in events:
                    try:
                        n += 1
                        mlog.info("处理第 %d 事件，事件id： %d", n, event['event_id'])
                        # 获取协议信息
                        alert_ids = event['related_alerts'].split(';')
                        protocols = get_port_traffic(
                            alert_ids=alert_ids,
                            report_type=int(event['report_type'])
                        ).get('protocol', "").split(',')
                        mlog.info("事件 %d 协议信息: %s", event['event_id'], protocols)

                        # 判断是否包含UDP协议
                        if any(p.lower() == 'udp' for p in protocols):
                            # 更新事件状态
                            judge_info = event['judge_info'] or ""
                            if judge_info and not judge_info.endswith('\n'):
                                judge_info += '\n'
                            judge_info += JUDGE_INFO_APPEND

                            ContinuousEvents.objects.filter(
                                event_id=event['event_id']
                            ).update(
                                judge_status=JUDGE_STATUS,
                                judge_info=judge_info
                            )
                            mlog.info("事件 %d 更新为DDOS事件", event['event_id'])

                        # 记录最大结束时间
                        max_end_time = max(max_end_time, event['end_time'])

                    except Exception as e:
                        mlog.error("处理事件 %d 失败: %s\n%s",
                                   event['event_id'], str(e), traceback.format_exc())
                        continue

                # 更新处理时间
                if max_end_time > self.last_timestamp:
                    self.update_last_time(max_end_time)
                    mlog.info("更新处理时间为: %s", max_end_time)

        except Exception as e:
            mlog.error("执行事件研判失败: %s\n%s", str(e), traceback.format_exc())


if __name__ == '__main__':
    import argparse

    # 创建参数解析器
    parser = argparse.ArgumentParser(description='物联网DDOS事件自动研判工具')
    parser.add_argument('report_type', type=int, choices=[1, 2], help='报告类型: 1=tclog, 2=netflow')
    parser.add_argument('--start', '-s', type=int, help='开始时间，时间戳')
    parser.add_argument('--end', '-e', type=int, help='结束时间，时间戳')
    parser.add_argument('--analysis_tech', '-a', type=int, choices=[1, 2], default=1,
                        help='动静态基线：1=静态基线，2=动态基线 (默认: 1)')

    # 解析命令行参数
    try:
        args = parser.parse_args()

        mlog.info("----执行研判脚本,研判{}...".format(REPORT_TYPE_MAP.get(args.report_type, "")))
        if args.start and args.end:
            mlog.info("指定时间范围处理，开始时间: {}, 结束时间: {}".format(
                datetime.fromtimestamp(args.start).strftime(DATE_TIME_FORMAT),
                datetime.fromtimestamp(args.end).strftime(DATE_TIME_FORMAT)
            ))
        else:
            mlog.info("未指定时间范围，使用默认时间范围")
        judge = IotDdosJudge(
            report_type=args.report_type,
            start_time=args.start,
            end_time=args.end,
            analysis_tech=args.analysis_tech
        )
        judge.judge_events()

        mlog.info("----自动研判完成")

    except Exception as e:
        mlog.exception("参数处理异常: %s", str(e))
        exit(1)
