# !/usr/bin/env python
# coding:utf-8
"""
NAME    : crontab_generate_overview_script.py
Author  : YuQ
Contact : <EMAIL>
Time    : 2024/4/30 14:05
"""
import json
import sys
import time

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.utils import logger
from cncert_kj.utils.overview_utils import get_max_key_unit_outflow_tllog, get_max_ip_outflow_tllog, \
    total_flow_tllog, \
    get_department_detail_tllog, get_key_unit_detail_tllog, get_ip_detail_tllog, total_flow_netflow, \
    get_max_ip_outflow_netflow, get_max_key_unit_outflow_netflow, get_department_detail_netflow, \
    get_key_unit_detail_netflow, get_ip_detail_netflow

reload(sys)
sys.setdefaultencoding('utf-8')

mlog = logger.init_logger('crontab_generate_overview_script')


def create_overview_data(overview_name, json_data):
    select_sql = """
        SELECT overview_name FROM internal_app_bsa_gjk.overview_data WHERE overview_name=%s;
        """
    res = json.loads(CFunction.execute(CPgSqlParam(select_sql, params=(overview_name,))))
    create_time = update_time = int(time.time())
    if len(res) and res[0][0] > 0:
        update_sql = """
        UPDATE internal_app_bsa_gjk.overview_data 
        SET json_data=%s, create_time=%s, update_time=%s
        WHERE overview_name=%s
        """
        blacklist_param = CPgSqlParam(update_sql, params=(str(json_data), create_time, update_time, overview_name))
        CFunction.execute(blacklist_param)
    else:
        insert_sql = '''
        INSERT INTO internal_app_bsa_gjk.overview_data (overview_name, json_data, create_time, update_time)
        VALUES
            (%s,%s,%s,%s)
    '''
        blacklist_param = CPgSqlParam(insert_sql, params=(overview_name, str(json_data), create_time, update_time))
        CFunction.execute(blacklist_param)


def execute_overview_tllog(asstype_value):
    ip_name = "_ipv4" if asstype_value == 1 else "_ipv6"

    # >> 通联日志重点监控 / 重点监控概览
    # 单位总流量概览
    mlog.info("-----------------------通联日志重点监控 / 重点监控概览------------------------")
    start_time = time.time()
    total_flow = total_flow_tllog(asstype_value)
    create_overview_data("total_flow_tllog" + ip_name, total_flow)
    mlog.info("total_flow_tllog,单位总流量概览,耗时:{}".format(time.time() - start_time))

    # 最大流出单位监控
    start_time = time.time()
    get_max_key_unit_outflow = get_max_key_unit_outflow_tllog(asstype_value)
    create_overview_data("get_max_key_unit_outflow_tllog" + ip_name, get_max_key_unit_outflow)
    mlog.info("get_max_key_unit_outflow_tllog,最大流出单位监控,耗时:{}".format(time.time() - start_time))

    # 最大流出IP监控
    start_time = time.time()
    get_max_ip_outflow = get_max_ip_outflow_tllog(asstype_value)
    create_overview_data("get_max_ip_outflow_tllog" + ip_name, get_max_ip_outflow)
    mlog.info("get_max_ip_outflow_tllog,最大流出IP监控,耗时:{}".format(time.time() - start_time))

    # 行业流量监控概览
    start_time = time.time()
    get_department_detail = get_department_detail_tllog(asstype_value)
    create_overview_data("get_department_detail_tllog" + ip_name, get_department_detail)
    mlog.info("get_department_detail_tllog,行业流量监控概览,耗时:{}".format(time.time() - start_time))

    # 单位流量监控概览
    start_time = time.time()
    get_key_unit_detail = get_key_unit_detail_tllog(asstype_value)
    create_overview_data("get_key_unit_detail_tllog" + ip_name, get_key_unit_detail)
    mlog.info("get_key_unit_detail_tllog,单位流量监控概览,耗时:{}".format(time.time() - start_time))

    # IP流量监控概览
    start_time = time.time()
    get_ip_detail = get_ip_detail_tllog(asstype_value)
    create_overview_data("get_ip_detail_tllog" + ip_name, get_ip_detail)
    mlog.info("get_ip_detail_tllog,IP流量监控概览,耗时:{}".format(time.time() - start_time))


def execute_overview_netflow(asstype_value):
    ip_name = "_ipv4" if asstype_value == 1 else "_ipv6"

    # >> NetFlow重点监控 / 重点监控概览
    # 单位总流量概览
    mlog.info("-----------------------NetFlow重点监控 / 重点监控概览------------------------")
    start_time = time.time()
    total_flow = total_flow_netflow(asstype_value)
    create_overview_data("total_flow_netflow" + ip_name, total_flow)
    mlog.info("total_flow_netflow,单位总流量概览,耗时:{}".format(time.time() - start_time))

    # 最大流出IP监控
    start_time = time.time()
    get_max_ip_outflow = get_max_ip_outflow_netflow(asstype_value)
    create_overview_data("get_max_ip_outflow_netflow" + ip_name, get_max_ip_outflow)
    mlog.info("get_max_ip_outflow_netflow,最大流出IP监控,耗时:{}".format(time.time() - start_time))

    # 最大流出单位监控
    start_time = time.time()
    get_max_key_unit_outflow = get_max_key_unit_outflow_netflow(asstype_value)
    create_overview_data("get_max_key_unit_outflow_netflow" + ip_name, get_max_key_unit_outflow)
    mlog.info("get_max_key_unit_outflow_netflow,最大流出单位监控,耗时:{}".format(time.time() - start_time))

    # 行业流量监控概览
    start_time = time.time()
    get_department_detail = get_department_detail_netflow(asstype_value)
    create_overview_data("get_department_detail_netflow" + ip_name, get_department_detail)
    mlog.info("get_department_detail_netflow,行业流量监控概览,耗时:{}".format(time.time() - start_time))

    # 单位流量监控概览
    start_time = time.time()
    get_key_unit_detail = get_key_unit_detail_netflow(asstype_value)
    create_overview_data("get_key_unit_detail_netflow" + ip_name, get_key_unit_detail)
    mlog.info("get_key_unit_detail_netflow,单位流量监控概览,耗时:{}".format(time.time() - start_time))

    # IP流量监控概览
    start_time = time.time()
    get_ip_detail = get_ip_detail_netflow(asstype_value)
    create_overview_data("get_ip_detail_netflow" + ip_name, get_ip_detail)
    mlog.info("get_ip_detail_netflow,IP流量监控概览,耗时:{}".format(time.time() - start_time))


if __name__ == '__main__':
    s_time = time.time()
    execute_overview_tllog(1)
    execute_overview_tllog(2)
    execute_overview_netflow(1)
    execute_overview_netflow(2)
    mlog.info("total_time,总耗时:{}".format(time.time() - s_time))
