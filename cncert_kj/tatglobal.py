# coding:utf-8
'''
溯源常量及公共接口模块
'''

import logging
import logging.handlers
import os

from appsUtils import env
from mlogging import TimedRotatingFileHandler_MP

APPNAME = "cncert_kj"
# 用于判断异常流量APP是否存在时需要用ATAAPPNAME
DBNAME = "internal_app_cncert_jgk"
APP_HOME = env.get_bsa_root() + "/apps/" + APPNAME

severity = {
    1: u"低危",
    2: u"中危",
    3: u"高危"
}

ERROR_CODE_LIST = {
    10000: u'成功。',
    10001: u'内部错误。',
    10002: u'查询数据为空。',
    10003: u'错误的请求。',
    10004: u'IP地址输入不正确',
    10005: u'参数错误'

}

eventType = {
    1: 'SYN FLOOD',
    2: 'ACK FLOOD',
    3: 'UDP FLOOD',
    4: 'ICMP FLOOD',
    5: 'IGMP FLOOD',
    6: 'PROTOCOL NULL FLOOD',
    7: u'TCP Flag 误用',
    8: 'TCP Flag NULL',
    9: 'HTTP FLOOD',
    10: 'HTTPS FLOOD',
    11: 'DNS REQUEST FLOOD',
    12: 'DNS RESPONSE FLOOD',
    13: 'LAND FLOOD',
    14: 'SIP FLOOD',
    15: u'DARK IP 异常',
    16: u'PRIVATE IP 异常',
    17: 'TRAFFIC ABNORMAL',
    19: 'CUSTOMER INBOUND TRAFFIC ABNORMAL',
    20: 'CUSTOMER OUTBOUND TRAFFIC ABNORMAL',
    21: 'SERVICE INBOUND TRAFFIC ABNORMAL',
    22: 'SERVICE OUTBOUND TRAFFIC ABNORMAL',
    23: 'NTP REFLECTION FLOOD',
    24: 'SSDP REFLECTION FLOOD',
    25: 'SNMP REFLECTION FLOOD',
    26: 'CHARGEN REFLECTION FLOOD'
}

CUSTOM_EVENT_START = 129
CUSTOM_EVENT_TYPE = u"自定义攻击类型"

FORMAT_CSV = "csv"
FORMAT_EXCEL = "excel"
FORMAT_PDF = "pdf"

EXPORT_FORMAT = [FORMAT_CSV, FORMAT_EXCEL, FORMAT_PDF]


def get_logger(logfile="bsa_tat"):
    '''获取日志句柄的方法'''
    logger = logging.getLogger(logfile)
    logger.setLevel(logging.DEBUG)
    logroot = env.get_bsa_root() + "/apps/" + APPNAME + "/logs"
    if not os.path.exists(logroot):
        os.mkdir(logroot)
    filehandle = TimedRotatingFileHandler_MP(os.path.normpath(logroot + "/" + \
                                                              logfile + ".log"), 'midnight')
    filehandle.suffix = "%Y-%m-%d"
    filehandle.setLevel(logging.DEBUG)
    consolehandle = logging.StreamHandler()
    consolehandle.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    filehandle.setFormatter(formatter)
    consolehandle.setFormatter(formatter)
    logger.addHandler(filehandle)
    logger.addHandler(consolehandle)
    return logger


# Define a default logger banding to bsa_tat.log in the log path of tat app.
DEFAULT_LOGGER = get_logger()
TRACE_LOGGER = get_logger("bsa_tat_trace")


def format_bps_or_pps(inputnum):
    inputnum = float(inputnum)
    if inputnum < 1000:
        inputnum = "%.2f" % (round(inputnum, 2))
    elif inputnum < 1000 * 1000:
        inputnum = "%.2fK" % (round(inputnum / 1000, 2),)
    elif inputnum < 1000 * 1000 * 1000:
        inputnum = "%.2fM" % (round(inputnum / 1000 / 1000, 2))
    elif inputnum < 1000 * 1000 * 1000 * 1000:
        inputnum = "%.2fG" % (round(inputnum / 1000 / 1000 / 1000, 2))
    elif inputnum < 1000 * 1000 * 1000 * 1000 * 1000:
        inputnum = "%.2fT" % (round(inputnum / 1000 / 1000 / 1000 / 1000, 2))
    elif inputnum < 1000 * 1000 * 1000 * 1000 * 1000 * 1000:
        inputnum = "%.2fP" % (round(inputnum / 1000 / 1000 / 1000 / 1000 / 1000, 2))
    elif inputnum < 1000 * 1000 * 1000 * 1000 * 1000 * 1000 * 1000:
        inputnum = "%.2fE" % (round(inputnum / 1000 / 1000 / 1000 / 1000 / 1000 / 1000, 2))
    elif inputnum < 1000 * 1000 * 1000 * 1000 * 1000 * 1000 * 1000 * 1000:
        inputnum = "%.2fZ" % (round(inputnum / 1000 / 1000 / 1000 / 1000 / 1000 / 1000 / 1000, 2))
    else:
        inputnum = "%.2fY" % (round(inputnum / 1000 / 1000 / 1000 / 1000 / 1000 / 1000 / 1000 / 1000, 2))
    return inputnum


DDOSTYPE = {
    "1": " and tcpflag = 2 and protocol = 6 ",  # SYN
    "2": " and tcpflag = 16 and protocol = 6 ",  # ACK
    "3": " and protocol = 17 and sport not in (53, 123, 167, 1900, 161, 19) ",  # UDP
    "4": " and protocol in (1, 58) ",  # ICMP
    "5": " and protocol = 2 ",  # IGMP
    "6": " and protocol = 0 ",  # PROTOCOL NULL
    "7": " and tcpflag in (1,3,5,6,7,9,11,15,33,37,41,45,48,63) and protocol = 6 ",  # TCPFLAG MISUSE
    "8": " and tcpflag = 0 and protocol = 6 ",  # TCPFLAG NULL
    "9": " and tcpflag = 24 and  dport in (80,591,8080) ",  # HTTP
    "10": " and tcpflag = 24 and dport = 443 ",  # HTTPS
    "11": " and dport =  53 ",  # DNS REQUEST
    "12": " and sport = 53 and protocol = 17 ",  # DNS RESPONSE
    "13": " and sip = dip ",  # LAND FLOOD
    "14": " and sport in (5060, 5061)  and dport in (5060, 5061) ",  # SIP
    "23": " and protocol = 17 and sport = 123 ",  # NTP RESPONSE
    "24": " and protocol = 17 and sport = 1900 ",  # SSDP REFLECTION FLOOD
    "25": " and protocol = 17 and sport = 161 ",  # SNMP REFLECTION FLOOD
    "26": " and protocol = 17 and sport = 19 ",  # CHARGEN REFLECTION FLOOD
}
'''
# TRACE_GROUP should keep the key in the same sequense with the group items in the trace page.
'''
TRACE_GROUP = {
    "1": 'sip',
    "2": 'dip',
    "3": 'sport',
    "4": 'dport',
    "5": 'protocol',
    "6": 'input_if',
    "7": 'output_if',
    "8": 'tcpflag',
    "9": 'srcas',
    "10": 'dstas',
    "11": 'srccountryname',
    "12": 'srcsubdivisionname',
    "13": 'srccityname',
    "14": 'dstcountryname',
    "15": 'dstsubdivisionname',
    "16": 'dstcityname'
}

TRACE_GROUP_DESC = {
    'sip': u'源IP',
    'dip': u'目的IP',
    'sport': u'源端口',
    'dport': u'目的端口',
    'protocol': u'协议',
    'input_if': u'INPUT接口',
    'output_if': u'OUTPUT接口',
    'tcpflag': u'TCPFLAG',
    'srcas': u'源自治域',
    'dstas': u'目的自治域',
    'srccountryname': u'源国家',
    'srcsubdivisionname': u'源省份',
    'srccityname': u'源城市',
    'dstcountryname': u'目的国家',
    'dstsubdivisionname': u'目的省份',
    'dstcityname': u'目的城市',
}

DDOS_DETAIL_FIELD_MAP = {
    "source_ip": u"攻击源IP",
    "atk_ip": u"攻击目标IP",
    "atk_port": u"被攻击端口",
    "event_type": u"攻击类型",
    "source_region": u"攻击源业务",
    "atk_region": u"攻击目标业务",
    "source_isp": u"攻击源ISP",
    "atk_isp": u"攻击目标ISP",
    "source_area": u"攻击源位置",
    "atk_area": u"攻击目标位置",
    "total_val": u"攻击总值(bytes/packets)",
    "max_val": u"攻击峰值(bps/pps)"
}

REPORT_NAME_DICT = {
    "isp": "运营商", "atk": "攻击类型", "bus": "业务", "ip": "IP", "area": "地域"
}

PROTOCOL_MAPPING = {
    # CODE : (NAME, DESCRIPTION)
    0: ('HOPOPT', 'IPv6 Hop-by-Hop Option'),
    1: ('ICMP', 'Internet Control Message'),
    2: ('IGMP', 'Internet Group Management'),
    3: ('GGP', 'Gateway-to-Gateway'),
    4: ('IPv4', 'IPv4 encapsulation'),
    5: ('ST', 'Stream'),
    6: ('TCP', 'Transmission Control'),
    7: ('CBT', 'CBT'),
    8: ('EGP', 'Exterior Gateway Protocol'),
    9: ('IGP', 'any private interior gateway (used by Cisco for their IGRP)'),
    10: ('BBN-RCC-MON', 'BBN RCC Monitoring'),
    11: ('NVP-II', 'Network Voice Protocol'),
    12: ('PUP', 'PUP'),
    13: ('ARGUS (deprecated)', 'ARGUS'),
    14: ('EMCON', 'EMCON'),
    15: ('XNET', 'Cross Net Debugger'),
    16: ('CHAOS', 'Chaos'),
    17: ('UDP', 'User Datagram'),
    18: ('MUX', 'Multiplexing'),
    19: ('DCN-MEAS', 'DCN Measurement Subsystems'),
    20: ('HMP', 'Host Monitoring'),
    21: ('PRM', 'Packet Radio Measurement'),
    22: ('XNS-IDP', 'XEROX NS IDP'),
    23: ('TRUNK-1', 'Trunk-1'),
    24: ('TRUNK-2', 'Trunk-2'),
    25: ('LEAF-1', 'Leaf-1'),
    26: ('LEAF-2', 'Leaf-2'),
    27: ('RDP', 'Reliable Data Protocol'),
    28: ('IRTP', 'Internet Reliable Transaction'),
    29: ('ISO-TP4', 'ISO Transport Protocol Class 4'),
    30: ('NETBLT', 'Bulk Data Transfer Protocol'),
    31: ('MFE-NSP', 'MFE Network Services Protocol'),
    32: ('MERIT-INP', 'MERIT Internodal Protocol'),
    33: ('DCCP', 'Datagram Congestion Control Protocol'),
    34: ('3PC', 'Third Party Connect Protocol'),
    35: ('IDPR', 'Inter-Domain Policy Routing Protocol'),
    36: ('XTP', 'XTP'),
    37: ('DDP', 'Datagram Delivery Protocol'),
    38: ('IDPR-CMTP', 'IDPR Control Message Transport Proto'),
    39: ('TP++', 'TP++ Transport Protocol'),
    40: ('IL', 'IL Transport Protocol'),
    41: ('IPv6', 'IPv6 encapsulation'),
    42: ('SDRP', 'Source Demand Routing Protocol'),
    43: ('IPv6-Route', 'Routing Header for IPv6'),
    44: ('IPv6-Frag', 'Fragment Header for IPv6'),
    45: ('IDRP', 'Inter-Domain Routing Protocol'),
    46: ('RSVP', 'Reservation Protocol'),
    47: ('GRE', 'Generic Routing Encapsulation'),
    48: ('DSR', 'Dynamic Source Routing Protocol'),
    49: ('BNA', 'BNA'),
    50: ('ESP', 'Encap Security Payload'),
    51: ('AH', 'Authentication Header'),
    52: ('I-NLSP', 'Integrated Net Layer Security  TUBA'),
    53: ('SWIPE (deprecated)', 'IP with Encryption'),
    54: ('NARP', 'NBMA Address Resolution Protocol'),
    55: ('MOBILE', 'IP Mobility'),
    56: ('TLSP', '"Transport Layer Security Protocol using Kryptonet key management"'),
    57: ('SKIP', 'SKIP'),
    58: ('IPv6-ICMP', 'ICMP for IPv6'),
    59: ('IPv6-NoNxt', 'No Next Header for IPv6'),
    60: ('IPv6-Opts', 'Destination Options for IPv6'),
    61: ('Any host internal protocol', ''),
    62: ('CFTP', 'CFTP'),
    63: ('Any local network', ''),
    64: ('SAT-EXPAK', 'SATNET and Backroom EXPAK'),
    65: ('KRYPTOLAN', 'Kryptolan'),
    66: ('RVD', 'MIT Remote Virtual Disk Protocol'),
    67: ('IPPC', 'Internet Pluribus Packet Core'),
    68: ('Any distributed file system', ''),
    69: ('SAT-MON', 'SATNET Monitoring'),
    70: ('VISA', 'VISA Protocol'),
    71: ('IPCV', 'Internet Packet Core Utility'),
    72: ('CPNX', 'Computer Protocol Network Executive'),
    73: ('CPHB', 'Computer Protocol Heart Beat'),
    74: ('WSN', 'Wang Span Network'),
    75: ('PVP', 'Packet Video Protocol'),
    76: ('BR-SAT-MON', 'Backroom SATNET Monitoring'),
    77: ('SUN-ND', 'SUN ND PROTOCOL-Temporary'),
    78: ('WB-MON', 'WIDEBAND Monitoring'),
    79: ('WB-EXPAK', 'WIDEBAND EXPAK'),
    80: ('ISO-IP', 'ISO Internet Protocol'),
    81: ('VMTP', 'VMTP'),
    82: ('SECURE-VMTP', 'SECURE-VMTP'),
    83: ('VINES', 'Transaction Transport Protocol'),
    84: ('IPTM', 'Internet Protocol Traffic Manager'),
    85: ('NSFNET-IGP', 'NSFNET-IGP'),
    86: ('DGP', 'Dissimilar Gateway Protocol'),
    87: ('TCF', 'TCF'),
    88: ('EIGRP', 'EIGRP'),
    89: ('OSPFIGP', 'OSPFIGP'),
    90: ('Sprite-RPC', 'Sprite RPC Protocol'),
    91: ('LARP', 'Locus Address Resolution Protocol'),
    92: ('MTP', 'Multicast Transport Protocol'),
    93: ('AX.25', 'AX.25 Frames'),
    94: ('IPIP', 'IP-within-IP Encapsulation Protocol'),
    95: ('MICP (deprecated)', 'Mobile Internetworking Control Pro.'),
    96: ('SCC-SP', 'Semaphore Communications Sec. Pro.'),
    97: ('ETHERIP', 'Ethernet-within-IP Encapsulation'),
    98: ('ENCAP', 'Encapsulation Header'),
    99: ('Any private encryption scheme', ''),
    100: ('GMTP', 'GMTP'),
    101: ('IFMP', 'Ipsilon Flow Management Protocol'),
    102: ('PNNI', 'PNNI over IP'),
    103: ('PIM', 'Protocol Independent Multicast'),
    104: ('ARIS', 'ARIS'),
    105: ('SCPS', 'SCPS'),
    106: ('QNX', 'QNX'),
    107: ('A/N', 'Active Networks'),
    108: ('IPComp', 'IP Payload Compression Protocol'),
    109: ('SNP', 'Sitara Networks Protocol'),
    110: ('Compaq-Peer', 'Compaq Peer Protocol'),
    111: ('IPX-in-IP', 'IPX in IP'),
    112: ('VRRP', 'Virtual Router Redundancy Protocol'),
    113: ('PGM', 'PGM Reliable Transport Protocol'),
    114: ('Any 0-hop protocol', ''),
    115: ('L2TP', 'Layer Two Tunneling Protocol'),
    116: ('DDX', 'D-II Data Exchange (DDX)'),
    117: ('IATP', 'Interactive Agent Transfer Protocol'),
    118: ('STP', 'Schedule Transfer Protocol'),
    119: ('SRP', 'SpectraLink Radio Protocol'),
    120: ('UTI', 'UTI'),
    121: ('SMP', 'Simple Message Protocol'),
    122: ('SM (deprecated)', 'Simple Multicast Protocol'),
    123: ('PTP', 'Performance Transparency Protocol'),
    124: ('ISIS over IPv4', ''),
    125: ('FIRE', ''),
    126: ('CRTP', 'Combat Radio Transport Protocol'),
    127: ('CRUDP', 'Combat Radio User Datagram'),
    128: ('SSCOPMCE', ''),
    129: ('IPLT', ''),
    130: ('SPS', 'Secure Packet Shield'),
    131: ('PIPE', 'Private IP Encapsulation within IP'),
    132: ('SCTP', 'Stream Control Transmission Protocol'),
    133: ('FC', 'Fibre Channel'),
    134: ('RSVP-E2E-IGNORE', ''),
    135: ('Mobility Header', ''),
    136: ('UDPLite', ''),
    137: ('MPLS-in-IP', ''),
    138: ('manet', 'MANET Protocols'),
    139: ('HIP', 'Host Identity Protocol'),
    140: ('Shim6', 'Shim6 Protocol'),
    141: ('WESP', 'Wrapped Encapsulating Security Payload'),
    142: ('ROHC', 'Robust Header Compression'),
    143: ('Unassigned', ''),
    144: ('Unassigned', ''),
    145: ('Unassigned', ''),
    146: ('Unassigned', ''),
    147: ('Unassigned', ''),
    148: ('Unassigned', ''),
    149: ('Unassigned', ''),
    150: ('Unassigned', ''),
    151: ('Unassigned', ''),
    152: ('Unassigned', ''),
    153: ('Unassigned', ''),
    154: ('Unassigned', ''),
    155: ('Unassigned', ''),
    156: ('Unassigned', ''),
    157: ('Unassigned', ''),
    158: ('Unassigned', ''),
    159: ('Unassigned', ''),
    160: ('Unassigned', ''),
    161: ('Unassigned', ''),
    162: ('Unassigned', ''),
    163: ('Unassigned', ''),
    164: ('Unassigned', ''),
    165: ('Unassigned', ''),
    166: ('Unassigned', ''),
    167: ('Unassigned', ''),
    168: ('Unassigned', ''),
    169: ('Unassigned', ''),
    170: ('Unassigned', ''),
    171: ('Unassigned', ''),
    172: ('Unassigned', ''),
    173: ('Unassigned', ''),
    174: ('Unassigned', ''),
    175: ('Unassigned', ''),
    176: ('Unassigned', ''),
    177: ('Unassigned', ''),
    178: ('Unassigned', ''),
    179: ('Unassigned', ''),
    180: ('Unassigned', ''),
    181: ('Unassigned', ''),
    182: ('Unassigned', ''),
    183: ('Unassigned', ''),
    184: ('Unassigned', ''),
    185: ('Unassigned', ''),
    186: ('Unassigned', ''),
    187: ('Unassigned', ''),
    188: ('Unassigned', ''),
    189: ('Unassigned', ''),
    190: ('Unassigned', ''),
    191: ('Unassigned', ''),
    192: ('Unassigned', ''),
    193: ('Unassigned', ''),
    194: ('Unassigned', ''),
    195: ('Unassigned', ''),
    196: ('Unassigned', ''),
    197: ('Unassigned', ''),
    198: ('Unassigned', ''),
    199: ('Unassigned', ''),
    200: ('Unassigned', ''),
    201: ('Unassigned', ''),
    202: ('Unassigned', ''),
    203: ('Unassigned', ''),
    204: ('Unassigned', ''),
    205: ('Unassigned', ''),
    206: ('Unassigned', ''),
    207: ('Unassigned', ''),
    208: ('Unassigned', ''),
    209: ('Unassigned', ''),
    210: ('Unassigned', ''),
    211: ('Unassigned', ''),
    212: ('Unassigned', ''),
    213: ('Unassigned', ''),
    214: ('Unassigned', ''),
    215: ('Unassigned', ''),
    216: ('Unassigned', ''),
    217: ('Unassigned', ''),
    218: ('Unassigned', ''),
    219: ('Unassigned', ''),
    220: ('Unassigned', ''),
    221: ('Unassigned', ''),
    222: ('Unassigned', ''),
    223: ('Unassigned', ''),
    224: ('Unassigned', ''),
    225: ('Unassigned', ''),
    226: ('Unassigned', ''),
    227: ('Unassigned', ''),
    228: ('Unassigned', ''),
    229: ('Unassigned', ''),
    230: ('Unassigned', ''),
    231: ('Unassigned', ''),
    232: ('Unassigned', ''),
    233: ('Unassigned', ''),
    234: ('Unassigned', ''),
    235: ('Unassigned', ''),
    236: ('Unassigned', ''),
    237: ('Unassigned', ''),
    238: ('Unassigned', ''),
    239: ('Unassigned', ''),
    240: ('Unassigned', ''),
    241: ('Unassigned', ''),
    242: ('Unassigned', ''),
    243: ('Unassigned', ''),
    244: ('Unassigned', ''),
    245: ('Unassigned', ''),
    246: ('Unassigned', ''),
    247: ('Unassigned', ''),
    248: ('Unassigned', ''),
    249: ('Unassigned', ''),
    250: ('Unassigned', ''),
    251: ('Unassigned', ''),
    252: ('Unassigned', 'Unassigned'),
    253: ('Use for experimentation and testing', ''),
    254: ('Use for experimentation and testing', ''),
    255: ('Reserved', '')
}
