#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    根据单位设置中的省份，查询每个省的行业名，然后去持续性事件中查找对应事件
    按每个省份分类，导出excel文件
"""
import csv
import datetime
import json
import os
import sys
import time

import paramiko

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.cont_event_models import ContinuousEventsModel
from cncert_kj.script.cont_events_task import global_black_list
from cncert_kj.utils import logger, time_trans, bytes_trans, net
from cncert_kj.utils.conf_util import CommonConf
from cncert_kj.utils.flow_log_util import SUFFIX
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT, get_days_ago_00_timestamp, DATE_FORMAT
from cncert_kj.utils.zx_trans import extract_between_strings, START_STR, END_STR

reload(sys)
sys.setdefaultencoding('utf-8')

mlog = logger.init_logger('do_excel_group_by_province')


class CertSSHClient(object):
    def __init__(self):
        self.province_dic = {
            "安徽省": "anhui",
            "西藏自治区": "xizang",
            "宁夏回族自治区": "ningxia",
            "广东省": "guangdong",
            "辽宁省": "liaoning",
            "内蒙古自治区": "neimenggu",
            "青海省": "qinghai",
            "山西省": "shan1xi",
            "陕西省": "shan3xi",
            "四川省": "sichuan",
            "新疆维吾尔自治区": "xinjiang",
            "云南省": "yunnan",
            "重庆市": "chongqing",
            "广西省": "guangxi",
            "上海市": "shanghai",
            "吉林省": "jilin",
            "湖南省": "hunan",
            "湖北省": "hubei",
            "海南省": "hainan",
            "河南省": "henan",
            "黑龙江": "heilongjiang",
            "贵州省": "guizhou",
            "福建省": "fujian",
            "甘肃省": "gansu",
            "浙江省": "zhejiang",
            "河北省": "hebei",
            "江西省": "jiangxi",
            "北京市": "beijing",
            "山东省": "shandong",
            "江苏省": "jiangsu",
        }

        # 线上ssh配置
        self.ssh_conf = CommonConf().ssh_put_event_conf()

        if CommonConf().is_dev():
            # 本地ssh配置
            self.ssh_conf = {
                "host": "************",
                "port": 22,
                "username": "root",
                "password": "jf1Z@.",
                "ssh_base_path": "/mnt/sdb/ftpdata",
            }

        self.ssh, self.sftp = self.ssh_login()

    def ssh_login(self):
        """ssh连接"""
        # 建立 SSH 连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=self.ssh_conf["host"],
                    port=self.ssh_conf["port"],
                    username=self.ssh_conf["username"],
                    password=self.ssh_conf["password"])
        # 创建 SFTP 客户端
        sftp = ssh.open_sftp()
        return ssh, sftp

    def ssh_close(self):
        """关闭ssh链接"""
        # 关闭 SFTP 客户端和 SSH 连接
        self.sftp.close()
        self.ssh.close()

    def ssh_put(self, local_file_path, remote_file_path):
        """
            使用ssh推动到对端服务器
        """
        # 将本地文件推送到远程服务器
        self.sftp.put(local_file_path, remote_file_path)

    def ssh_get(self, remote_file_path, local_file_path):
        """
            使用ssh从对端服务器获取文件
        """
        # 从远程服务器获取文件
        self.sftp.get(remote_file_path, local_file_path)

    def ssh_cmd(self, cmd):
        """
            使用ssh执行命令
        """
        # 执行命令并获取输出
        stdin, stdout, stderr = self.ssh.exec_command(cmd)

        # 解析命令输出
        output = stdout.read().decode("utf-8")
        if output:
            files = output.split("\n")  # 将输出按行拆分
            for file in files:
                print(file)

    def get_file_size(self, remote_file_path):
        """
            获取文件大小
        """
        # 获取文件信息
        file_info = self.sftp.stat(remote_file_path)
        # 获取文件大小
        file_size = file_info.st_size
        return file_size

    def get_file_list(self, remote_file_path):
        """
            获取文件列表
        """
        # 获取文件列表
        file_list = self.sftp.listdir(remote_file_path)
        return file_list

    def get_file_stat(self, remote_file_path):
        """
            获取文件状态
        """
        file_stat = self.sftp.stat(remote_file_path)
        return file_stat

    def check_yesterday_file_size(self):
        """
            检查昨天文件的大小
        """
        # 获取昨天的日期
        yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime(DATE_FORMAT)
        mlog.info("yesterday:{}".format(yesterday))
        csv_file_sizes = {}
        json_file_size = {}
        for i in self.province_dic.values():
            province_dir_path = os.path.join(self.ssh_conf["ssh_base_path"], i.encode("utf-8"))
            file_list = self.get_file_list(province_dir_path)
            for file in file_list:
                file_path = os.path.join(province_dir_path, file)
                attributes = self.get_file_stat(file_path)
                if (file.split(".")[0]).endswith(yesterday):
                    file_size = attributes.st_size
                    if file.endswith(".csv"):
                        csv_file_sizes[file_path] = file_size
                    elif file.endswith(".json"):
                        json_file_size[file_path] = file_size


        if not csv_file_sizes:
            mlog.info("SCRIPT:do_excel_group_by_province.py:\t\t{},未推送事件".format(yesterday))
        else:
            str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},事件推送异常,都为空".format(yesterday)
            for i in csv_file_sizes.values():
                if i != 0:
                    str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},事件推送正常".format(yesterday)
                    break
            mlog.info(str_)

        if not json_file_size:
            mlog.info("SCRIPT:do_excel_group_by_province.py:\t\t{},未推送原始日志".format(yesterday))
        else:
            str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},原始日志推送异常,都为空".format(yesterday)
            for i in json_file_size.values():
                if i != 0:
                    str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},原始日志推送正常".format(yesterday)
                    break
            mlog.info(str_)


class ProvinceEventToExcel:
    def __init__(self, start_timestamp, end_timestamp):
        self.start_timestamp = start_timestamp
        self.end_timestamp = end_timestamp

        self.csv_data = {}
        self.json_data = {}

        self.other_csv_data = {}
        self.other_json_data = {}

        # >> 目录
        # self.base_path = os.path.basename(CONF_PATH)
        self.dir_path = "/home/<USER>/ISOP/ProvinceEventToExcel"
        self.conf_util = CommonConf()
        self.EVENT_TYPE_MAP = self.conf_util.get_continuous_event_type_reverse()

        # ssh
        self.ssh_ = CertSSHClient()
        # ssh目录
        self.ssh_base_path = self.ssh_.ssh_conf["ssh_base_path"]
        # ssh省份目录
        self.province_dic = self.ssh_.province_dic

        self.creat_dir()
        self.creat_province_dir()

        self.type_str_dic = self.get_event_type_tuple()

    def creat_dir(self):
        if not os.path.exists(self.dir_path):
            os.makedirs(self.dir_path)
            mlog.info("创建目录：{}".format(self.dir_path))
        else:
            mlog.info("目录路径已存在：{}".format(self.dir_path))

    def creat_province_dir(self):
        for i in self.province_dic.values():
            province_dir_path = os.path.join(self.dir_path, i)
            if not os.path.exists(province_dir_path):
                os.makedirs(province_dir_path)
                # mlog.info("创建子目录：{}".format(province_dir_path))
            else:
                # mlog.info("子目录路径已存在：{}".format(province_dir_path))
                pass

    def filter_event(self, where_condition="", tag_filter_condition="", params=[]):
        """查询事件"""
        sql_ = """
            with events as (
            SELECT 
                event_id,   --0
                src_ip,
                src_region,
                src_operator,
                src_info,
                src_threat_mark,    --5
                dst_ip,
                dst_region,
                dst_operator,
                dst_info,
                dst_threat_mark,    --10
                start_time,
                end_time,
                status,
                event_type,
                up_bytesall,        --15
                down_bytesall,
                judge_status,
                report_type,
                judge_info,
                src_com,        --20
                dst_com,
                analysis_tech,
                related_alerts,
                reverse_tag,
                related_alerts_count,   --25
                app_type    --26
            FROM internal_app_bsa_gjk.continuous_events
            {where_condition}
            )        
            select 
                events.*,
                c_e_tag.src_info,   --27
                c_e_tag.dst_info,   --28
                c_e_tag.src_service,   --29
                c_e_tag.app_type,   --30
                c_e_tag.port_distribution   --31
            from events
            left join 
            (
                SELECT event_id, 
                    string_agg(case when tag_type=1 then tag_content end,'&') src_info,
                    string_agg(case when tag_type=2 then tag_content end,'&') dst_info,
                    string_agg(case when tag_type=1 and tag_name='service' then tag_content end,'&') src_service,
                    string_agg(case when tag_name='app_type' then tag_content end,'&') app_type,
                    string_agg(case when tag_type=0 and tag_name='port_distribution' then tag_content end,'&') port_distribution
                FROM internal_app_bsa_gjk.continuous_events_tag cet 
                WHERE event_id IN ( SELECT event_id FROM events ) 
                GROUP BY event_id
            )  c_e_tag on events.event_id = c_e_tag.event_id
            {tag_filter_condition} 
            order by event_type asc, end_time desc
        """.format(where_condition=where_condition,
                   tag_filter_condition=tag_filter_condition)
        filter_sql_param = CPgSqlParam(sql_, params=tuple(params))
        filter_res_list = json.loads(CFunction.execute(filter_sql_param))
        res_list = []
        for res in filter_res_list:
            data = {
                "event_id": res[0],
                "src_ip": res[1],
                "src_region": res[2],
                "src_operator": res[3],
                "src_info": res[27],
                "src_threat_mark": res[5],
                "dst_ip": res[6],
                "dst_region": res[7],
                "dst_operator": res[8],
                "dst_info": res[28],
                "dst_threat_mark": res[10],
                "start_time": res[11],
                "end_time": res[12],
                "status": res[13],
                "event_type": res[14],
                "up_bytesall": res[15],
                "down_bytesall": res[16],
                "judge_status": res[17],
                "report_type": res[18],
                "judge_info": res[19],
                "src_com": res[20],
                "dst_com": res[21],
                "analysis_tech": res[22],
                "related_alerts": res[23],
                "reverse_tag": res[24],
                "related_alerts_count": res[25],
                "app_type": res[30] or "",
                "port_distribution": res[31] or ""
            }
            res_list.append(data)

        return res_list

    def filter_unit_by_province(self):
        try:
            key_unit_sql = '''
                SELECT
                    id,
                    name,
                    department
                FROM internal_app_bsa_gjk.key_unit
                where department in %s
                ORDER BY id DESC
            '''
            key_unit_param = CPgSqlParam(key_unit_sql, params=(tuple(self.province_dic.keys()),))
            key_unit_res_list = json.loads(CFunction.execute(key_unit_param))

            dic_ = {}
            for k, v in self.province_dic.items():
                dic_[k.encode("utf-8")] = []

            for i in key_unit_res_list:
                department = i[2].encode("utf-8")
                if department not in dic_:
                    dic_[department] = [i[1]]
                else:
                    dic_[department].append(i[1])

            return dic_
        except Exception:
            mlog.exception("查询重点单位失败")

    def select_sql(self):
        """查询语句通用字段"""
        sql_ = """
        SELECT 
            event_id,   --0
            src_ip,
            src_region,
            src_operator,
            src_info,
            src_threat_mark,    --5
            dst_ip,
            dst_region,
            dst_operator,
            dst_info,
            dst_threat_mark,    --10
            start_time,
            end_time,
            status,
            event_type,
            up_bytesall,        --15
            down_bytesall,
            judge_status,
            report_type,
            judge_info,
            src_com,        --20
            dst_com,
            analysis_tech,
            related_alerts,
            reverse_tag,
            related_alerts_count,   --25
            app_type    --26
        FROM internal_app_bsa_gjk.continuous_events
        """
        return sql_

    def csv_title(self):
        """事件csv文件表头"""
        file_cont_list = [[
            "开始时间",
            "结束时间",
            # "流量持续状态",
            # "事件处置状态",
            "事件类型",
            # "检测手段",
            "上行/流出总流量",
            "下行/流入总流量",
            "源IP",
            "源地理位置",
            "源运营商",
            "源备案单位",
            "源信息",
            "目的IP",
            "目的地理位置",
            "目的运营商",
            "目的备案单位",
            "目的信息",
            "应用类型",
            "告警次数",
            "端口分布",
            "备注"
        ]]
        return file_cont_list

    def _csv_data_list(self, event_info):
        """csv文件插入字段"""
        src_info_list = [i for i in event_info[27].split("&") if i] if event_info[27] else []
        dst_info_list = [i for i in event_info[28].split("&") if i] if event_info[28] else []

        if event_info[1] in global_black_list:
            src_info_list.append("源IP属于黑名单")
        if event_info[6] in global_black_list:
            dst_info_list.append("目的IP属于黑名单")
        event_res_list = [
            time_trans.timestamp2format(event_info[11]),  # 开始时间  11
            time_trans.timestamp2format(event_info[12]),  # 结束时间  12
            # STATUS_MAP.get(str(event_info[13]), ""),  # 流量持续状态    13
            # JUDGE_STATUS_MAP.get(str(event_info[17]), ""),  # 事件处置状态
            self.EVENT_TYPE_MAP.get(str(event_info[14]), ""),  # >> 事件类型
            # ANALYSIS_TECH_MAP.get(str(event_info[22]), ""),  # 检测手段

            bytes_trans.long2unit(event_info[15]),  # 上行/流出总流量
            bytes_trans.long2unit(event_info[16]),  # 下行/流入总流量
            event_info[1],  # 源IP
            event_info[2],  # 源地理位置
            event_info[3],  # 源运营商
            event_info[20],  # >> 源备案单位
            # event_info[4],  # 源信息
            '&'.join(src_info_list),  # 源信息

            event_info[6],  # 目的IP
            event_info[7],  # 目的地理位置
            event_info[8],  # 目的运营商
            event_info[21],  # >> 目的备案单位
            # event_info[9],  # 目的信息
            '&'.join(dst_info_list),  # 目的信息
            event_info[30],  # 应用类型
            event_info[25],  # 告警次数
            event_info[19]  # 备注
        ]
        return event_res_list

    def csv_data_list(self, event_info):
        """csv文件插入字段"""
        src_info_list = [i for i in event_info.get("src_info", "").split("&") if i] if event_info.get("src_info",
                                                                                                      "") else []
        dst_info_list = [i for i in event_info.get("dst_info", "").split("&") if i] if event_info.get("dst_info",
                                                                                                      "") else []

        if event_info.get("src_ip", "") in global_black_list:
            src_info_list.append("源IP属于黑名单")
        if event_info.get("dst_ip", "") in global_black_list:
            dst_info_list.append("目的IP属于黑名单")
        event_res_list = [
            time_trans.timestamp2format(event_info.get("start_time", "")),  # 开始时间  11
            time_trans.timestamp2format(event_info.get("end_time", "")),  # 结束时间  12
            self.EVENT_TYPE_MAP.get(str(event_info.get("event_type", "")), ""),  # 事件类型

            bytes_trans.long2unit(event_info.get("up_bytesall", "")),  # 上行/流出总流量
            bytes_trans.long2unit(event_info.get("down_bytesall", "")),  # 下行/流入总流量
            event_info.get("src_ip", ""),  # 源IP
            event_info.get("src_region", ""),  # 源地理位置
            event_info.get("src_operator", ""),  # 源运营商
            event_info.get("src_com", ""),  # 源备案单位
            '&'.join(src_info_list),  # 源信息

            event_info.get("dst_ip", ""),  # 目的IP
            event_info.get("dst_region", ""),  # 目的地理位置
            event_info.get("dst_operator", ""),  # 目的运营商
            event_info.get("dst_com", ""),  # 目的备案单位
            '&'.join(dst_info_list),  # 目的信息
            event_info.get("app_type", ""),  # 应用类型
            event_info.get("related_alerts_count", ""),  # 告警次数
            event_info.get("port_distribution", ""),  # 端口分布状态
            event_info.get("judge_info", "").replace("\n", "&")  # 备注
        ]
        return event_res_list

    def filter_event_by_unit(self, department_dic, start_timestamp, end_timestamp):
        """查询包含重点单位的事件"""
        event_dic = {}

        for k, v in department_dic.items():
            if not v:
                event_dic[k] = []
                continue

            # and split_part(src_info, '&', 2) IN %s
            params = [start_timestamp, end_timestamp]
            where_sql = """
            WHERE end_time >=%s and end_time <%s  
                and judge_status != 6
                and up_bytesall >= 104857600
            """.format(start_timestamp, end_timestamp)
            filter_sql = self.select_sql() + where_sql

            tag_field_list = [
                "src_info",  # 27
                "dst_info",  # 28
                "src_service",  # 29
                "app_type"
            ]
            # tag_filter_condition = "where src_service in %s"
            tag_filter_condition = "where c_e_tag.src_service in %s"
            params.append(tuple(v))
            filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list, tag_filter_condition)
            filter_sql += " order by end_time desc"

            filter_res_list = self.filter_event(where_condition=where_sql, tag_filter_condition=tag_filter_condition,
                                                params=params)

            mlog.info("行业名称：{}， 查询到事件量：{}".format(k, len(filter_res_list)))

            event_dic[k] = filter_res_list
        return event_dic

    def get_csv_file(self, file_path, file_content):
        with open(file_path, 'wb') as f:
            # 写入表头，不加双引号
            csvwriter = csv.writer(f)
            for row in file_content:
                csvwriter.writerow([unicode(s).encode("utf-8") for s in row])

    def get_json_file(self, file_path, file_content):
        with open(file_path, 'wb') as f:
            # json.dump(file_content, f)
            for i in file_content:
                f.write(json.dumps(i, ensure_ascii=False) + "\n")

    def filter_alert(self, related_alerts_list, report_type):
        """
            查询告警表--获取原始日志，20条
        """
        if report_type == 1:
            filter_traffic_alert_sql = """
            SELECT 
                uuid,
                flow_logs
            FROM internal_app_bsa_gjk.traffic_alert 
            WHERE uuid in %s
            order by end_time desc
            limit 20
            """
        else:
            filter_traffic_alert_sql = """
            SELECT 
                uuid,
                flow_logs
            FROM internal_app_bsa_gjk.netflow_alert_5min 
            WHERE uuid in %s
            order by end_time desc
            limit 20
            """
        res_traffic_alert = json.loads(CFunction.execute(CPgSqlParam(
            filter_traffic_alert_sql,
            params=(tuple(related_alerts_list),)
        )))

        flow_logs_list = []
        for i in res_traffic_alert:
            if len(flow_logs_list) >= 20:
                break
            if str(i[1]).endswith(SUFFIX):
                continue
            flow_logs = i[1].split("|")
            for k in flow_logs:
                if len(flow_logs_list) >= 20:
                    break
                else:
                    try:
                        flow_logs_dic = json.loads(k)
                        if not flow_logs_dic:
                            continue
                    except Exception:
                        try:
                            hostr = extract_between_strings(k, START_STR, END_STR)
                            flow_logs_dic = json.loads(k.replace(hostr, '""'))
                        except Exception:
                            continue
                    if not isinstance(flow_logs_dic, dict):
                        continue

                    flow_logs_list.append(flow_logs_dic)

        return flow_logs_list

    def timestamp_to_str_time(self, timestamp):
        """时间戳转字符串格式"""
        return time.strftime(DATE_TIME_FORMAT, time.localtime(timestamp))

    def dispose_flow_logs_list(self, flow_logs_list):
        """
            处理原始日志
        """

        res_data_list = []
        for i in flow_logs_list:
            res_dic = {
                "src_ip": net.int32_to_ip(i.get("src_ip")),
                "sport": i.get("sport"),
                "dst_ip": net.int32_to_ip(i.get("dst_ip")),
                "dport": i.get("dport"),
                "up_packets": i.get("up_packets"),
                "up_bytes": i.get("up_bytes"),
                "down_packets": i.get("down_packets"),
                "down_bytes": i.get("down_bytes"),
                "c_log_time": self.timestamp_to_str_time(i.get("c_log_time")),
                "stream_time": self.timestamp_to_str_time(i.get("stream_time")),
                "src_country": i.get("src_country", ""),
                "src_province": i.get("src_province", ""),
                "src_oper": i.get("src_oper"),
                "dst_country": i.get("dst_country", ""),
                "dst_province": i.get("dst_province", ""),
                "dst_oper": i.get("dst_oper"),
                "app": i.get("app"),
                "hostr": i.get("hostr"),
                "protocol": i.get("protocol"),
            }
            res_data_list.append(res_dic)
        return res_data_list

    def format_province(self, province):
        """去掉源地理位置中的省市区，方便判断是否存在于给定的省份字典中"""
        province = province.replace("省", "")
        province = province.replace("市", "")
        province = province.replace("自治区", "")
        return province

    def _get_csv_data_and_alert_data(self, filter_res_list):
        """遍历事件，获取事件与原始日志"""
        for event_info in filter_res_list:
            src_region = event_info[2]
            # 新需求:源标签中含有省份的,也推送
            src_info = event_info[27]
            if (not src_region or src_region == 'None') and (src_info == 'None' or not src_info):
                continue
            province_list = src_region.split(' ')
            if len(province_list) == 1:
                province_ = province_list[0]
            else:
                province_ = province_list[1]
            province_ = self.format_province(province_).strip()
            # if not province_:
            #     continue

            event_type = event_info[14]
            # other_type_list = [int(i) for i in self.type_str_dic.get("其他").split(',')]
            other_type_list = self.type_str_dic.get("其他")

            if event_type in other_type_list:
                for k, v in self.other_csv_data.items():
                    if (province_ and self.format_province(k).strip() in province_) or (
                            src_info and self.format_province(k).strip() in src_info):
                        # if self.format_province(k).strip() in province_ or self.format_province(k).strip() in src_info:
                        # if province_ in k:
                        event_res_list = self.csv_data_list(event_info)
                        v.append(event_res_list)

                        # 查询关联告警,获取原始日志
                        related_alerts_list = event_info[23].split(";")
                        report_type = event_info[18]
                        flow_logs_list = self.filter_alert(related_alerts_list, report_type)
                        res_flow_logs_list = self.dispose_flow_logs_list(flow_logs_list)
                        # alert_province_dic.get(k).extend(res_flow_logs_list)
                        self.other_json_data.get(k).extend(res_flow_logs_list)
                    else:
                        continue
            else:
                for k, v in self.csv_data.items():
                    if (province_ and self.format_province(k).strip() in province_) or (
                            src_info and self.format_province(k).strip() in src_info):
                        # if province_ in k:
                        event_res_list = self.csv_data_list(event_info)
                        v.append(event_res_list)

                        # 查询关联告警,获取原始日志
                        related_alerts_list = event_info[23].split(";")
                        report_type = event_info[18]
                        flow_logs_list = self.filter_alert(related_alerts_list, report_type)
                        res_flow_logs_list = self.dispose_flow_logs_list(flow_logs_list)
                        # alert_province_dic.get(k).extend(res_flow_logs_list)
                        self.json_data.get(k).extend(res_flow_logs_list)
                    else:
                        continue

    def get_csv_data_and_alert_data(self, filter_res_list):
        """遍历事件，获取事件与原始日志"""
        if not filter_res_list:
            mlog.info("没有事件数据需要处理")
            return

        # 预处理省份格式化:{西藏自治区:西藏}
        formatted_provinces = {k: self.format_province(k).strip() for k in self.province_dic.keys()}

        # 获取其他类型列表
        other_type_list = self.type_str_dic.get("其他", [])

        # 事件计数器
        processed_count = 0
        matched_count = 0

        for event_info in filter_res_list:
            # 提取源地区和源信息
            src_region = event_info.get("src_region", "")
            src_info = event_info.get("src_info", "")

            # 跳过没有地区和信息的事件
            if (not src_region or src_region == 'None') and (not src_info or src_info == 'None'):
                continue

            # 处理省份信息
            province_ = ""
            if src_region and src_region != 'None':
                province_list = src_region.split(' ')
                province_ = province_list[1] if len(province_list) > 1 else province_list[0]
                province_ = self.format_province(province_).strip()

            # 确定事件类型和目标数据字典
            event_type = event_info.get("event_type", "")
            is_other_type = event_type in other_type_list

            target_data = self.other_csv_data if is_other_type else self.csv_data
            target_json = self.other_json_data if is_other_type else self.json_data

            # 查找匹配的省份
            matched_provinces = []
            for k, formatted_k in formatted_provinces.items():
                # 检查省份是否匹配
                if (province_ and formatted_k in province_) or (src_info and formatted_k in src_info):
                    matched_provinces.append(k)

            # 如果没有匹配的省份，继续下一个事件
            if not matched_provinces:
                continue

            # 处理匹配的省份
            for k in matched_provinces:
                # 生成CSV数据
                event_res_list = self.csv_data_list(event_info)
                target_data[k].append(event_res_list)

                # 查询关联告警,获取原始日志
                related_alerts_list = event_info.get("related_alerts", "").split(";")
                report_type = event_info.get("report_type", "")

                # 使用批量查询优化
                flow_logs_list = self.filter_alert(related_alerts_list, report_type)
                res_flow_logs_list = self.dispose_flow_logs_list(flow_logs_list)
                target_json[k].extend(res_flow_logs_list)

                matched_count += 1

            processed_count += 1

            # 每处理100条记录输出一次日志
            if processed_count % 100 == 0:
                mlog.info("已处理 {} 条事件".format(processed_count, matched_count))

        mlog.info("事件处理完成，共处理 {} 条事件，匹配 {} 条".format(processed_count, matched_count))

    def put(self, prvince, event_data_list, all_event_related_alerts_list, type_name=""):
        """推送事件与告警的csv文件到对端ssh路径"""
        mlog.info("事件数量：{}， 原始日志数量：{}".format(len(event_data_list) - 1, len(all_event_related_alerts_list)))
        province_dir_path = os.path.join(self.dir_path, self.province_dic.get(prvince.encode("utf-8")))
        file_name = "{}{}异常事件_{}.csv".format(
            prvince,
            type_name,
            time.strftime(DATE_FORMAT, time.localtime(self.start_timestamp))
        )
        flow_logs_file_name = "{}{}原始日志_{}.json".format(
            prvince,
            type_name,
            time.strftime(DATE_FORMAT, time.localtime(self.start_timestamp))
        )

        file_path = os.path.join(province_dir_path, file_name)
        flow_logs_file_path = os.path.join(province_dir_path, flow_logs_file_name)

        mlog.info("生成异常事件文件,路径：{}".format(file_path))
        mlog.info("生成原始日志文件,路径：{}".format(flow_logs_file_path))

        # 文件存入本地路径
        self.get_csv_file(file_path, event_data_list)
        self.get_json_file(flow_logs_file_path, all_event_related_alerts_list)

        # 推送对端ftp
        remote_path = os.path.join(self.ssh_base_path,
                                   self.province_dic.get(prvince.encode("utf-8")),
                                   file_name)
        flow_logs_remote_path = os.path.join(self.ssh_base_path,
                                             self.province_dic.get(prvince.encode("utf-8")),
                                             flow_logs_file_name)

        mlog.info("推送异常事件文件到对端，路径：{}".format(remote_path))
        mlog.info("推送原始日志文件到对端，路径：{}".format(flow_logs_remote_path))

        self.ssh_.ssh_put(file_path, remote_path)
        self.ssh_.ssh_put(flow_logs_file_path, flow_logs_remote_path)
        mlog.info("推送完成")

    def delete_old_dir(self):
        """删除以前的目录"""
        # 获取当前时间戳
        current_time = datetime.datetime.now().date()
        two_days_ago = current_time - datetime.timedelta(days=2)

        # 指定要删除的目录路径
        for k, v in self.province_dic.items():
            province_dir_path = os.path.join(self.dir_path, v)
            list_dir = os.listdir(province_dir_path)
            # 遍历目录下的所有文件
            for filename in list_dir:
                file_path = os.path.join(province_dir_path, filename)
                if os.path.isfile(file_path):
                    # 获取文件最后修改日期
                    modified_date = datetime.date.fromtimestamp(os.path.getmtime(file_path))

                    # 检查文件日期是否在两天前之前
                    if modified_date <= two_days_ago:
                        # 删除文件
                        os.remove(file_path)
                        print "删除两天前的本地文件: {}".format(file_path)

    def run_key_unit(self):
        """重点单位"""
        mlog.info("一、重点单位异常事件")
        department_dic = self.filter_unit_by_province()
        event_dic = self.filter_event_by_unit(department_dic, self.start_timestamp, self.end_timestamp)

        for k, v in event_dic.items():
            # mlog.info("---------------{},{}".format(k, type(k)))

            # >> 当前省份每条事件关联告警list
            # all_event_related_alerts_list = []
            for event_info in v:
                # 字段处理
                event_res_list = self.csv_data_list(event_info)
                self.csv_data[k].append(event_res_list)

                # >> 查询关联告警,获取原始日志
                # related_alerts_list = event_info[23].split(";")
                # report_type = event_info[18]
                related_alerts_list = event_info.get("related_alerts", "").split(";")
                report_type = event_info.get("report_type", "")
                flow_logs_list = self.filter_alert(related_alerts_list, report_type)
                res_flow_logs_list = self.dispose_flow_logs_list(flow_logs_list)
                # >>
                # all_event_related_alerts_list.extend(res_flow_logs_list)
                self.json_data[k].extend(res_flow_logs_list)

    def run_key_db_service(self):
        """数据库默认端口类型异常事件"""
        mlog.info("二、数据库默认端口类型异常事件")
        type_list = self.type_str_dic.get("数据库默认端口")
        if type_list is None or not type_list:
            return
        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]
        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
            and judge_status in (2,7)
            and up_bytesall >= 104857600
        """
        # >>
        # filter_sql = self.select_sql() + where_sql
        #
        # tag_field_list = [
        #     "src_info",  # 27
        #     "dst_info",  # 28
        #     "src_service",  # 29
        #     "app_type"
        # ]
        # filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        # filter_sql += " order by end_time desc"
        # filter_res_list = json.loads(CFunction.execute(CPgSqlParam(filter_sql, params=tuple(params))))
        filter_res_list = self.filter_event(where_condition=where_sql, params=params)
        mlog.info("查询到前一天数据库默认端口类型事件数量：{}".format(len(filter_res_list)))

        # 将事件与告警按省份分别整理
        self.get_csv_data_and_alert_data(filter_res_list)

    def run_key_sercice(self):
        """Web邮箱默认端口类型异常事件"""
        mlog.info("三、Web邮箱默认端口类型异常事件")

        type_list = self.type_str_dic.get("Web邮箱默认端口")
        if type_list is None or not type_list:
            return
        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]

        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
            and up_bytesall >= 104857600
        """
        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by end_time desc"

        filter_res_list = self.filter_event(where_condition=where_sql, params=params)
        mlog.info("查询到前一天Web邮箱默认端口类型事件数量：{}".format(len(filter_res_list)))

        # 将事件与告警按省份分别整理
        self.get_csv_data_and_alert_data(filter_res_list)

    def run_big_flow_judged(self):
        """自动研判未通报-大流量类型异常事件"""
        mlog.info("-四、大流量类型事件")
        type_list = self.type_str_dic.get("大流量")
        if type_list is None or not type_list:
            return
        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]

        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
            and judge_status = 7
            and up_bytesall >= 104857600
        """
        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by end_time desc"

        filter_res_list = self.filter_event(where_condition=where_sql, params=params)

        mlog.info("查询到前一天大流量类型事件数量：{}".format(len(filter_res_list)))

        # 将事件与告警按省份分别整理
        self.get_csv_data_and_alert_data(filter_res_list)

    def run_ip_flow_multiple_judged(self):
        """自动研判未通报-IP流量倍数类型异常事件"""
        mlog.info("五、IP流量倍数类型事件")

        type_list = self.type_str_dic.get("流量倍数")
        if type_list is None or not type_list:
            return
        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]

        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
            and up_bytesall >= 104857600
        """
        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by end_time desc"

        filter_res_list = self.filter_event(where_condition=where_sql, params=params)

        mlog.info("查询到前一天IP与ip对流量倍数异常类型事件数量：{}".format(len(filter_res_list)))

        self.get_csv_data_and_alert_data(filter_res_list)

    def run_ftp_default_port(self):
        """FTP默认端口类型异常事件"""
        mlog.info("六、FTP默认端口类型异常事件")
        type_list = self.type_str_dic.get("FTP默认端口")
        if type_list is None or not type_list:
            return
        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]

        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s and judge_status != 6
            and up_bytesall >= 104857600
        """
        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by end_time desc"

        filter_res_list = self.filter_event(where_condition=where_sql, params=params)

        mlog.info("查询到前一天FTP默认端口异常类型事件数量：{}".format(len(filter_res_list)))

        self.get_csv_data_and_alert_data(filter_res_list)

    # 其他一般类型事件的研判
    def run_nomal_types_judged(self):
        """剩余重点目标类型事件的研判"""
        mlog.info("七、剩余重点目标类型事件的研判")
        # 定义需要获取的事件类型
        event_types = [
            "重点监控目标",
            "扫描行为",
            "非常用端口",
            "远程登录默认端口",
            "Web服务默认端口"
        ]

        # 获取并合并所有类型的ID列表
        type_list = []
        for event_type in event_types:
            type_ids = self.type_str_dic.get(event_type, [])
            if type_ids:
                type_list.extend(type_ids)
                mlog.info("获取到 {} 类型事件: {}".format(
                    event_type,
                    json.dumps(type_ids, ensure_ascii=False)
                ))
            else:
                mlog.warning("{} 类型事件为空".format(event_type))

        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]

        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
        """
        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by end_time desc"

        filter_res_list = self.filter_event(where_condition=where_sql, params=params)

        mlog.info("查询到前一天剩余重点目标类型事件数量：{}".format(len(filter_res_list)))

        self.get_csv_data_and_alert_data(filter_res_list)

    def run_other(self):
        """其他类型异常事件，包括资产"""
        mlog.info("----其他类型异常事件")
        type_list = self.type_str_dic.get("其他")
        if type_list is None or not type_list:
            return

        params = [self.start_timestamp, self.end_timestamp, tuple(type_list)]
        where_sql = """
        WHERE 
            end_time >=%s and end_time <%s  
            and event_type in %s
            and up_bytesall >= 104857600
        """

        filter_sql = self.select_sql() + where_sql
        tag_field_list = [
            "src_info",  # 27
            "dst_info",  # 28
            "src_service",  # 29
            "app_type"
        ]
        filter_sql = ContinuousEventsModel().with_tag_sql(filter_sql, tag_field_list)
        filter_sql += " order by event_type asc, end_time desc"
        filter_res_list = self.filter_event(where_condition=where_sql, params=params)

        mlog.info("查询到前一天其他类型异常事件数量：{}".format(len(filter_res_list)))

        self.get_csv_data_and_alert_data(filter_res_list)

    def get_event_type_tuple(self):
        """获取事件类型分类映射
        Returns:
            dict: 包含各类事件类型ID列表的字典
        """
        # 定义需要获取的事件类型
        EVENT_TYPES = [
            u"重点单位",
            u"数据库默认端口",
            u"Web邮箱默认端口",
            u"大流量",
            u"流量倍数",
            u"FTP默认端口",
            u"重点监控目标",
            u"扫描行为",
            u"非常用端口",
            u"远程登录默认端口",
            u"Web服务默认端口"
        ]

        # 获取所有事件类型
        all_type = self.conf_util.get_cate_type(is_event=True)
        try:
            # 使用字典推导式获取所有类型的ID列表
            type_mapping = {
                event_type: list(set(int(i) for i in all_type.get(event_type, [])))
                for event_type in EVENT_TYPES
            }

            # 获取其他类型（非动态类型减去已知类型）
            # not_dynamic_types = set(self.conf_util.get_not_is_dynamic_type())

            # 获取其他类型(包含资产)
            all_types = set()
            for type_list in all_type.values():
                all_types.update([int(i) for i in type_list])  # 使用update方法展平并添加到集合中
            known_types = set().union(*[set(ids) for ids in type_mapping.values()])
            other_types = [int(i) for i in (all_types - known_types)]

            # 构建最终返回的字典
            result_dict = {
                "重点单位": type_mapping[u"重点单位"],
                "数据库默认端口": type_mapping[u"数据库默认端口"],
                "Web邮箱默认端口": type_mapping[u"Web邮箱默认端口"],
                "大流量": type_mapping[u"大流量"],
                "流量倍数": type_mapping[u"流量倍数"],
                "FTP默认端口": type_mapping[u"FTP默认端口"],
                "重点监控目标": type_mapping[u"重点监控目标"],
                "扫描行为": type_mapping[u"扫描行为"],
                "非常用端口": type_mapping[u"非常用端口"],
                "远程登录默认端口": type_mapping[u"远程登录默认端口"],
                "Web服务默认端口": type_mapping[u"Web服务默认端口"],
                "其他": other_types
            }

            mlog.info("类型统计：{}".format(json.dumps(result_dict, ensure_ascii=False)))
            return result_dict

        except Exception as e:
            mlog.exception("获取事件类型映射失败: {}".format(e))
            # 返回空字典作为默认值
            return {key: [] for key in
                    ["重点单位", "数据库默认端口", "Web邮箱默认端口", "大流量", "流量倍数", "FTP默认端口", "重点监控目标", "扫描行为",
                     "非常用端口", "远程登录默认端口", "Web邮箱默认端口", "其他"]}

    def run(self):
        mlog.info("=============开始执行异常事件推送任务=============")
        try:
            for k, v in self.province_dic.items():
                self.csv_data[k.encode("utf-8")] = self.csv_title()
                self.json_data[k.encode("utf-8")] = []

                self.other_csv_data[k.encode("utf-8")] = self.csv_title()
                self.other_json_data[k.encode("utf-8")] = []

            self.run_key_unit()  # 重点单位--根据key_unit表中的单位去查询
            self.run_key_db_service()  # 数据库默认端口
            self.run_key_sercice()  # web邮箱默认端口
            self.run_big_flow_judged()  # 大流量
            self.run_ip_flow_multiple_judged()  # 流量倍数
            self.run_ftp_default_port()  # ftp默认端口
            self.run_nomal_types_judged()  # 重点目标类型中非特殊查询条件类型

            self.run_other()  # 其他类型--包括资产

            for k, v in self.csv_data.items():
                mlog.info("---------------{}".format(k))
                self.put(k, v, self.json_data.get(k, []))

            for k, v in self.other_csv_data.items():
                mlog.info("---------------{}_其他类型异常事件".format(k))
                self.put(k, v, self.other_json_data.get(k, []), type_name="其他类型")

            # 关闭ssh连接
            self.ssh_.ssh_close()

            # 删除旧文件
            self.delete_old_dir()
            mlog.info("=============异常事件推送任务完成=============")
        except Exception as e:
            mlog.exception("任务出错:{}".format(e))
            mlog.info("=============异常事件推送任务失败=============")


if __name__ == '__main__':
    if len(sys.argv) == 3:
        st = int(sys.argv[1])
        et = int(sys.argv[2])
        mlog.info("开始时间：{}, {}".format(st, datetime.datetime.fromtimestamp(st)))
        mlog.info("结束时间：{}, {}".format(et, datetime.datetime.fromtimestamp(et)))
    else:
        st, et = get_days_ago_00_timestamp(1)

    ProvinceEventToExcel(st, et).run()
