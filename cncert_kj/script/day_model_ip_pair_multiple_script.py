#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
IP对流量倍数模型
IP对流量倍数模型实现逻辑：
第一步计算均值：查询计算最近15天所有重点监控单位IP每个IP对每天流出流量平均值，计算公式类似：(sum(out_bytes) group by sip,dip))/15，以此结果作为基线（这里我们有个阈值的下限设置是20MB，意思是IP对平均每天的流量低于20MB就忽略掉，不会纳入到基线。 另外基线会每天进行更新，算最近15天的）
第二步检测告警：查询上述统计的基线IP中每个IP对的前一天流出流量，如果大于基线流量的5倍，则产生IP对告警
每天零点01分执行一次
用于生成 IP对流量倍数告警数据，均值是通过IP总流量/对端IP个数得到的
"""
import datetime
import hashlib
import json
import sys
import time
import traceback
import uuid

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.static_baseline_model import get_dynamic_baseline_threshold, get_dynamic_baseline_status
from cncert_kj.script.alert_day_model.day_filter_rule import get_filter_rule
from cncert_kj.script.event_asset_info import GetAssetInfo
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.script.update_cont_event import update_tllog_sql
from cncert_kj.utils import es_utils, conf_util
from cncert_kj.utils import logger
from cncert_kj.utils import net
from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_filing_sys import RequestFilingSys

reload(sys)
sys.setdefaultencoding('utf-8')

mlog = logger.init_logger('day_model_ip_pair_multiple_script')


class MultipleAlarmSampling:

    def __init__(self):
        self.max_buckets = 60000
        self.day = 15
        # 15天的流量均值大于 10mb ， 那么15天的流量总值就要大于150mb    10MB  5倍
        # self.threshold = 1024 * 1024 * 20
        self.detect_name = "IP对流量倍数异常告警"

        self.multiple = 5  # 倍数
        self.analysis_tech = 2  # 动态基线
        self.type = 52  # 告警类型

        self.threshold = 1024 * 1024 * 10  # 10 MB 1024 * 1024 * 10
        threshold = get_dynamic_baseline_threshold(self.type, belong=2)
        self.threshold = threshold if threshold else self.threshold
        self.threshold_fifteen = self.threshold * self.day
        mlog.info("类型：{}； 阈值：{}， threshold_fifteen：{}".format(self.type, self.threshold, self.threshold_fifteen))

        self.tllog_fields = ','.join((
            "uuid",
            "name",
            "type",
            "start_time",
            "end_time",
            "sip",
            "src_region",
            "src_operator",
            "src_iot",
            "src_service",
            "src_unit",
            "src_com",
            "sport",
            "dip",
            "dst_region",
            "dst_operator",
            "dst_iot",
            "dst_service",
            "dst_unit",
            "dst_com",
            "dport",
            "up_bytes_all",
            "down_bytes_all",
            "up_packets_all",
            "down_packets_all",
            "flow_list",
            "analysis_tech",
            "ipv6",
            "port_distribution",
            "flow_logs"
        ))
        self.tllog_insert_sql_template = """
        INSERT INTO internal_app_bsa_gjk.traffic_alert ({fields}) VALUES ({params})
        """

    def get_key_unit(self):
        """
        查询境内目标重点单位以及其IP
        :return:
        """
        query_sql = """
            SELECT  name, ip_range FROM internal_app_bsa_gjk.key_unit WHERE boundary = 2 and tclog_dynamic_baseline=true
        """
        result = json.loads(CFunction.execute(CPgSqlParam(query_sql)))
        ip_list = []
        for item in result:
            asset_name = item[0]
            ip_range = item[1]
            try:
                if not ip_range:
                    continue
                asset_ips = net.ips_to_list(ip_range.split(","))
                ip_list.extend([net.ip_to_int32(i.strip()) for i in asset_ips if i])
            except Exception as _:
                mlog.error("存在错误ip，无法进行转换, asset_name: {} , ip_range: {}".format(asset_name, ip_range))
                mlog.error(traceback.format_exc())
                continue
        return ip_list

    def generate_dsl(self, asset_ip, start_time, end_time):
        """
        生成DSL
        """
        ASSET_IP_SIZE = 1
        PEER_IP_SIZE = self.max_buckets / ASSET_IP_SIZE
        query_dsl = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "term": {
                                "asset_ip": net.ip_to_int32(asset_ip)
                            }
                        },
                        {
                            "range": {
                                "end_time": {
                                    "gte": start_time,
                                    "lt": end_time
                                }
                            }
                        }
                    ]
                }
            },
            "size": 0,
            "aggregations": {
                "peer_ip": {
                    "aggregations": {
                        "out_bytes": {
                            "sum": {
                                "field": "out_bytes"
                            }
                        },
                        "in_bytes": {
                            "sum": {
                                "field": "in_bytes"
                            }
                        },
                        "start_time": {
                            "min": {
                                "field": "start_time"
                            }
                        },
                        "dport_hits": {
                            "top_hits": {
                                "size": 1,
                                "_source": {
                                    "includes": [
                                        "dport"
                                    ]
                                }
                            }
                        }
                    },
                    "terms": {
                        "field": "peer_ip",
                        "size": PEER_IP_SIZE,
                        "order": {
                            "out_bytes": "desc"
                        }
                    }
                }
            }
        }
        # mlog.info("-------------------------{}".format(query_dsl))
        return query_dsl

    def search_es_max_out_bytes_ip(self):
        """
        查询每个IP的最大IP对流量大于15天阈值的数据IP
        :return: {ip: 一个assetip的15天最大的对端IP的均值}
        """
        data = {}
        try:
            ip_list = self.get_key_unit()
            es = es_utils.ESUtil()
            day_timestamp = 86400
            today = datetime.date.today()
            end_time = int(time.mktime(today.timetuple()))
            start_time = int(time.mktime(today.timetuple())) - self.day * day_timestamp
            SIZE = 10000

            n = 0
            count = 0
            for i in range(0, len(ip_list), SIZE):
                n += 1
                mlog.info("查询第{}批， 数量:{}， 开始时间:{}".format(n, len(ip_list[i:i + SIZE]), time.time()))
                query_dsl = {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "terms": {
                                        "asset_ip": ip_list[i:i + SIZE]
                                    }
                                },
                                {
                                    "range": {
                                        "end_time": {
                                            "gte": start_time,
                                            "lt": end_time
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "size": 0,
                    "aggregations": {
                        "asset_ip": {
                            "terms": {
                                "field": "asset_ip",
                                "size": SIZE
                            },
                            "aggregations": {
                                "peer_ip": {
                                    "aggregations": {
                                        "out_bytes": {
                                            "sum": {
                                                "field": "out_bytes"
                                            }
                                        }
                                    },
                                    "terms": {
                                        "field": "peer_ip",
                                        "size": 1,
                                        "order": {
                                            "out_bytes": "desc"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                try:
                    result = es.search_es(conf_util.TLLOG_IP_PAIR_5MIN, query_dsl)
                except Exception as er:
                    mlog.error("查询每个IP的最大IP对流量大于15天阈值的数据失败：{}, query_dsl:{}".format(er, query_dsl))
                    continue

                buckets = result.get("aggregations", {}).get("asset_ip", {}).get("buckets", [])
                for item in buckets:
                    asset_ip = net.int32_to_ip(item.get("key"))
                    out_bytes = item.get("peer_ip", {}).get("buckets", [{}])[0].get("out_bytes", {}).get("value", 0)
                    if out_bytes > self.threshold_fifteen:
                        # 保留流出流量 大于 (15天 * 阈值) 的数据
                        count += 1
                        data[asset_ip] = out_bytes / self.day
                mlog.info("第{}批查询结束， 结束时间:{}".format(n, time.time()))
            mlog.info("最大ip对流量超过150MB的ip有：{}个".format(count))
            return data
        except Exception as e:
            mlog.error("查询每个IP的最大IP对流量大于15天阈值的数据失败：{}".format(e))
            mlog.error(traceback.format_exc())
            return data

    def es_data(self, result, avg_out_bytes, asset_ip):
        data = {}
        buckets = result.get("aggregations", {}).get("peer_ip", {}).get("buckets", [])
        for bucket in buckets:
            # 流出流量
            out_bytes = bucket.get("out_bytes", {}).get("value", 0)
            if out_bytes < avg_out_bytes * self.multiple:
                # 结果按照 out_bytes 倒排，出现流出小于均值 * 倍数的 后面的则都不符合， 直接结束循环
                break
            peer_ip = bucket.get("key")
            # mlog.info("-----------------------------asset_ip:{}".format(asset_ip))
            asset_ip_ = net.ip_to_int32(asset_ip)
            # mlog.info("================asset_ip,:{} , peer_ip:{}".format(asset_ip_, peer_ip))
            key = md5(asset_ip_, peer_ip)
            start_time = bucket.get("start_time", {}).get("value", 0)
            _source = bucket['dport_hits']["hits"]["hits"][0]["_source"]
            dport = _source.get("dport", "") if _source else ""
            in_bytes = bucket.get("in_bytes").get("value")
            ip_pair_dat = {
                "asset_ip": asset_ip_,
                "peer_ip": peer_ip,
                "in_bytes": in_bytes,
                "out_bytes": out_bytes,
                "start_time": start_time,
                "dport": dport,
                "avg_out_bytes": avg_out_bytes
            }
            data[key] = ip_pair_dat
        return data

    def select_es_data(self, start_time, end_time):
        """
            按照单位的ip数量来查询，
            下方ip均为int类型
        """
        n = 0
        s_time = time.time()
        data = {}
        key_unit_data = self.get_ip_pair_avg()
        es = es_utils.ESUtil()
        # 下方的 out_bytes 是均值
        for asset_ip, out_bytes in key_unit_data.items():
            n += 1
            mlog.info("当前查询第{}个, IP：{} ".format(n, asset_ip))
            try:
                dsl = self.generate_dsl(asset_ip, start_time=start_time, end_time=end_time)
                begin_time = time.time()
                result = es.search_es(conf_util.TLLOG_IP_PAIR_5MIN, dsl)
                stop_time = time.time()
                if result:
                    data.update(self.es_data(result, out_bytes, asset_ip))
                mlog.info("IP：{} ,查询完成, 现在数据数量为：{}".format(asset_ip, len(data)))
                mlog.info("本次查询耗时：{} ".format(stop_time - begin_time))

            except Exception as e:
                mlog.error("IP：{} , 查询ES报错：{}".format(asset_ip, e))
                mlog.error(traceback.format_exc())
                es = es_utils.ESUtil()
                mlog.info("重试查询, IP：{} ".format(asset_ip))
                try:
                    dsl = self.generate_dsl(asset_ip, start_time=start_time, end_time=end_time)
                    result = es.search_es(conf_util.TLLOG_IP_PAIR_5MIN, dsl)
                    if result:
                        data.update(self.es_data(result, out_bytes, asset_ip))
                    mlog.info("IP：{} ,重试查询成功, 现在数据数量为：{}".format(asset_ip, len(data)))
                except Exception as ee:
                    mlog.error("IP：{} , 重试查询ES报错：{}".format(asset_ip, ee))

        e_time = time.time()
        mlog.info("执行时间：{} 秒, 数据条数：{}".format(e_time - s_time, len(data)))
        return data

    def get_last_day_flow_logs(self, ip_pair, ip_details):
        """
        获取前一天的flow_logs
        :param ip_pair:
        :param ip_details:
        :return:
        """
        try:
            index_name = conf_util.TLLOG_IP_PAIR_5MIN

            today = datetime.date.today()
            start_time = int(time.mktime(today.timetuple())) - 86400
            end_time = int(time.mktime(today.timetuple()))

            es = es_utils.ESUtil()
            res_data = {}
            for key, val in ip_pair.items():
                asset_ip = val.get("asset_ip")
                peer_ip = val.get("peer_ip")
                query_dsl = {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "term": {
                                        "asset_ip": asset_ip
                                    }
                                },
                                {
                                    "term": {
                                        "peer_ip": peer_ip
                                    }
                                },
                                {
                                    "range": {
                                        "end_time": {
                                            "gte": start_time,
                                            "lt": end_time
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "size": 10
                }

                mlog.info('DSL : {}'.format(query_dsl))
                try:
                    result = es.search_es(index_name, query_dsl)
                except Exception as e:
                    mlog.error("查询失败：{}; query_dsl:{}".format(e, query_dsl))
                    continue
                if not result and result.get("hits", {}).get("hits", []):
                    continue
                res_data[key] = self.format_flow_logs(result.get("hits", {}).get("hits", []), ip_details)
            return res_data
        except Exception as e:
            mlog.error("flow_logs查询失败：{}".format(e))
            mlog.error(traceback.format_exc())
            return {}

    def format_flow_logs(self, data, ip_details):
        """
        将es查询处理的ip pair 数据处理为 flow_logs 格式
        :param ip_details:
        :param data:
        :return:
        """
        flow_logs = []
        for i in data:
            source = i.get("_source")
            src_ip = source.get("asset_ip")
            dst_ip = source.get("peer_ip")

            src_ip_detail = ip_details.get(src_ip, {})
            src_com = src_ip_detail.get("user")
            src_oper = src_ip_detail.get("operator")

            dst_ip_detail = ip_details.get(dst_ip, {})
            dst_com = dst_ip_detail.get("user")
            dst_oper = dst_ip_detail.get("operator")

            flow_logs.append(
                {
                    "src_ip": src_ip,
                    "sport": source.get("sport", ""),
                    "dst_ip": dst_ip,
                    "dport": source.get("dport", ""),
                    "up_packets": source.get("out_packets"),
                    "up_bytes": source.get("out_bytes"),
                    "down_packets": source.get("in_packets"),
                    "down_bytes": source.get("in_bytes"),
                    "c_log_time": source.get("start_time"),
                    "stream_time": source.get("start_time"),
                    "c_s_boundary": source.get("asset_boundary"),
                    "c_d_boundary": source.get("peer_boundary"),
                    "src_country": source.get("asset_country"),
                    "src_province": source.get("asset_province"),
                    "src_city": source.get("asset_city", ""),
                    "src_oper": src_oper,
                    "src_com": src_com,
                    "dst_country": source.get("peer_country"),
                    "dst_province": source.get("peer_province"),
                    "dst_city": source.get("peer_city", ""),
                    "dst_oper": dst_oper,
                    "dst_com": dst_com,
                    "app": source.get("app_protocol", ""),
                    "hostr": "",
                    "protocol": source.get("protocol", ""),
                    "src_mark1": "",
                    "src_mark2": "",
                    "src_mark3": source.get("asset_name", ""),
                    "dst_mark1": "",
                    "dst_mark2": "",
                    "dst_mark3": ""
                }
            )
        return flow_logs

    def get_ip_pair_avg(self):
        """
        获取IP对的均值， 天均值超过阈值的数据
        :return:
        """
        data = {}
        try:
            ip_list = self.get_key_unit()
            es = es_utils.ESUtil()
            day_timestamp = 86400
            today = datetime.date.today()
            end_time = int(time.mktime(today.timetuple()))
            start_time = int(time.mktime(today.timetuple())) - self.day * day_timestamp
            SIZE = 1000

            n = 0
            count = 0
            for i in range(0, len(ip_list), SIZE):
                n += 1
                mlog.info("查询第{}批， 数量:{}， 开始时间:{}".format(n, len(ip_list[i:i + SIZE]), time.time()))
                query_dsl = {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "terms": {
                                        "asset_ip": ip_list[i:i + SIZE]
                                    }
                                },
                                {
                                    "range": {
                                        "end_time": {
                                            "gte": start_time,
                                            "lt": end_time
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "size": 0,
                    "aggregations": {
                        "asset_ip": {
                            "aggregations": {
                                "out_bytes": {
                                    "sum": {
                                        "field": "out_bytes"
                                    }
                                },
                                "pair_ip_count": {
                                    'cardinality': {
                                        "field": "peer_ip"
                                    }
                                }
                            },
                            "terms": {
                                'field': "asset_ip",
                                "size": SIZE
                            }
                        }
                    }
                }
                try:
                    result = es.search_es(conf_util.TLLOG_IP_PAIR_5MIN, query_dsl)
                except Exception as er:
                    mlog.error("查询失败：{}, query_dsl:{}".format(er, query_dsl))
                    continue
                if not result:
                    continue
                buckets = result.get("aggregations", {}).get("asset_ip", {}).get("buckets", [])
                for item in buckets:
                    asset_ip = net.int32_to_ip(item.get("key"))
                    out_bytes = item.get("out_bytes", {}).get("value", 0)
                    pair_ip_count = item.get("pair_ip_count", {}).get("value", 0)

                    if out_bytes and pair_ip_count:
                        avg_flow = out_bytes / pair_ip_count / self.day
                        if avg_flow <= self.threshold:
                            continue
                        count += 1
                    else:
                        continue
                    data[asset_ip] = avg_flow
                mlog.info("第{}批查询结束， 结束时间:{}".format(n, time.time()))
            mlog.info("ip对流量均值超过阈值的ip有：{}个".format(count))
        except Exception as e:
            mlog.error("查询每个IP的IP对流量均值大于阈值的数据失败：{}".format(e))
            mlog.error(traceback.format_exc())
        return data

    def generate_alert(self, one_day_data):
        """
        生成告警sql参数
        :return:
        """
        params_list = []
        new_params_list = []
        ip_pair = {}
        ip_list = []
        # key 是更加asset_ip和peer_ipMD5生成的数据的key
        for key, value in one_day_data.items():
            # 如果有数据
            if value:
                asset_ip = value.get("asset_ip")  # 重点单位IP
                peer_ip = value.get("peer_ip")  # 对端IP
                start_time = value.get("start_time")  # ip对数据的最小开始时间
                end_time = value.get("end_time")
                dst_port = value.get("dport", -1)  # 对端端口
                one_day_in_bytes = value.get("in_bytes")  # 前一天流量
                one_day_out_bytes = value.get("out_bytes")  # 前一天流量
                fifteen_day_out_bytes = value.get("avg_out_bytes")  # 15天流量均值
                alert_name = "重点监控IP对, 流出流量均值为{out_avg},当前流出流量为{out_bytes}, 超过均值的{multiple}倍。".format(
                    out_avg=long2unit(fifteen_day_out_bytes), out_bytes=long2unit(one_day_out_bytes),
                    multiple=self.multiple
                )
                params_list.append({
                    "key": key,
                    "asset_ip": net.int32_to_ip(asset_ip),
                    "peer_ip": net.int32_to_ip(peer_ip),
                    # "asset_name": asset_name,
                    "name": alert_name,
                    "uuid": str(uuid.uuid4()),
                    "end_time": end_time,
                    "out_bytes": one_day_out_bytes,
                    "in_bytes": one_day_in_bytes,
                    "start_time": start_time,
                    "dst_port": dst_port
                })
                ip_list.append(asset_ip)
                ip_list.append(peer_ip)
                # 用来查flow_logs
                ip_pair[key] = {
                    "asset_ip": asset_ip,
                    "peer_ip": peer_ip
                }

        if not params_list:
            return new_params_list

        return self.handle_params(params_list, ip_list, ip_pair)

    def handle_params(self, params_list, ip_list, ip_pair):
        new_params_list = []
        with RequestFilingSys() as request_filing_obj:
            ip_details = request_filing_obj.query_icp_api(ip_list)
        # ip_details = {}
        # 有数据，查flow_logs
        flow_logs = self.get_last_day_flow_logs(ip_pair, ip_details)
        rules = get_filter_rule()
        if not rules:
            rules = {}
        src_com_filter = set(rules.get("2", []))
        ip_filter = set(rules.get("1", []))
        ip_port_filter = set(rules.get("3", []))
        for params in params_list:
            key = params.get("key")
            asset_ip = params.get("asset_ip")
            peer_ip = params.get("peer_ip")
            dst_port = params.get("dst_port")
            src_port = params.get("src_port")
            asset_ip_detail = ip_details.get(asset_ip, {})
            peer_ip_detail = ip_details.get(peer_ip, {})
            params["src_region"] = asset_ip_detail.get("region", "")
            params["src_operator"] = asset_ip_detail.get("operator", "")
            params["src_com"] = asset_ip_detail.get("user", "")

            params["dst_region"] = peer_ip_detail.get("region", "")
            params["dst_operator"] = peer_ip_detail.get("operator", "")
            params["dst_com"] = peer_ip_detail.get("user", "")
            if params['src_com'] in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(params['src_com']))
                continue
            if params['dst_com'] in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(params['dst_com']))
                continue
            if asset_ip + '-' + peer_ip in ip_filter:
                mlog.info("境内外IP：{}-{}， 已下发为白名单事件过滤规则".format(asset_ip, peer_ip))
                continue
            if peer_ip + '-' + asset_ip in ip_filter:
                mlog.info("境内外IP：{}-{}， 已下发为白名单事件过滤规则".format(peer_ip, asset_ip))
                continue
            if asset_ip + ':' + str(src_port) in ip_port_filter:
                mlog.info("境内外IP：{}:{}， 已下发为白名单事件过滤规则".format(asset_ip, src_port))
                continue
            if peer_ip + ':' + str(dst_port) in ip_port_filter:
                mlog.info("境内外IP：{}:{}， 已下发为白名单事件过滤规则".format(peer_ip, dst_port))
                continue
            flow_log = flow_logs.get(key, [])
            params["flow_log"] = "|".join([json.dumps(log, ensure_ascii=False) for log in flow_log])
            if not flow_log:
                flow_log = [{}]
            if is_ipv4_correct(asset_ip):
                ipv6 = False
            else:
                ipv6 = True
            asset_name = flow_log[0].get("src_mark3")
            new_params_list.append([
                params.get("uuid"),
                params.get("name"),
                self.type,
                params.get("start_time"),
                params.get("end_time"),
                params.get("asset_ip"),
                params.get("src_region"),
                params.get("src_operator"),
                "",  # src_iot
                "",  # src_service
                asset_name,
                params.get("src_com"),
                -1,  # sport
                params.get("peer_ip"),
                params.get("dst_region"),
                params.get("dst_operator"),
                "",  # dst_iot
                "",  # dst_service
                "",  # dst_unit
                params.get("dst_com"),
                params.get("dst_port", -1),
                params.get("out_bytes"),
                params.get("in_bytes", 0),
                0,  # up_packets_all
                0,  # down_packets_all
                "",  # flow_list
                self.analysis_tech,
                ipv6,
                3,
                params.get("flow_log")
            ])
        """
        数据写入文件验证
        with open("params.txt", "w") as f:
            f.write(json.dumps(new_params_list, indent=4, ensure_ascii=False))
        """
        return new_params_list
    def insert_alert(self, params_list):
        """
        插入数据库
        :param params_list:
        :return:
        """
        uuid_list = []
        try:
            for i in params_list:
                uuid_list.append(i[0])
                val = ",".join(len(i) * ["%s"])
                CFunction.execute(
                    CPgSqlParam(self.tllog_insert_sql_template.format(fields=self.tllog_fields, params=val),
                                params=tuple(i)))
            mlog.info("生成通联倍数告警数量：{}".format(len(params_list)))
        except Exception as e:
            mlog.error("告警数据入库失败：{}".format(e))
            mlog.error(traceback.format_exc())
        return uuid_list

    def run(self):
        # 只有通联的
        tc_status = get_dynamic_baseline_status(self.type, 2)
        if not tc_status:
            mlog.info("通联IP对流量倍数告警未开启")
            return

        begin_time = time.time()
        today = datetime.date.today()
        end_time = int(time.mktime(today.timetuple()))
        mlog.info("=========================================================================")
        # 查前一天的数据
        one_day_data = self.select_es_data(end_time - 60 * 60 * 24, end_time)
        mlog.info("=========================================================================")
        # 生成告警的参数、flow_logs、源目信息增强
        params = self.generate_alert(one_day_data)
        mlog.info("告警参数数量：{}".format(len(params)))
        # 入库
        if params:
            uuid_list = self.insert_alert(params)
            if uuid_list:
                if len(uuid_list) == 1:
                    tc_filter_condition = "uuid = '{}'".format(uuid_list[0])
                else:
                    tc_filter_condition = "uuid in {}".format(tuple(uuid_list))
                update_tllog_sql(tc_filter_condition)
        finish_time = time.time()
        mlog.info("任务总耗时：{}".format(finish_time - begin_time))


def md5(asset_ip, peer_ip):
    """
    根据 重点监控ip和对端ip 进行 md5
    """
    combined_string = "{}-{}".format(asset_ip, peer_ip)
    md5_hash = hashlib.md5()
    md5_hash.update(combined_string.encode('utf-8'))
    key = md5_hash.hexdigest()
    return key


def get_detect_rule(belong, detect_name):
    try:
        threshold = 0
        query_sql = "SELECT rule_id, threshold FROM internal_app_bsa_gjk.detect_rule WHERE belong=%s and detect_name=%s"
        result = json.loads(CFunction.execute(CPgSqlParam(query_sql, params=(belong, detect_name))))
        if result:
            threshold = result[0][1]
        return threshold
    except Exception as _:
        mlog.error(traceback.format_exc())
        return 0


if __name__ == '__main__':
    mas = MultipleAlarmSampling()
    mas.run()
    analysis_tech = 2
    today = datetime.date.today()
    stime = int(time.mktime(today.timetuple())) - 86400
    etime = int(time.mktime(today.timetuple()))
    mlog.info("开始增强资产信息")
    GetAssetInfo(analysis_tech=analysis_tech, start_time=stime, end_time=etime,
                 event_type=[52]).enhance_event_asset_info()
    mlog.info("开始增强恶意标签信息")
    EventMaliciousTag().run(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[52])
    mlog.info("开始增强情报信息")
    enhance_event_intelligence_info(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[52])
