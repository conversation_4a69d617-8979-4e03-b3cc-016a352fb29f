#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    卸载
"""
import logging
import os
import sys
import traceback

from appManager.module.global_api import ComponentHelper
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction

reload(sys)
sys.setdefaultencoding('utf8')

APP_NAME = "cncert_kj"

BASE_DIR = os.environ["BASE_DIR"]
PATH_BSA_APP = os.path.join(BASE_DIR, "ISOP/apps", APP_NAME)
PATH_BSA_STATIC = os.path.join(BASE_DIR, 'ISOP/static', APP_NAME)

# /home/<USER>/logs/peafowls_uninstall.log
PATH_LOG = BASE_DIR + '/logs/' + APP_NAME + '_uninstall.log'


def get_logger_uninstall(logger_name, logger_level=logging.INFO):
    """
        为了保障在缺失其他文件的情况下，卸载文件能正常运行，需尽量减少对其他部分的依赖，所以日志不采用引入的方式而是新写一个
        安装/升级 日志
    """
    import logging.handlers
    logger = logging.getLogger(logger_name)
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(filename)s %(funcName)s:%(lineno)d %(message)s')
    logger.setLevel(logger_level)
    logger_file = os.path.normpath('/home/<USER>/logs')
    if not os.path.exists(logger_file):
        os.mkdir(logger_file)
    if not logger.handlers:
        file_handler = logging.handlers.TimedRotatingFileHandler(logger_name, 'midnight')
        file_handler.suffix = "%Y-%m-%d"
        file_handler.setLevel(logger_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    return logger


logger = get_logger_uninstall(PATH_LOG)


def check_file_without_root(*dir_path_list):
    """
        检查目录中是否有root权限的文件
    """
    for dir_path in dir_path_list:
        cmd = "find %s -user root" % dir_path
        cmd_res = os.popen(cmd)
        res = cmd_res.read().strip()
        if res:
            msg = "发现路径 %s 中有root用户的文件：\n%s！" % (dir_path, res)
            logger.error(msg)
            raise ValueError(msg)
        logger.info("目录%s检查通过" % dir_path)
    logger.info("检查root权限的文件完成，无异常")


def remove_task(app_name):
    """
    组件批量注销任务
    """
    ch = ComponentHelper(app_name)
    ch.remove_task()
    logger.info("定时任务注销成功")


def remove_right(app_name):
    """
    删除菜单项
    """
    from dataInterface.db.params import CPgSqlParam
    from dataInterface.functions import CFunction
    del_sql = "delete from internal_app_permission.tb_right_info where app_name='%s'" % app_name
    CFunction.execute(CPgSqlParam(del_sql))
    logger.info("删除菜单项成功")


def uninstall_db_schema(app_name):
    """
        删除app 数据库 删除数据
    """
    from dataInterface.db.params import CPgSqlParam
    from dataInterface.functions import CFunction
    del_sql = CPgSqlParam("drop schema if exists internal_app_%s cascade" % app_name)
    CFunction.execute(del_sql)
    logger.info("删库成功")


if __name__ == '__main__':
    try:
        """
        需要卸载信息有：删除后端文件，删除前端文件，删除从数据库，删除任务，还原菜单

        卸载失败的可能原因： 代码文件存在root文件，python无权限

        删除代码文件 平台自动删
        """
        logger.info(">>>开始卸载！！！")
        logger.info("1、检查目录中是否有root权限的文件……")
        check_file_without_root(PATH_BSA_APP, PATH_BSA_STATIC)

        logger.info("2、开始注销定时任务")
        remove_task(APP_NAME)

        logger.info("3、开始删除菜单项……")
        remove_right(APP_NAME)
        # >> 删除数据库
        # logger.info("4、开始删除数据库")
        # uninstall_db_schema("bsa_gjk")

        logger.info(">>>卸载结束！！！")
        sys.exit(0)
    except Exception as e:
        logger.error(traceback.format_exc())
        sys.exit(4)
