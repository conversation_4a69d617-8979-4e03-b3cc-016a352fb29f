# -*- coding: utf-8 -*-
import sys

reload(sys)
sys.setdefaultencoding('utf8')

import os
import sys


def get_pg_info():
    pg_conf_info = dict()
    try:
        from .appsUtils import confutil
        conf_obj = confutil.ConfUtil()
        return conf_obj.getPostgresqlConf()
    except Exception as e:
        print(str(e))
    return pg_conf_info


def get_time_zone():
    time_zone = "Asia/Shanghai"
    try:
        from internation.models import get_time_zone_name
        time_zone = get_time_zone_name()
    except Exception as e:
        print(str(e))
    return time_zone


ISOP_PG_CONF_INFO = get_pg_info()

PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__).decode('utf-8')).replace('\\', '/')
sys.path.insert(0, os.path.join(PROJECT_ROOT, 'apps'))
sys.path.insert(0, os.path.join(PROJECT_ROOT, 'libs/Python/'))
sys.path.insert(0, os.path.join(PROJECT_ROOT, 'etl/python/'))
DEBUG = False
TEMPLATE_DEBUG = DEBUG

ADMINS = (
    # ('Your Name', '<EMAIL>'),
)

MANAGERS = ADMINS
try:
    from cncert_kj.conf.constant import db_username, db_ip, db_password, db_port, db_database
except Exception:
    db_database = ISOP_PG_CONF_INFO.get('database', 'nsc')
    db_username = ISOP_PG_CONF_INFO.get('username', 'nsc')
    db_password = ISOP_PG_CONF_INFO.get('password', 'nsc')
    db_ip = ISOP_PG_CONF_INFO.get('ip', '127.0.0.1')
    db_port = ISOP_PG_CONF_INFO.get('port', '5432')
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        # Add 'postgresql_psycopg2', 'mysql', 'sqlite3' or 'oracle'.
        'NAME': db_database,  # Or path to database file if using sqlite3.
        # The following settings are not used with sqlite3:
        'USER': db_username,
        'PASSWORD': db_password,
        'HOST': db_ip,
        # Empty for localhost through domain sockets or '127.0.0.1' for localhost through TCP.
        'PORT': db_port,  # Set to empty string for default.
    }
}

# Hosts/domain names that are valid for this site; required if DEBUG is False
# See https://docs.djangoproject.com/en/1.5/ref/settings/#allowed-hosts
# localIP = os.getenv('IP')
# print "get local IP:",localIP
# ALLOWED_HOSTS = [localIP]
ALLOWED_HOSTS = ['*']

# Local time zone for this installation. Choices can be found here:
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# although not all choices may be available on all operating systems.
# In a Windows environment this must be set to your system time zone.
TIME_ZONE = get_time_zone()

# Language code for this installation. All choices can be found here:
# http://www.i18nguy.com/unicode/language-identifiers.html
LANGUAGE_CODE = 'zh-hans'

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = True

# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale.
USE_L10N = True

# If you set this to False, Django will not use timezone-aware datetimes.
USE_TZ = True

# translate dir
LOCALE_PATHS = [
    os.path.join(PROJECT_ROOT, 'locale'),
]

# Absolute filesystem path to the directory that will hold user-uploaded files.
# Example: "/var/www/example.com/media/"
MEDIA_ROOT = ''

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash.
# Examples: "http://example.com/media/", "http://media.example.com/"
MEDIA_URL = ''

# Absolute path to the directory static files should be collected to.
# Don't put anything in this directory yourself; store your static files
# in apps' "static/" subdirectories and in STATICFILES_DIRS.
# Example: "/var/www/example.com/static/"
STATIC_ROOT = os.path.join(PROJECT_ROOT, 'static')

# URL prefix for static files.
# Example: "http://example.com/static/", "http://static.example.com/"
STATIC_URL = '/WebApi/'

# Additional locations of static files
STATICFILES_DIRS = (
    # Put strings here, like "/home/<USER>/static" or "C:/www/django/static".
    # Always use forward slashes, even on Windows.
    # Don't forget to use absolute paths, not relative paths.
)


# Add each app's static dir to STATICFILES_DIRS


def add_appstatic():
    try:
        global STATICFILES_DIRS
        staticfiles_dirs_tmp = []
        apps_path = os.path.join(PROJECT_ROOT, 'apps')
        www_dir = os.path.join(PROJECT_ROOT, 'www')
        app_dirs = os.listdir(apps_path)
        for app in app_dirs:
            try:
                app_abspath = os.path.join(apps_path, app)
                app_static_abspath = os.path.join(app_abspath, 'static')
                if os.path.isdir(app_abspath) and \
                        os.path.isdir(app_static_abspath):
                    app_static_prefix = os.path.join(app, 'static')
                    staticfiles_dirs_tmp.append((app_static_prefix,
                                                 app_static_abspath))
            except Exception:
                print('%s staticfiles dir add failed!' % (app))
        staticfiles_dirs_tmp.append(www_dir)
        STATICFILES_DIRS = tuple(staticfiles_dirs_tmp)
    except Exception:
        # >> STATICFILES_DIRS = (www_dir)
        STATICFILES_DIRS = ()
        print('all apps staticfiles dir add failed!')

_CLASS = 'logging.handlers.RotatingFileHandler'
_CLASS2 = 'isoploghandler.ISOPLogHandler'
_CLASS3 = "django_redis.client.DefaultClient"
_CLASS4 = "django_redis.cache.RedisCache"
def add_locale():
    try:
        global LOCALE_PATHS
        locale_dirs_tmp = []
        apps_path = os.path.join(PROJECT_ROOT, 'apps')
        app_dirs = os.listdir(apps_path)
        for app in app_dirs:
            try:
                app_abspath = os.path.join(apps_path, app)
                app_locale_abspath = os.path.join(app_abspath, 'locale')
                if os.path.isdir(app_abspath) and \
                        os.path.isdir(app_locale_abspath):
                    app_static_prefix = os.path.join(app_abspath, 'locale')
                    locale_dirs_tmp.append(app_static_prefix)
            except Exception:
                print('%s locale_dir dir add failed!' % (app))
        locale_dirs_tmp.append(os.path.join(PROJECT_ROOT, 'locale'))
        LOCALE_PATHS = list(locale_dirs_tmp)
    except Exception:
        # >> STATICFILES_DIRS = (www_dir)
        LOCALE_PATHS = []
        print('all apps locale dir add failed!')


add_appstatic()

add_locale()

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
)

# Make this unique, and don't share it with anybody.
SECRET_KEY = 'oi21&y_^j!=d*_!_d_$9!2*rzbnp#1l$q=vhnvrj*v!_b==nve'

# List of callables that know how to import templates from various sources.
TEMPLATE_LOADERS = (
    'django.template.loaders.filesystem.Loader',
    'django.template.loaders.app_directories.Loader',
)

MIDDLEWARE_CLASSES = (
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'middleware.static_middleware.StaticAuthMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'middleware.UrlfilterMiddleware.UrlfilterMiddleware',
    'middleware.online_user.OnlineUserMiddleware',
)

# webservice middleware
try:
    __import__('north_interface.middleware.token_filter')
    MIDDLEWARE_CLASSES += ('north_interface.middleware.token_filter.WebServiceTokenFilter',)
except Exception:
    pass

ROOT_URLCONF = 'ISOP.urls'

# Python dotted path to the WSGI application used by Django's runserver.
WSGI_APPLICATION = 'ISOP.wsgi.application'

PROJECT_BASE_DIR = os.path.dirname(__file__)

TEMPLATE_DIRS = (
    # Put strings here, like "/home/<USER>/django_templates" or "C:/www/django/templates".
    # Always use forward slashes, even on Windows.
    # Don't forget to use absolute paths, not relative paths.
    os.path.join(PROJECT_BASE_DIR, 'templates'),
    os.path.join(PROJECT_BASE_DIR, 'apps'),
    # os.path.join(PROJECT_BASE_DIR, 'www'),
)

INSTALLED_APPS = (
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
)

SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'

SESSION_COOKIE_SECURE = True

CSRF_COOKIE_SECURE = True

# config the session parameters
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
# A sample logging configuration. The only tangible logging
# performed by this configuration is to send an email to
# the site admins on every HTTP 500 error when DEBUG=False.
# See http://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.

REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S'
}

try:
    if "BASE_DIR" in os.environ.keys():
        os.environ["JAVA_HOME"] = os.environ["BASE_DIR"] + "/jdk"
        os.environ["KAFKA_HOME"] = os.environ["BASE_DIR"] + "/kafka"
        os.environ["POSTGRESQL_HOME"] = os.environ["BASE_DIR"] + "/postgresql-9.6.2"
        LOGROOT = os.environ["BASE_DIR"]
        appsUtilsPath = os.path.normpath(PROJECT_ROOT + "/appsUtils")
        sys.path.insert(0, appsUtilsPath)

        if not os.path.exists(LOGROOT + "/" + "logs"):
            os.mkdir(LOGROOT + "/" + "logs")

        LOGGING = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'verbose': {
                    'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
                },
                'standard': {
                    'format': '[%(asctime)s]-[%(levelname)s]-[%(filename)s:%(lineno)d]-[%(module)s:%(funcName)s]: %(message)s '
                },
            },
            'filters': {
                'require_debug_false': {
                    '()': 'django.utils.log.RequireDebugFalse'
                }
            },
            'handlers': {
                'default': {
                    'level': 'ERROR',
                    'class': _CLASS,
                    'filename': os.path.join(LOGROOT, 'logs', 'django.log'),
                    'maxBytes': 1024 * 1024 * 5,
                    'backupCount': 5,
                    'formatter': 'standard',
                    'encoding': 'utf-8',
                },
                'watermark': {
                    'level': 'INFO',
                    'class': _CLASS,
                    'filename': os.path.join(LOGROOT, 'logs', 'watermark.log'),
                    'maxBytes': 1024 * 1024 * 5,
                    'backupCount': 5,
                    'formatter': 'standard',
                    'encoding': 'utf-8',
                },
                'ip_geo': {
                    'level': 'INFO',
                    'class': _CLASS,
                    'filename': os.path.join(LOGROOT, 'logs', 'ip_geo.log'),
                    'maxBytes': 1024 * 1024 * 5,
                    'backupCount': 5,
                    'formatter': 'standard',
                    'encoding': 'utf-8',
                },
                'mail_admins': {
                    'level': 'ERROR',
                    'filters': ['require_debug_false'],
                    'class': 'django.utils.log.AdminEmailHandler'
                }, 'accountmanage_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'accountmanage.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }, 'audit_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'audit.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }, 'iniappdb_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'iniappdb.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }, 'isopLog_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'isop.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }, 'asset_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'asset.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }, 'debugtool_handler': {
                    'level': 'DEBUG',
                    'class': _CLASS2,
                    'filename': os.path.join(LOGROOT, 'logs', 'debugtool.log'),
                    'formatter': 'verbose',
                    'when': 'midnight'
                }
            },
            'loggers': {
                'django': {
                    'handlers': ['default'],
                    'level': 'INFO',
                    'propagate': False
                },
                'watermark': {
                    'handlers': ['watermark'],
                    'level': 'INFO',
                    'propagate': False
                },
                'ip_geo': {
                    'handlers': ['ip_geo'],
                    'level': 'INFO',
                    'propagate': False
                },
                'django.request': {
                    'handlers': ['mail_admins'],
                    'level': 'ERROR',
                    'propagate': True,
                }, 'accountmanage_log': {
                    'level': 'DEBUG',
                    'handlers': ['accountmanage_handler'],
                    'propagate': False
                }, 'audit_log': {
                    'level': 'DEBUG',
                    'handlers': ['audit_handler'],
                    'propagate': False
                }, 'iniappdb_log': {
                    'level': 'DEBUG',
                    'handlers': ['iniappdb_handler'],
                    'propagate': False
                }, 'isopLog_log': {
                    'level': 'DEBUG',
                    'handlers': ['isopLog_handler'],
                    'propagate': False
                }, 'asset_log': {
                    'level': 'DEBUG',
                    'handlers': ['asset_handler'],
                    'propagate': False
                }, 'debugtool_log': {
                    'level': 'DEBUG',
                    'handlers': ['debugtool_handler'],
                    'propagate': False
                }
            }
        }
        from appsUtils.redisUtil import RedisUtil

        redisutil = RedisUtil()
        CACHES = {
            "default": {
                "BACKEND": _CLASS4,
                "LOCATION": "redis://127.0.0.1:6379/0",
                "OPTIONS": {
                    "CLIENT_CLASS": _CLASS3,
                    "CONNECTION_POOL_KWARGS": {"max_connections": 10},
                    "PASSWORD": redisutil.getRedisPass(),
                },
                'TIMEOUT': None,
                "KEY_FUNCTION": lambda cache_key, prefix, version: cache_key
            },
            "one": {
                "BACKEND": _CLASS4,
                "LOCATION": "redis://127.0.0.1:6379/1",
                "OPTIONS": {
                    "CLIENT_CLASS": _CLASS3,
                    "CONNECTION_POOL_KWARGS": {"max_connections": 10},
                    "PASSWORD": redisutil.getRedisPass(),
                },
                'TIMEOUT': None,
                "KEY_FUNCTION": lambda cache_key, prefix, version: cache_key
            },
            "session": {
                "BACKEND": _CLASS4,
                "LOCATION": "redis://127.0.0.1:6379/2",
                "OPTIONS": {
                    "CLIENT_CLASS": _CLASS3,
                    "SERIALIZER": "django_redis.serializers.json.JSONSerializer",
                    "CONNECTION_POOL_KWARGS": {"max_connections": 50},
                    "PASSWORD": redisutil.getRedisPass(),
                },
                'TIMEOUT': None,
                "KEY_FUNCTION": lambda cache_key, prefix, version: cache_key
            },
        }
        SESSION_ENGINE = 'custom_session_engine'
        # >> SESSION_CACHE_ALIAS = 'session'
except Exception as e:
    # >> avoid pack error
    print(str(e))
