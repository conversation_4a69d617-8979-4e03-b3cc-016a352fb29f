[{"task_name": "day_model_custom_ports_flow_detect_1005", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_custom_ports_flow_detect.py 1005 1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "指定端口流量检测，每天一次"}, {"task_name": "day_model_custom_ports_flow_detect_1224", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_custom_ports_flow_detect.py 1224 1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 20 1 * * ?", "is_enable": 1, "task_description": "指定端口流量检测，每天一次"}, {"task_name": "day_model_apt_ports_flow_detect", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_apt_ports_flow_detect.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 30 1 * * ?", "is_enable": 1, "task_description": "apt非常用端口天告警，每凌晨1点30执行该任务"}, {"task_name": "day_model_tcp_custom_ports_flow_detect", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_tcp_custom_ports_flow_detect.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 30 1 * * ?", "is_enable": 1, "task_description": "tcp指定非常用端口天告警，每凌晨1点30执行该任务,与day_model_tcp_ports_flow_detect重复，当day_model_tcp_ports_flow_detect优化修复后停用"}, {"task_name": "day_model_real_oa_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_oa.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 2 * * ?", "is_enable": 1, "task_description": "真实OA天告警，每天2点执行该任务"}, {"task_name": "day_model_real_vpn_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_vpn.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 30 2 * * ?", "is_enable": 1, "task_description": "真实VPN天告警，每天2点30执行该任务"}, {"task_name": "day_model_real_iot_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_iot.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "真实物联网天告警，每天1点执行该任务"}, {"task_name": "day_model_real_firewall", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_firewall.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 50 2 * * ?", "is_enable": 1, "task_description": "防火墙天告警，每天2点50执行该任务"}, {"task_name": "day_model_cloud_storage", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_cloud_storage.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 10 3 * * ?", "is_enable": 1, "task_description": "云盘云笔记天告警，每天3点10分执行该任务"}, {"task_name": "day_model_real_video", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_video.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 30 3 * * ?", "is_enable": 1, "task_description": "真实物联网视频天告警，每天3点30执行该任务"}, {"task_name": "day_model_real_web_email_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_web_email.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 55 3 * * ?", "is_enable": 1, "task_description": "真实WEB邮件天告警，每天4点执行该任务"}, {"task_name": "day_model_real_ftp_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_ftp.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 10 4 * * ?", "is_enable": 1, "task_description": "真实FTP天告警，每天4点30执行该任务"}, {"task_name": "day_model_real_remote_desktop_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_remote_desktop.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 20 4 * * ?", "is_enable": 1, "task_description": "真实远程登录天告警，每天5点执行该任务"}, {"task_name": "day_model_real_database_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_database.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 30 4 * * ?", "is_enable": 1, "task_description": "真实数据库天告警，每天5点30执行该任务"}, {"task_name": "day_model_key_unit", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_key_unit.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 45 4 * * ?", "is_enable": 1, "task_description": "重点单位天告警，每天5点45执行该任务"}, {"task_name": "day_model_black_ip", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_black_ip.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 5 * * ?", "is_enable": 1, "task_description": "黑IP天告警，每天6点执行该任务"}, {"task_name": "day_model_real_router", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_router.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 10 5 * * ?", "is_enable": 1, "task_description": "真实路由器天告警，每天5点10执行该任务"}, {"task_name": "day_model_real_cmp", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_cmp.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 25 5 * * ?", "is_enable": 1, "task_description": "真实云管理平台天告警，每天5点25执行该任务"}, {"task_name": "day_model_real_nas", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_real_nas.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 40 5 * * ?", "is_enable": 1, "task_description": "真实网络附属存储天告警，每天5点40执行该任务"}, {"task_name": "day_model_multiple_alert", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_multiple_alert.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 1 0 * * ?", "is_enable": 1, "task_description": "IP倍数告警, 每天的0点1分执行一次该任务"}, {"task_name": "day_model_ip_pair_multiple_script", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_ip_pair_multiple_script.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 1 0 * * ?", "is_enable": 1, "task_description": "IP对倍数告警, 每天的0点1分执行一次该任务"}, {"task_name": "day_model_unit_threshold_alarm_script", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_unit_threshold_alarm_script.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/30 * * * ?", "is_enable": 1, "task_description": "单位阈值告警, 每隔30分钟执行一次该任务"}, {"task_name": "csemp_script", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/csemp_script.py >/dev/null 2>&1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/2 * * * ?", "is_enable": 1, "task_description": "监控报文检测/留存检索任务状态, 每隔2分钟执行一次该任务"}, {"task_name": "agg_netflow_worker", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/agg_netflow_worker.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "Netflow大流量事件，每天凌晨1点执行该任务"}, {"task_name": "agg_tclog_worker", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/agg_tclog_worker.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "通联日志大流量事件，每天凌晨1点执行该任务"}, {"task_name": "key_unit_flow", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/key_unit_detail/key_unit_flow.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "单位流量缓存, 每隔5分钟执行一次该任务"}, {"task_name": "tllog_cont_event_update", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/tllog_cont_event_update.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "通联持续性事件归并, 每隔5分钟执行一次该任务"}, {"task_name": "netflow_cont_event_update", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/netflow_cont_event_update.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "netflow持续性事件归并, 每隔5分钟执行一次该任务"}, {"task_name": "event_asset_info", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_asset_info.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "通联资产增强, 每隔5分钟执行一次该任务"}, {"task_name": "netflow_event_asset_info", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_asset_info.py --report_type 2", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "Netflow资产增强, 每隔5分钟执行一次该任务"}, {"task_name": "crontab_generate_overview_script", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/crontab_generate_overview_script.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/10 * * * ?", "is_enable": 1, "task_description": "概览缓存任务"}, {"task_name": "asset_knowledge", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/asset_knowledge.py --action 3", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 6 ? * 1", "is_enable": 1, "task_description": "资产知识库拉取与推送，每周日早上6点执行1次该任务"}, {"task_name": "clean_alarm_and_event", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/clean_alarm_and_event.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "持续性事件、告警数据自动清理"}, {"task_name": "clean_agg_data", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/clean_agg_data.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "清理流量排行数据"}, {"task_name": "big_screen_run_5min", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_5min.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "大屏数据推送，每五分钟执行一次"}, {"task_name": "big_screen_run_hour", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_hour.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 * * * ?", "is_enable": 1, "task_description": "大屏数据推送，每小时执行一次"}, {"task_name": "big_screen_run_day", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_day.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 * * ?", "is_enable": 1, "task_description": "大屏数据推送，每天执行一次"}, {"task_name": "big_screen_run_week", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_week.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 * * 1", "is_enable": 1, "task_description": "大屏数据推送，每周执行一次"}, {"task_name": "big_screen_run_month", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_month.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 1 * ?", "is_enable": 1, "task_description": "大屏数据推送，每月执行一次"}, {"task_name": "big_screen_run_year", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/big_screen/run_task/run_year.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 1 1 ?", "is_enable": 1, "task_description": "大屏数据推送，每年执行一次"}, {"task_name": "database_backup_script", "task_type": 1, "exec_cmd": "sh /home/<USER>/ISOP/apps/cncert_kj/script/database_backup_script.sh", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 * * ?", "is_enable": 1, "task_description": "数据库备份任务"}, {"task_name": "do_excel_group_by_province", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/do_excel_group_by_province.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "按每个省份分类，导出excel文件"}, {"task_name": "clean_pg_data", "task_type": 1, "exec_cmd": "sh /home/<USER>/ISOP/apps/cncert_kj/script/clean_pg_data.sh", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * 1", "is_enable": 1, "task_description": "清理PG数据"}, {"task_name": "update_cont_events_report_type", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/update_cont_events_report_type.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0,12 * * ?", "is_enable": 1, "task_description": "更新持续性事件来源"}, {"task_name": "automatic_issue_pcap", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/automatic_issue_pcap.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "自动下发pcap，每5分钟执行该任务"}, {"task_name": "cont_event_judge", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/cont_event_judge.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "持续性事件自动研判"}, {"task_name": "remote_login_judge", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/remote_login_judge.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "远程登录研判，每5分钟执行该任务"}, {"task_name": "ftp_judge", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/ftp_judge.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "ftp类型事件研判，每5分钟执行该任务"}, {"task_name": "cont_event_judge_ago", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/cont_event_judge_ago.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 * * ?", "is_enable": 1, "task_description": "数据库事件研判，每天凌晨零点运行一次"}, {"task_name": "ip_count_script", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/ip_count_script.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0/1 * * ?", "is_enable": 1, "task_description": "ip一对多统计脚本,每小时运行一次,每次都统计一个月的事件"}, {"task_name": "day_event_enhance", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_event_enhance.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0/2 * * ?", "is_enable": 1, "task_description": "天模型事件信息增强-资产，情报，恶意情报，应用类型等"}, {"task_name": "tclog_auto_judge_iot_ddos", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/auto_judge_iot_ddos.py 1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "通联物联网事件研判，每5分钟执行该任务"}, {"task_name": "netflow_auto_judge_iot_ddos", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/auto_judge_iot_ddos.py 2", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "NetFlow物联网事件研判，每5分钟执行该任务"}, {"task_name": "event_alert_daily_stats", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_alert_daily_stats.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 0 * * ?", "is_enable": 1, "task_description": "事件，告警数量统计脚本，每天的0点0分0秒执行该任务(5min)"}, {"task_name": "event_alert_daily_stats_day", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_alert_daily_stats.py   --model_type 1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 23 * * ?", "is_enable": 1, "task_description": "事件，告警数量统计脚本，每天的23点0分0秒执行该任务(天模型)"}, {"task_name": "kj_event_recommend", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_recommend/kj_event_recommend.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 5 * * ?", "is_enable": 1, "task_description": "事件推荐脚本，每天的5点执行该任务"}, {"task_name": "flow_surge", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/flow_surge.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "流量突增任务, 每五分钟运行一次"}, {"task_name": "abnormal_tasks", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/exception_event/abnormal_tasks.py", "task_owner": "cncert_kj", "run_mode": 0, "is_enable": 1, "task_description": "异常场景常驻任务"}, {"task_name": "event_enrichment", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/event_enrichment.py", "task_owner": "cncert_kj", "run_mode": 0, "is_enable": 1, "task_description": "事件增强常驻任务"}, {"task_name": "log_monitor", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/log_monitor.py", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0/5 * * * ?", "is_enable": 1, "task_description": "日志监测脚本，每5分钟执行一次"}, {"task_name": "tllog_alert_data_transfer", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/data_transfer_script/alert_data_transfer.py --report_type 1", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "通联告警外发，每天一次"}, {"task_name": "netflow_alert_data_transfer", "task_type": 1, "exec_cmd": "python /home/<USER>/ISOP/apps/cncert_kj/script/data_transfer_script/alert_data_transfer.py --report_type 2", "task_owner": "cncert_kj", "run_mode": 1, "duration_args": "0 0 1 * * ?", "is_enable": 1, "task_description": "netflow告警外发，每天一次"}]