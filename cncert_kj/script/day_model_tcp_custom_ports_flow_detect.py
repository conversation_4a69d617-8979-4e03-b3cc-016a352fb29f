#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@ Author    : <PERSON><PERSON><PERSON><PERSON><PERSON>
@ File      : day_model_tcp_custom_ports_flow_detect.py
@ Time      : 2024/12/6 16:24
@ Desc      : 非常用端口按天的是指监测境外的指定端口，境内端口不需要命中
"""
import argparse
import copy
import datetime
import time
import traceback
import uuid

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.static_baseline_model import get_dynamic_baseline_threshold, get_dynamic_baseline_status
from cncert_kj.script.alert_day_model.day_filter_rule import get_filter_rule
from cncert_kj.script.alert_day_model.day_model_utils import get_port_distribution2
from cncert_kj.script.event_asset_info import GetAssetInfo
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.script.uncommon_port_judge import UncommonPortJudge
from cncert_kj.script.update_cont_event import update_tllog_sql, update_netflow_sql
from cncert_kj.utils import logger
from cncert_kj.utils import net
from cncert_kj.utils.conf_util import TLLOG_IP_PAIR_5MIN, NETFLOW_IP_PAIR_5MIN
from cncert_kj.utils.es_utils import ESUtil
from cncert_kj.utils.flow_log_util import format_flow_logs, generate_flow_key
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_filing_sys import RequestFilingSys

mlog = logger.init_logger('day_model_tcp_custom_ports_flow_detect')


class TCPPortFlowDetect(object):
    def __init__(self, start_time, end_time, ports, alert_name_type, flow_type, protocol=None):
        """
        @param start_time: 查询开始时间
        @param end_time: 查询结束时间
        @param flow_type: 流量类型 1:通联 2:netflow
        """
        self.threshold_data = {}
        self.start_time = start_time
        self.end_time = end_time
        self.flow_type = flow_type
        self.ports = ports
        self.protocol = protocol
        self.es_util = ESUtil()
        self.es = ESUtil().es
        self.mlog = mlog
        if flow_type not in (1, 2):
            raise ValueError("参数错误")
        self.alert_name_type = copy.deepcopy(alert_name_type)

        # 后期阈值从数据库中获取
        self.threshold = 1024 * 1024 * 100
        self.analysis_tech = 2  # 与静态基线数据归并到一起
        self.tclog_type = 1
        self.netflow_type = 2

        if self.flow_type == self.tclog_type:
            self.es_index = TLLOG_IP_PAIR_5MIN
            self.msg = "通联"
            self.table_name = "internal_app_bsa_gjk.traffic_alert"
            self.fields = ','.join((
                "uuid",
                "name",
                "type",
                "start_time",
                "end_time",
                "sip",
                "src_region",
                "src_operator",
                "src_iot",
                "src_service",
                "src_unit",
                "src_com",
                "sport",
                "dip",
                "dst_region",
                "dst_operator",
                "dst_iot",
                "dst_service",
                "dst_unit",
                "dst_com",
                "dport",
                "up_bytes_all",
                "down_bytes_all",
                "up_packets_all",
                "down_packets_all",
                "flow_list",
                "analysis_tech",
                "ipv6",
                "port_distribution",
                "asset_tag",
                "flow_logs",
                "protocol",
            ))
        else:
            self.msg = "NetFlow"
            self.es_index = NETFLOW_IP_PAIR_5MIN
            self.table_name = "internal_app_bsa_gjk.netflow_alert_5min"
            self.fields = ','.join((
                "uuid",
                "name",
                "type",
                "start_time",
                "end_time",
                "sip",
                "src_region",
                "src_operator",
                "src_iot",
                "src_service",
                "src_unit",
                "src_com",
                "sport",
                "dip",
                "dst_region",
                "dst_operator",
                "dst_iot",
                "dst_service",
                "dst_unit",
                "dst_com",
                "dport",
                "bytes_all",
                "bytes_all_down",
                "packets_all",
                "packets_all_down",
                "analysis_tech",
                "ipv6",
                "port_distribution",
                "asset_tag",
                "flow_logs",
                "protocol",
            ))
        self.insert_sql_template = """
        INSERT INTO {table_name} ({fields}) VALUES {params}
        """

    def es_keep_alive(self):
        if not self.es.ping():
            self.es = ESUtil().es
        return self.es

    def add_protocol(self, dsl):
        if self.protocol:
            dsl["query"]["bool"]["filter"].append({
                "term": {
                    "protocol": self.protocol.lower()
                }
            })
        return dsl

    def scroll(self, index):
        """
        滚动查全量
        """
        count_dsl = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "end_time": {
                                    "gte": self.start_time,
                                    "lt": self.end_time
                                }
                            }
                        },
                        {
                            "terms": {
                                "dport": self.ports
                            }
                        }
                    ]
                }
            }
        }
        count_dsl = self.add_protocol(count_dsl)
        count = self.es_util.search_count(index, count_dsl)
        dsl = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "end_time": {
                                    "gte": self.start_time,
                                    "lt": self.end_time
                                }
                            }
                        },
                        {
                            "terms": {
                                "dport": self.ports
                            }
                        }
                    ]
                }
            },
            "_source": [
                "asset_ip", "peer_ip", "service_tag", "asset_boundary",
                "start_time", "end_time", "asset_name", "out_packets", "in_packets",
                "out_bytes", "in_bytes", "dport", "asset_country", "asset_province", "peer_country",
                "peer_province", "protocol", "sport"
            ]
        }
        dsl = self.add_protocol(dsl)
        scroll_id, result = None, {}
        n = 0
        retry_limit = 10
        self.es.transport.send_get_body_as = "POST"
        while True:
            try:
                n += 1
                self.es = self.es_keep_alive()
                self.mlog.info("{} 每次查询10000条，正在查询第{}次，共需查询数据量：{}".format(self.msg, n, count))
                # 每次循环处理一万条数据
                if scroll_id is None:
                    # 第一次滚动请求，初始化滚动ID
                    response = self.es.search(index=index, scroll='5m', size=10000, body=dsl, request_timeout=1800)
                    scroll_id = response['_scroll_id']
                    hits = response['hits']['hits']
                else:
                    # 后续滚动请求，继续读取文档
                    response = self.es.scroll(body={"scroll_id": scroll_id, "scroll": '5m'}, request_timeout=1800)
                    hits = response['hits']['hits']

                if not hits:
                    # 已读取完所有文档，退出循环
                    self.mlog.info("已读取完所有文档，退出循环")
                    break
                # 处理数据-归并
                for hit in hits:
                    asset_ip = hit['_source']['asset_ip']
                    peer_ip = hit['_source']['peer_ip']

                    service_tag = hit['_source']['service_tag']
                    asset_boundary = hit['_source']['asset_boundary']
                    dport = hit['_source']['dport']
                    if not all([asset_boundary, service_tag]):
                        continue
                    key = (asset_ip, peer_ip, asset_boundary, service_tag, dport)
                    data = result.get(key, {})
                    if not isinstance(data, dict):
                        continue
                    data = self.handle_scroll_data(hit, data, asset_ip, peer_ip, asset_boundary, service_tag, dport)
                    result[key] = data
            except Exception as e:
                self.mlog.error("{} 第{}次获取数据异常，异常信息：{}".format(self.msg, n, e))
                self.mlog.error(traceback.format_exc())
                retry_limit -= 1
                if retry_limit <= 0:
                    self.mlog.error("多次获取数据失败，初始化数据重新开始")
                    self.es = ESUtil().es
                    n = 0
                    retry_limit = 10
                    scroll_id, result = None, {}
        return result

    def handle_scroll_data(self, hit, data, asset_ip, peer_ip, asset_boundary, service_tag, dport):
        start_time = hit['_source']['start_time']
        end_time = hit['_source']['end_time']
        asset_name = hit['_source']['asset_name']
        out_packets = hit['_source']['out_packets']
        in_packets = hit['_source']['in_packets']
        out_bytes = hit['_source']['out_bytes']
        in_bytes = hit['_source']['in_bytes']
        asset_country = hit['_source']['asset_country']
        asset_province = hit['_source']['asset_province']
        peer_country = hit['_source']['peer_country']
        peer_province = hit['_source']['peer_province']
        protocol = hit['_source'].get('protocol')
        sport = hit['_source'].get('sport', None)

        if not data:
            protocol_dict = {
                protocol: {"out_bytes": out_bytes, "in_bytes": in_bytes}
            }
            src_port_dict = {
                sport: {"out_bytes": out_bytes, "in_bytes": in_bytes}
            }

            data = {
                "asset_ip": asset_ip,
                "peer_ip": peer_ip,
                "service_tag": service_tag,
                "asset_boundary": asset_boundary,
                "dport": dport,
                "start_time": start_time,
                "end_time": end_time,
                "asset_name": asset_name,
                "out_packets": out_packets,
                "in_packets": in_packets,
                "out_bytes": out_bytes,
                "in_bytes": in_bytes,
                "asset_country": asset_country,
                "asset_province": asset_province,
                "peer_country": peer_country,
                "peer_province": peer_province,
                "protocol_dict": protocol_dict,
                "src_port_dict": src_port_dict,
            }
        else:
            _start_time = data.get("start_time", 0)
            _end_time = data.get("end_time", 0)
            data['out_packets'] += out_packets
            data['in_packets'] += in_packets
            data['out_bytes'] += out_bytes
            data['in_bytes'] += in_bytes

            # 增加地理位置判断
            if asset_country:
                data["asset_country"] = asset_country
            if asset_province:
                data["asset_province"] = asset_province
            if peer_country:
                data["peer_country"] = peer_country
            if peer_province:
                data["peer_province"] = peer_province
            if _start_time > start_time:
                data['start_time'] = start_time
            if _end_time < end_time:
                data['end_time'] = end_time

            protocol_dict = data.get("protocol_dict")  # 这里肯定是有数据的
            if not protocol_dict.get(protocol, {}):  # 这个协议可能没有数据，需要新增
                protocol_dict[protocol] = {
                    "out_bytes": out_bytes,
                    "in_bytes": in_bytes,
                }
            else:
                # 端口有数据就累加
                protocol_dict[protocol]['out_bytes'] += out_bytes
                protocol_dict[protocol]['in_bytes'] += in_bytes
            data["protocol_dict"] = protocol_dict

            # 记录sport流量
            src_port_dict = data.get("src_port_dict")
            if not src_port_dict.get(sport, {}):
                src_port_dict[sport] = {
                    "out_bytes": out_bytes,
                    "in_bytes": in_bytes,
                }
            else:
                # 端口有数据就累加
                src_port_dict[sport]['out_bytes'] += out_bytes
                src_port_dict[sport]['in_bytes'] += in_bytes
            data["src_port_dict"] = src_port_dict
        return data

    def handle_data(self, result):
        data = {}
        for key, value in result.items():
            if self.flow_type == self.tclog_type:
                res = self.handle_data_tclog(value, key)

            elif self.flow_type == self.netflow_type:
                res = self.handle_data_netflow(value, key)

            else:
                continue
            if not res:
                continue
            data[key] = res
        return data

    def handle_data_tclog(self, value, key):
        data = {}

        asset_ip = key[0]
        peer_ip = key[1]
        asset_boundary = key[2]
        service_tag = key[3]
        out_bytes = value.get("out_bytes")
        in_bytes = value.get("in_bytes")
        start_time = value.get("start_time")
        end_time = value.get("end_time")
        out_packets = value.get("out_packets")
        in_packets = value.get("in_packets")
        asset_name = value.get("asset_name")
        asset_country = value.get("asset_country")
        asset_province = value.get("asset_province")
        peer_country = value.get("peer_country")
        peer_province = value.get("peer_province")
        src_unit = dst_unit = ""
        src_port_dict = value.get("src_port_dict", {})
        dport = value.get("dport")
        protocol_dict = value.get("protocol_dict", {})
        out_bytes_protocol = max(protocol_dict.items(), key=lambda x: x[1]['out_bytes'])[0]
        in_bytes_protocol = max(protocol_dict.items(), key=lambda x: x[1]['in_bytes'])[0]

        out_bytes_sport = max(src_port_dict.items(), key=lambda x: x[1]['out_bytes'])[0]
        in_bytes_sport = max(src_port_dict.items(), key=lambda x: x[1]['in_bytes'])[0]
        alert_key = "{}_{}".format(asset_boundary, service_tag)
        alert_type, alert_name = self.alert_name_type.get(alert_key, [0, "未知"])
        if alert_type == 0:
            return data
        # 从配置表中获取阈值
        belong = 2
        self.get_threshold(belong, alert_type)

        # 通联
        # 端口是服务端端口，  asset_product是服务端信息，  通联服务端是目的
        if asset_boundary == 2 and service_tag == 2 and out_bytes > self.threshold:
            # asset_ip是境内IP， peer_ip是境外IP， 境外IP是服务端  主动类型，境内向境外资产上传数据
            port_distribution = get_port_distribution2(src_port_dict)
            src_unit = asset_name
            protocol = out_bytes_protocol
            sport = out_bytes_sport

        elif asset_boundary == 2 and service_tag == 1 and out_bytes > self.threshold:
            # asset_ip是境内IP， peer_ip是境外IP， 境内IP是服务端  被动类型，境外从境内资产下载数据
            # IP  流量  互换位置
            port_distribution = get_port_distribution2(src_port_dict)
            dst_unit = asset_name
            asset_ip, peer_ip = peer_ip, asset_ip
            in_bytes, out_bytes = out_bytes, in_bytes
            asset_country, peer_country = peer_country, asset_country
            asset_province, peer_province = peer_province, asset_province
            protocol = out_bytes_protocol
            sport = out_bytes_sport

        elif asset_boundary == 1 and service_tag == 2 and in_bytes > self.threshold:
            # 判断 in_bytes
            # asset_ip是境外IP， peer_ip是境内IP， 境内IP是服务端  被动类型，境外从境内资产下载数据
            port_distribution = get_port_distribution2(src_port_dict)
            src_unit = asset_name
            protocol = in_bytes_protocol
            sport = in_bytes_sport

        elif asset_boundary == 1 and service_tag == 1 and in_bytes > self.threshold:
            # IP 、 流量 互换   判断 in_bytes
            # asset_ip是境外IP， peer_ip是境内IP， 境外IP是服务端  主动类型，境内向境外资产上传数据
            port_distribution = get_port_distribution2(src_port_dict)
            dst_unit = asset_name
            asset_ip, peer_ip = peer_ip, asset_ip
            in_bytes, out_bytes = out_bytes, in_bytes
            asset_country, peer_country = peer_country, asset_country
            asset_province, peer_province = peer_province, asset_province
            protocol = in_bytes_protocol
            sport = in_bytes_sport

        else:
            return data
        if in_bytes > out_bytes:
            # 非常用端口天模型 流入大于流出的要过滤掉
            return data
        data = {
            "asset_ip": net.int32_to_ip(asset_ip),
            "peer_ip": net.int32_to_ip(peer_ip),
            "alert_name": alert_name,
            "alert_type": alert_type,
            "start_time": start_time,
            "end_time": end_time,
            "out_packets": out_packets,
            "in_packets": in_packets,
            "out_bytes": out_bytes,
            "in_bytes": in_bytes,
            "sport": sport,
            "dport": dport,
            "port_distribution": port_distribution,
            "src_unit": src_unit,
            "dst_unit": dst_unit,
            "asset_country": asset_country,
            "asset_province": asset_province,
            "peer_country": peer_country,
            "peer_province": peer_province,
            "protocol": protocol
        }

        return data

    def handle_data_netflow(self, value, key):
        data = {}
        asset_ip = key[0]
        peer_ip = key[1]
        asset_boundary = key[2]
        service_tag = key[3]
        out_bytes = value.get("out_bytes")
        in_bytes = value.get("in_bytes")
        start_time = value.get("start_time")
        end_time = value.get("end_time")
        out_packets = value.get("out_packets")
        in_packets = value.get("in_packets")
        asset_name = value.get("asset_name")
        asset_country = value.get("asset_country")
        asset_province = value.get("asset_province")
        peer_country = value.get("peer_country")
        peer_province = value.get("peer_province")
        src_unit = dst_unit = ""
        src_port_dict = value.get("src_port_dict", {})
        dport = value.get("dport")
        protocol_dict = value.get("protocol_dict", {})
        out_bytes_protocol = max(protocol_dict.items(), key=lambda x: x[1]['out_bytes'])[0]
        in_bytes_protocol = max(protocol_dict.items(), key=lambda x: x[1]['in_bytes'])[0]

        out_bytes_sport = max(src_port_dict.items(), key=lambda x: x[1]['out_bytes'])[0]
        in_bytes_sport = max(src_port_dict.items(), key=lambda x: x[1]['in_bytes'])[0]

        alert_key = "{}_{}".format(asset_boundary, service_tag)
        alert_type, alert_name = self.alert_name_type.get(alert_key, [0, "未知"])
        if alert_type == 0:
            return data
        # 从配置表中获取阈值
        belong = 1
        self.get_threshold(belong, alert_type)

        # NetFlow
        if asset_boundary == 2 and service_tag == 2 and out_bytes > self.threshold:
            # asset_ip是境内IP， peer_ip是境外IP， 境外IP是服务端  主动类型，境内向境外资产上传数据
            port_distribution = get_port_distribution2(src_port_dict)
            src_unit = asset_name
            protocol = out_bytes_protocol
            sport = out_bytes_sport

        elif asset_boundary == 2 and service_tag == 1 and out_bytes > self.threshold:
            # asset_ip是境内IP， peer_ip是境外IP， 境内IP是服务端  被动类型，境外从境内资产下载数据
            port_distribution = 1
            sport = out_bytes_sport
            sport, dport = dport, sport
            src_unit = asset_name
            protocol = out_bytes_protocol

        elif asset_boundary == 1 and service_tag == 2 and in_bytes > self.threshold:
            # 判断 in_bytes    IP、流量 互换
            # asset_ip是境外IP， peer_ip是境内IP， 境内IP是服务端  被动类型，境外从境内资产下载数据
            port_distribution = 1
            sport = in_bytes_sport
            sport, dport = dport, sport
            dst_unit = asset_name
            asset_ip, peer_ip = peer_ip, asset_ip
            in_bytes, out_bytes = out_bytes, in_bytes
            asset_country, peer_country = peer_country, asset_country
            asset_province, peer_province = peer_province, asset_province
            protocol = in_bytes_protocol

        elif asset_boundary == 1 and service_tag == 1 and in_bytes > self.threshold:
            # 判断 in_bytes    IP、流量 互换
            # asset_ip是境外IP， peer_ip是境内IP， 境外IP是服务端  主动类型，境内向境外资产上传数据
            port_distribution = get_port_distribution2(src_port_dict)
            dst_unit = asset_name
            asset_ip, peer_ip = peer_ip, asset_ip
            in_bytes, out_bytes = out_bytes, in_bytes
            asset_country, peer_country = peer_country, asset_country
            asset_province, peer_province = peer_province, asset_province
            protocol = in_bytes_protocol
            sport = in_bytes_sport
        else:
            return data
        if in_bytes > out_bytes:
            # 非常用端口天模型 流入大于流出的要过滤掉
            return data
        data = {
            "asset_ip": net.int32_to_ip(asset_ip),
            "peer_ip": net.int32_to_ip(peer_ip),
            "alert_name": alert_name,
            "alert_type": alert_type,
            "start_time": start_time,
            "end_time": end_time,
            "out_packets": out_packets,
            "in_packets": in_packets,
            "out_bytes": out_bytes,
            "in_bytes": in_bytes,
            "sport": sport,
            "dport": dport,
            "port_distribution": port_distribution,
            "src_unit": src_unit,
            "dst_unit": dst_unit,
            "asset_country": asset_country,
            "asset_province": asset_province,
            "peer_country": peer_country,
            "peer_province": peer_province,
            "protocol": protocol
        }
        return data

    def get_threshold(self, belong, alert_type):
        threshold = self.threshold_data.get("{}_{}".format(alert_type, belong))
        if not threshold:
            self.mlog.info("开始查询该类型阈值：{}，{}".format(alert_type, belong))
            threshold = get_dynamic_baseline_threshold(alert_type, belong=belong)
            self.threshold_data["{}_{}".format(alert_type, belong)] = threshold
            self.mlog.info("类型：{}； 阈值：{}， 流量类型：{}".format(alert_type, threshold, self.flow_type))
        self.threshold = threshold if threshold else self.threshold

    def get_last_day_flow_logs(self, ip_pair):
        """
        获取flow_logs
        """
        try:
            res_data = {}
            n = 0
            total = len(ip_pair)
            for item in ip_pair:
                n += 1
                asset_ip = item[0]
                peer_ip = item[1]
                asset_boundary = item[2]
                service_tag = item[3]
                dport = item[4]
                alert_type = item[5]

                query_dsl = {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "term": {
                                        "service_tag": service_tag
                                    }
                                },
                                {
                                    "term": {
                                        "dport": dport
                                    }
                                },
                                {
                                    "term": {
                                        "asset_ip": asset_ip
                                    }
                                },
                                {
                                    "term": {
                                        "peer_ip": peer_ip
                                    }
                                },
                                {
                                    "range": {
                                        "end_time": {
                                            "gte": self.start_time,
                                            "lte": self.end_time
                                        }
                                    }
                                },
                                {
                                    "term": {
                                        "asset_boundary": asset_boundary
                                    }
                                }
                            ]
                        }
                    },
                    "size": 10
                }
                if self.protocol:
                    query_dsl["query"]["bool"]["filter"].append({
                        "term": {
                            "protocol": self.protocol.lower()
                        }
                    })
                try:
                    result = self.es_util.search_es(self.es_index, query_dsl, timeout=1800)

                    if not result and result.get("hits", {}).get("hits", []):
                        continue
                except Exception as er:
                    self.mlog.error("查询原始日志错误：{}， query_dsl:{}".format(er, query_dsl))
                    continue
                hits = result.get("hits", {}).get("hits", [])
                res_data[item] = format_flow_logs(hits, self.flow_type, alert_type)
                if n % 10 == 0:
                    self.mlog.info("查询原始日志进度：{}/{}".format(n, total))
            return res_data
        except Exception as e:
            self.mlog.error("flow_logs查询失败：{}".format(e))
            self.mlog.error(traceback.format_exc())
            return {}

    def generate_alert(self, es_data):
        """
        生成告警sql参数
        :return:
        """
        params_list = []
        new_params_list = []
        ip_pair = []
        ip_list = []
        for key, value in es_data.items():
            # 如果有数据
            if value:
                asset_country = value.get("asset_country")
                asset_province = value.get("asset_province")
                peer_country = value.get("peer_country")
                peer_province = value.get("peer_province")
                # 用来查flow_logs
                alert_type = value.get("alert_type")
                key = tuple(list(key) + [alert_type, self.protocol])
                ip_pair.append(key)

                params_list.append({
                    "key": key,
                    "asset_ip": value.get("asset_ip"),
                    "peer_ip": value.get("peer_ip"),
                    "name": value.get("alert_name"),
                    "type": alert_type,
                    "uuid": str(uuid.uuid4()),
                    "start_time": value.get("start_time"),
                    "end_time": value.get("end_time"),
                    "out_bytes": value.get("out_bytes"),
                    "in_bytes": value.get("in_bytes"),
                    "out_packets": value.get("out_packets"),
                    "in_packets": value.get("in_packets"),
                    "dst_port": value.get("dport"),
                    "src_port": value.get("sport"),
                    "src_unit": value.get("src_unit"),
                    "dst_unit": value.get("dst_unit"),
                    "port_distribution": value.get("port_distribution"),
                    "src_region": " ".join([asset_country, asset_province]),
                    "dst_region": " ".join([peer_country, peer_province]),
                    "protocol": value.get("protocol"),
                })
                ip_list.extend(key[:2])

        if not params_list:
            return new_params_list
        # 查询备案系统
        self.mlog.info("开始查询备案系统，增强备案信息")
        with RequestFilingSys() as request_filing_obj:
            ip_details = request_filing_obj.query_icp_api(ip_list)

        # 测试 ip_details = {}
        # 查flow_logs
        # self.mlog.info("开始查询ip对索引，构建原始日志数据")
        # flow_logs = self.get_last_day_flow_logs(ip_pair)
        # flow_logs : {}
        new_params_list = self.handle_params(params_list, ip_details, new_params_list)
        return new_params_list

    def handle_params(self, params_list, ip_details, new_params_list):
        rules = get_filter_rule()
        if not rules:
            rules = {}
        src_com_filter = set(rules.get("2", []))
        ip_filter = set(rules.get("1", []))
        ip_port_filter = set(rules.get("3", []))
        self.mlog.info("开始构建告警参数")
        for params in params_list:
            key = params.get("key")
            asset_ip = params.get("asset_ip")
            peer_ip = params.get("peer_ip")
            dst_port = params.get("dst_port")
            src_port = params.get("src_port")
            asset_ip_detail = ip_details.get(asset_ip, {})
            peer_ip_detail = ip_details.get(peer_ip, {})
            params["src_operator"] = asset_ip_detail.get("operator", "")
            params["src_com"] = asset_ip_detail.get("user", "")
            params["dst_operator"] = peer_ip_detail.get("operator", "")
            params["dst_com"] = peer_ip_detail.get("user", "")
            params["flow_log"] = generate_flow_key(key)
            if params['src_com'] in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(params['src_com']))
                continue
            if params['dst_com'] in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(params['dst_com']))
                continue
            if asset_ip + '-' + peer_ip in ip_filter:
                mlog.info("境内外IP：{}-{}， 已下发为白名单事件过滤规则".format(asset_ip, peer_ip))
                continue
            if peer_ip + '-' + asset_ip in ip_filter:
                mlog.info("境内外IP：{}-{}， 已下发为白名单事件过滤规则".format(peer_ip, asset_ip))
                continue
            if asset_ip + ':' + str(src_port) in ip_port_filter:
                mlog.info("境内外IP：{}:{}， 已下发为白名单事件过滤规则".format(asset_ip, src_port))
                continue
            if peer_ip + ':' + str(dst_port) in ip_port_filter:
                mlog.info("境内外IP：{}:{}， 已下发为白名单事件过滤规则".format(peer_ip, dst_port))
                continue

            if is_ipv4_correct(asset_ip):
                ipv6 = False
            else:
                ipv6 = True
            if self.flow_type == self.tclog_type:
                new_params_list.append([
                    params.get("uuid"),
                    params.get("name"),
                    params.get("type"),
                    params.get("start_time"),
                    params.get("end_time"),
                    params.get("asset_ip"),
                    params.get("src_region"),
                    params.get("src_operator"),
                    "",  # src_iot
                    params.get("src_service", ""),  # src_service
                    params.get("src_unit", ""),
                    params.get("src_com"),
                    params.get("src_port", -1),  # sport
                    params.get("peer_ip"),
                    params.get("dst_region"),
                    params.get("dst_operator"),
                    "",  # dst_iot
                    params.get("dst_service", ""),  # dst_service
                    params.get("dst_unit", ""),  # dst_unit
                    params.get("dst_com"),
                    params.get("dst_port", -1),
                    params.get("out_bytes"),
                    params.get("in_bytes", 0),
                    params.get("out_packets", 0),  # up_packets_all
                    params.get("in_packets", 0),  # down_packets_all
                    "",  # flow_list
                    self.analysis_tech,
                    ipv6,
                    params.get("port_distribution"),
                    None,
                    params.get("flow_log"),
                    params.get("protocol")
                ])
            else:
                new_params_list.append([
                    params.get("uuid"),
                    params.get("name"),
                    params.get("type"),
                    params.get("start_time"),
                    params.get("end_time"),
                    params.get("asset_ip"),
                    params.get("src_region"),
                    params.get("src_operator"),
                    "",  # src_iot
                    params.get("src_service", ""),  # src_service
                    params.get("src_unit", ""),
                    params.get("src_com"),
                    params.get("src_port", -1),  # sport
                    params.get("peer_ip"),
                    params.get("dst_region"),
                    params.get("dst_operator"),
                    "",  # dst_iot
                    params.get("dst_service", ""),  # dst_service
                    params.get("dst_unit", ""),  # dst_unit
                    params.get("dst_com"),
                    params.get("dst_port", -1),
                    params.get("out_bytes"),
                    params.get("in_bytes", 0),
                    params.get("out_packets", 0),  # packets_all
                    params.get("in_packets", 0),  # packets_all_down
                    self.analysis_tech,
                    ipv6,
                    params.get("port_distribution"),
                    None,
                    params.get("flow_log"),
                    params.get("protocol")
                ])
        return new_params_list

    def insert_alert(self, params_list):
        """
        插入数据库
        :param params_list:
        :return:
        """
        uuid_list = []
        step = 100
        try:
            for i in range(0, len(params_list), step):
                params = params_list[i:i + step]
                uuid_list.extend([j[0] for j in params])
                val = ",".join(len(params) * ["%s"])
                new_params = [tuple(k) for k in params]
                CFunction.execute(
                    CPgSqlParam(
                        self.insert_sql_template.format(table_name=self.table_name, fields=self.fields, params=val),
                        params=tuple(new_params)))
            if self.flow_type == self.tclog_type:
                name = "通联"
            else:
                name = "NetFlow"
            self.mlog.info("生成{}告警数量：{}".format(name, len(params_list)))
        except Exception as e:
            self.mlog.error("告警数据入库失败：{}".format(e))
            self.mlog.error(traceback.format_exc())
        return uuid_list

    def run(self):
        try:
            # 循环alert_name_type, 过滤出开启的告警类型
            belong = 2 if self.flow_type == 1 else 1
            for k, v in self.alert_name_type.items():
                alert_type = v[0]
                status = get_dynamic_baseline_status(alert_type, belong)
                if not status:
                    self.mlog.info("{} {}:{}告警天模型未开启，跳过".format(self.msg, v[0], v[1]))
                    del self.alert_name_type[k]

            # 过滤后如果没有告警类型开启，直接退出
            if not self.alert_name_type:
                self.mlog.info("没有开启的告警类型，任务结束")
                return
            self.mlog.info("开始滚动查询全量数据")
            res = self.scroll(self.es_index)
            self.mlog.info("数据查询完成")
            data = self.handle_data(res)
            self.mlog.info("{} 数据数量为:{}".format(self.msg, len(data)))
            # 数据入库
            data_params = self.generate_alert(data)
            self.mlog.info("开始插入数据库")
            uuid_list = self.insert_alert(data_params)
            self.mlog.info("插入数据库完成")
            if uuid_list:
                self.mlog.info("开始生成事件")
                if self.flow_type == self.tclog_type:
                    if len(uuid_list) == 1:
                        tc_filter_condition = "uuid = '{}'".format(uuid_list[0])
                    else:
                        tc_filter_condition = "uuid in {}".format(tuple(uuid_list))
                    # self.mlog.info("tc_filter_condition:{}".format(tc_filter_condition))
                    update_tllog_sql(tc_filter_condition)
                else:
                    if len(uuid_list) == 1:
                        filter_condition = "uuid = '{}'".format(uuid_list[0])
                    else:
                        filter_condition = "uuid in {}".format(tuple(uuid_list))
                    # self.mlog.info("filter_condition:{}".format(filter_condition))
                    update_netflow_sql(filter_condition)
                self.mlog.info("生成事件完成")

        except Exception as err:
            self.mlog.error("{}".format(err))
            self.mlog.error(traceback.format_exc())


if __name__ == '__main__':
    today = datetime.date.today()
    stime = int(time.mktime(today.timetuple())) - 86400
    etime = int(time.mktime(today.timetuple()))
    date = str(today - datetime.timedelta(days=1))
    parser = argparse.ArgumentParser(description="非常用端口天告警模型")
    parser.add_argument("--start_time", default=stime, help="开始时间", type=int)
    parser.add_argument("--end_time", default=etime, help="结束时间", type=int)
    args = parser.parse_args()
    mlog.info("接收参数：{}".format(args))
    stime_ = args.start_time
    etime_ = args.end_time
    begin_tiem = time.time()
    alert_name_type = {
        "2_2": (91, "境内向境外非常用端口持续上传数据告警"),  # 主动
        "2_1": (92, "境外从境内非常用端口持续下载数据告警"),  # 被动
        "1_2": (92, "境外从境内非常用端口持续下载数据告警"),  # 被动
        "1_1": (91, "境内向境外非常用端口持续上传数据告警"),  # 主动
    }
    port_list_ = [1005, 46346, 46347]
    # 通联日志
    TCPPortFlowDetect(stime_, etime_, port_list_, alert_name_type, flow_type=1, protocol="tcp").run()

    # NetFlow  指定三个端口
    TCPPortFlowDetect(stime_, etime_, port_list_, alert_name_type, flow_type=2, protocol="tcp").run()
    analysis_tech = 2
    mlog.info("开始进行非常用端口研判")
    UncommonPortJudge(analysis_tech=analysis_tech).run(report_type=1)
    UncommonPortJudge(analysis_tech=analysis_tech).run(report_type=2)
    mlog.info("开始增强资产信息")
    GetAssetInfo(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_,
                 event_type=[91, 92]).enhance_event_asset_info()
    mlog.info("开始增强恶意标签信息")
    EventMaliciousTag().run(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_, event_type=[91, 92])
    mlog.info("开始增强情报信息")
    enhance_event_intelligence_info(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_,
                                    event_type=[91, 92])

    stop_time = time.time()
    mlog.info("总耗时: {} 秒".format(stop_time - begin_tiem))
