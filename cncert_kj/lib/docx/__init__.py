# encoding: utf-8

from cncert_kj.lib.docx.api import Document  # noqa

__version__ = '0.8.7'


# register custom Part classes with opc package reader

from cncert_kj.lib.docx.opc.constants import CONTENT_TYPE as CT, RELATIONSHIP_TYPE as RT
from cncert_kj.lib.docx.opc.part import PartFactory
from cncert_kj.lib.docx.opc.parts.coreprops import CorePropertiesPart

from cncert_kj.lib.docx.parts.document import DocumentPart
from cncert_kj.lib.docx.parts.image import ImagePart
from cncert_kj.lib.docx.parts.numbering import NumberingPart
from cncert_kj.lib.docx.parts.settings import SettingsPart
from cncert_kj.lib.docx.parts.styles import StylesPart


def part_class_selector(content_type, reltype):
    if reltype == RT.IMAGE:
        return ImagePart
    return None


PartFactory.part_class_selector = part_class_selector
PartFactory.part_type_for[CT.OPC_CORE_PROPERTIES] = CorePropertiesPart
PartFactory.part_type_for[CT.WML_DOCUMENT_MAIN] = DocumentPart
PartFactory.part_type_for[CT.WML_NUMBERING] = NumberingPart
PartFactory.part_type_for[CT.WML_SETTINGS] = SettingsPart
PartFactory.part_type_for[CT.WML_STYLES] = StylesPart

del (
    CT, CorePropertiesPart, DocumentPart, NumberingPart, PartFactory,
    StylesPart, part_class_selector
)
