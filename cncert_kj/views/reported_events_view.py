#!/usr/bin/env python
# -*- coding:utf-8 -*-
import datetime
import glob
import hashlib
import io
import json
import operator
import os
import re
import shutil
import time

from collections import defaultdict
from functools import reduce
from io import BytesIO
from urllib import quote
from zipfile import ZipFile

from audit.audit import Audit
from django.db import transaction
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from rest_framework.decorators import list_route
from rest_framework.viewsets import ModelViewSet

from cncert_kj.conf.constant import PATH_APP
from cncert_kj.lib import openpyxl
from cncert_kj.lib.openpyxl.reader.excel import load_workbook
from cncert_kj.lib.openpyxl.utils import get_column_letter
from cncert_kj.lib.openpyxl.writer.excel import save_virtual_workbook
from cncert_kj.models.base_model import ReportedEventsModel, ReportedExceptionType, ContinuousEvents, \
    ContinuousEventsTagModel, WhitelistEventsModel
from cncert_kj.models.base_serializers import ReportedEventsSerializer, WhitelistEventsSerializer
from cncert_kj.utils import logger
from cncert_kj.utils.bytes_trans import long2unit as long2unit_no_slash
from cncert_kj.utils.conf_util import CommonConf
from cncert_kj.utils.time_trans import format3timestamp, timestamp3format, get_months_ago, \
    DATE_FORMAT, timestamp2format
from cncert_kj.views.black_white import check_ips
from cncert_kj.views.cont_event import APPENDIX_LOCAL_PATH

mlog = logger.init_logger('reported_events_view')
REPORTED_EVENTS_FILES_PATH = "/home/<USER>/ISOP/reported_events_files"
auditLog = Audit()

REQUEST_ERROR_CONSTANT = "接口请求失败"
VERIFY_RES_CONSTANT = "校验结果：{}"
UNCOMMON_FOREIGN_PORT_CONSTANT = "非常用端口（境外）"
ATTACHMENT_FILENAME_CONSTANT = 'attachment; filename={}'

class ReportedEventsCheck:
    """异常事件校验"""
    APT_EXCEPTION_TYPE = "APT攻击"
    UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE = UNCOMMON_FOREIGN_PORT_CONSTANT

    def check(self, exception_type=None, src_ips=None, src_ports=None, dst_ips=None, dst_ports=None,
              reported_ids=None):
        """
        校验ip与端口
        Args:
            exception_type (str): 异常类型
            src_ips (str): 境内IP列表
            src_ports (str): 境内端口列表
            dst_ips (str): 境外IP列表
            dst_ports (str): 境外端口列表
            reported_ids (list): 排除的事件ID列表

        Returns:
            str: 错误信息，如果没有错误则返回None
        """
        # 使用策略模式处理不同类型的事件
        handlers = {
            self.APT_EXCEPTION_TYPE: self._check_apt_event,
            self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE: self._check_uncommon_foreign_port_event,
            # 可以在这里添加更多的处理器
        }

        # 获取对应的处理器，如果没有则使用默认处理器
        handler = None
        if exception_type:
            for key in handlers:
                if key in exception_type:
                    handler = handlers[key]
                    break

        # 如果没有找到匹配的处理器，使用默认处理器
        if handler is None:
            handler = self._check_normal_event

        # 调用对应的处理器
        return handler(exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids)

    def _check_apt_event(self, exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids):
        """APT攻击事件的IP和端口校验"""
        # 必填字段校验
        if not src_ips or src_ips.lower() == "none":
            return "APT攻击事件境内IP不能为空"
        if not dst_ips or dst_ips.lower() == "none":
            return "APT攻击事件境外IP不能为空"
        if not dst_ports or dst_ports.lower() == "none":
            return "APT攻击事件境外端口不能为空"

        # 调用通用方法检查境内外IP与境外端口
        return self._check_ip_port_with_foreign_port(
            exception_type, src_ips, dst_ips, dst_ports, reported_ids, "APT攻击事件"
        )

    def _check_uncommon_foreign_port_event(self, exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids):
        """非常用端口（境外）事件的IP和端口校验"""
        # 必填字段校验
        if not src_ips or src_ips.lower() == "none":
            return "{}事件境内IP不能为空".format(self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE)
        if not dst_ips or dst_ips.lower() == "none":
            return "{}事件境外IP不能为空".format(self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE)
        if not dst_ports or dst_ports.lower() == "none":
            return "{}事件境外端口不能为空".format(self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE)

        # 调用通用方法检查境内外IP与境外端口
        return self._check_ip_port_with_foreign_port(
            exception_type, src_ips, dst_ips, dst_ports, reported_ids,
            "{}事件".format(self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE)
        )

    def _check_ip_port_with_foreign_port(self, exception_type, src_ips, dst_ips, dst_ports, reported_ids,
                                         event_type_name):
        """
        通用方法：检查境内外IP与境外端口
        
        Args:
            exception_type (str): 异常类型
            src_ips (str): 境内IP列表
            dst_ips (str): 境外IP列表
            dst_ports (str): 境外端口列表
            reported_ids (list): 排除的事件ID列表
            event_type_name (str): 事件类型名称，用于错误消息
        
        Returns:
            str: 错误信息，如果没有错误则返回None
        """
        # 检查IP格式
        src_ip_list = src_ips.split(",")
        if not check_ips(src_ip_list):
            return "境内IP校验失败"

        dst_ip_list = dst_ips.split(",")
        if not check_ips(dst_ip_list):
            return "境外IP校验失败"

        # 检查端口格式
        try:
            dst_port_list = [int(p.strip()) for p in dst_ports.split(",") if p.strip() and p.strip() != "动态变化"]
        except ValueError:
            return "境外端口格式错误，应为数字或'动态变化'"

        # 查询已存在记录
        instance_list = ReportedEventsModel.objects.filter(exception_type=exception_type)
        if reported_ids:
            instance_list = instance_list.exclude(id__in=reported_ids)

        # 检查是否与已有记录重复
        for instance in instance_list:
            instance_src_ip_list = instance.src_ip.split(",") if instance.src_ip else []
            instance_dst_ip_list = instance.dst_ip.split(",") if instance.dst_ip else []
            instance_dst_port_list = [int(p.strip()) for p in instance.dst_port.split(",")
                                      if p.strip() and p.strip() != "动态变化"] if instance.dst_port else []

            overlap_src_ip = set(instance_src_ip_list) & set(src_ip_list)
            overlap_dst_ip = set(instance_dst_ip_list) & set(dst_ip_list)
            overlap_dst_port = set(instance_dst_port_list) & set(dst_port_list)

            if overlap_src_ip and overlap_dst_ip and overlap_dst_port:
                return "{}境内IP:{},境外IP:{},境外端口:{}已存在".format(
                    event_type_name, overlap_src_ip, overlap_dst_ip, overlap_dst_port)

        return None

    def _check_normal_event(self, exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids):
        """普通事件的IP和端口校验"""
        # 校验境内IP和端口
        error_msg = self._check_ip_port_pair("境内", exception_type, src_ips, src_ports, reported_ids)
        if error_msg:
            return error_msg

        # 校验境外IP和端口
        error_msg = self._check_ip_port_pair("境外", exception_type, dst_ips, dst_ports, reported_ids)
        if error_msg:
            return error_msg

        return None

    def _is_empty_or_none(self, value):
        """检查值是否为空或None"""
        return any([
            not value,
            isinstance(value, basestring) and value.lower() == "none"
        ])

    def _validate_ip_format(self, boundary, ips):
        """验证IP格式"""
        ip_list = ips.split(",")
        if not check_ips(ip_list):
            return "{}IP校验失败".format(boundary)
        return None

    def _validate_port_format(self, boundary, ports):
        """
        验证端口格式

        Returns:
            None: 端口为空或动态变化
            list: 有效的端口列表
            str: 错误信息
        """
        if not all([ports, ports.lower() != "none", ports != "动态变化"]):
            return None

        try:
            port_list = [int(p.strip()) for p in ports.split(",")]
            return port_list
        except ValueError:
            return "{}端口格式错误，应为数字或'动态变化'".format(boundary)

    def _get_query_fields(self, boundary):
        """获取查询字段名"""
        ip_field = "src_ip" if boundary == "境内" else "dst_ip"
        port_field = "src_port" if boundary == "境内" else "dst_port"
        return ip_field, port_field

    def _get_existing_records(self, exception_type, boundary, reported_ids):
        """获取已存在的记录"""
        ip_field, _ = self._get_query_fields(boundary)

        query_kwargs = {"exception_type": exception_type}
        exclude_conditions = [
            {ip_field + "__isnull": True},
            {ip_field: ''}
        ]

        instance_list = ReportedEventsModel.objects.filter(**query_kwargs).exclude(
            Q(**exclude_conditions[0]) | Q(**exclude_conditions[1])
        )

        if reported_ids:
            instance_list = instance_list.exclude(id__in=reported_ids)

        return instance_list

    def _check_duplicate_records(self, boundary, exception_type, ips, ports, reported_ids, port_list):
        """检查记录重复性"""
        ip_field, port_field = self._get_query_fields(boundary)
        instance_list = self._get_existing_records(exception_type, boundary, reported_ids)
        ip_list = ips.split(",")

        for instance in instance_list:
            duplicate_error = self._check_single_record_duplicate(
                instance, ip_field, port_field, boundary, ip_list, port_list, ports
            )
            if duplicate_error:
                return duplicate_error

        return None

    def _check_single_record_duplicate(self, instance, ip_field, port_field, boundary, ip_list, port_list, ports):
        """检查单条记录的重复性"""
        instance_ip = getattr(instance, ip_field)
        instance_port = getattr(instance, port_field)

        # 跳过空IP
        if self._is_empty_or_none(instance_ip):
            return None

        # 跳过空端口或动态变化端口
        if self._is_empty_or_none_port(instance_port):
            return None

        # 检查IP重复
        instance_ip_list = instance_ip.split(",")
        if not (set(instance_ip_list) & set(ip_list)):
            return None

        # 检查端口重复
        try:
            exist_port_list = [int(p.strip()) for p in instance_port.split(",")]
            if set(exist_port_list) & set(port_list):
                return "{}IP:{},与端口:{}已存在".format(boundary, instance_ip, ports)
        except ValueError:
            pass

        return None

    def _is_empty_or_none_port(self, port_value):
        """检查端口值是否为空、None或动态变化"""
        return any([
            not port_value,
            isinstance(port_value, basestring) and port_value.lower() == "none",
            port_value == "动态变化"
        ])

    def _check_ip_port_pair(self, boundary, exception_type, ips, ports, reported_ids):
        """
        校验单边(境内/境外)的IP和端口

        Args:
            boundary (str): 边界类型，"境内"或"境外"
            exception_type (str): 异常类型
            ips (str): IP地址字符串，逗号分隔
            ports (str): 端口字符串，逗号分隔
            reported_ids (list): 需要排除的报告ID列表

        Returns:
            str or None: 错误信息，无错误时返回None
        """
        # 早期返回：检查IP是否为空或None
        if self._is_empty_or_none(ips):
            return None

        # 验证IP格式
        ip_validation_error = self._validate_ip_format(boundary, ips)
        if ip_validation_error:
            return ip_validation_error

        # 验证端口格式
        port_validation_result = self._validate_port_format(boundary, ports)
        if isinstance(port_validation_result, str):  # 错误信息
            return port_validation_result
        elif port_validation_result is None:  # 端口为空或动态变化，无需检查重复
            return None

        # 检查重复性
        return self._check_duplicate_records(boundary, exception_type, ips, ports, reported_ids, port_validation_result)

    # >>
    # def _check_ip_port_pair(self, boundary, exception_type, ips, ports, reported_ids):
    #     """校验单边(境内/境外)的IP和端口"""
    #     if any([not ips, isinstance(ips, basestring) and ips.lower() == "none"]):
    #         return None
    #
    #     # 确定查询字段
    #     ip_field = "src_ip" if boundary == "境内" else "dst_ip"
    #     port_field = "src_port" if boundary == "境内" else "dst_port"
    #
    #     # 构建查询条件
    #     query_kwargs = {"exception_type": exception_type}
    #     # 修复：将字典改为列表，包含两个排除条件
    #     exclude_kwargs = [{ip_field + "__isnull": True}, {ip_field: ''}]
    #     #>> exclude_kwargs = {ip_field + "__isnull": True, ip_field: ''}
    #
    #     # 查询已存在记录
    #     instance_list = ReportedEventsModel.objects.filter(**query_kwargs).exclude(
    #         Q(**exclude_kwargs[0]) | Q(**exclude_kwargs[1]))
    #     if reported_ids:
    #         instance_list = instance_list.exclude(id__in=reported_ids)
    #
    #     # 检查IP格式
    #     ip_list = ips.split(",")
    #     if not check_ips(ip_list):
    #         return "{}IP校验失败".format(boundary)
    #
    #     # 检查端口格式
    #     if not all([ports, ports.lower() != "none", ports != "动态变化"]):
    #         return None
    #     try:
    #         port_list = [int(p.strip()) for p in ports.split(",")]
    #     except ValueError:
    #         return "{}端口格式错误，应为数字或'动态变化'".format(boundary)
    #
    #     # 检查已存在记录
    #     for instance in instance_list:
    #         instance_ip = getattr(instance, ip_field)
    #         instance_port = getattr(instance, port_field)
    #
    #         if any([not instance_ip,
    #                 isinstance(instance_ip, basestring) and instance_ip.lower() == "none"]):
    #             continue
    #         # if not instance_port or instance_port == "None" or instance_port.lower() == "none" or instance_port == "动态变化":
    #         if any([not instance_port,
    #                 isinstance(instance_port, basestring) and instance_port.lower() == "none",
    #                 instance_port == "动态变化"]):
    #             continue
    #
    #         instance_ip_list = instance_ip.split(",")
    #         if set(instance_ip_list) & set(ip_list):
    #             try:
    #                 exist_port_list = [int(p.strip()) for p in instance_port.split(",")]
    #                 if set(exist_port_list) & set(port_list):
    #                     return "{}IP:{},与端口:{}已存在".format(boundary, instance_ip, ports)
    #             except ValueError:
    #                 continue
    #
    #     return None

    def check_required_params(self, data):
        """
            校验必填参数
            非常用端口（境外）必填项为：异常类型exception_type、境内单位src_unit、境内IPsrc_ip、境外IPdst_ip、境外端口dst_port、流出流量up_bytes、时间event_time
        """
        required_params = []
        if data.get("exception_type") == self.UNCOMMON_FOREIGN_PORT_EXCEPTION_TYPE:
            required_params = ["exception_type", "src_unit", "src_ip", "dst_ip", "dst_port", "up_bytes", "event_time"]

        for param in required_params:
            if not data.get(param):
                return "缺少必填参数: {}".format(param)

        return None


class ReportedEventsView(ModelViewSet):
    """
    已通报事件管理页面接口
    """
    serializer_class = ReportedEventsSerializer  # 设置序列化器
    queryset = ReportedEventsModel.objects.all()

    def save_file(self, file_list, instance_id):
        """保存附件"""
        if not os.path.exists(REPORTED_EVENTS_FILES_PATH):
            os.makedirs(REPORTED_EVENTS_FILES_PATH)
        for file in file_list:
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(), hashlib.md5(str(instance_id)).hexdigest())
            # 写入文件
            file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name)
            mlog.info("文件保存路径：{}".format(file_path))
            with open(file_path, 'wb') as f:
                for line in file.chunks():
                    f.write(line)

    def check_ip_port(self, exception_type=None, src_ips=None, src_ports=None, dst_ips=None, dst_ports=None,
                      reported_ids=None):
        """
        校验ip与端口
        Args:
            exception_type (str): 异常类型
            src_ips (str): 境内IP列表
            src_ports (str): 境内端口列表 
            dst_ips (str): 境外IP列表
            dst_ports (str): 境外端口列表

        Returns:
            str: 错误信息，如果没有错误则返回None
        """
        return ReportedEventsCheck().check(exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids)

        # # APT事件特殊处理
        # if "APT" in exception_type:
        #     if not src_ips or src_ips.lower() == "none":
        #         return "APT攻击事件境内IP不能为空"
        #     if not dst_ips or dst_ips.lower() == "none":
        #         return "APT攻击事件境外IP不能为空"
        #     if not dst_ports or dst_ports.lower() == "none":
        #         return "APT攻击事件境外端口不能为空"
        #
        #     src_ips = src_ips.split(",")
        #     dst_ips = dst_ips.split(",")
        #     dst_ports = [int(p.strip()) for p in dst_ports.split(",")]
        #
        #     instance_list = ReportedEventsModel.objects.filter(exception_type=exception_type)
        #     if reported_ids:
        #         instance_list = instance_list.exclude(id__in=reported_ids)
        #     for instance in instance_list:
        #         instance_src_ip_list = instance.src_ip.split(",")
        #         instance_dst_ip_list = instance.dst_ip.split(",")
        #         instance_dst_port_list = [int(p.strip()) for p in instance.dst_port.split(",") if p.strip() and p.strip() != "动态变化"]
        #
        #         overlap_src_ip = set(instance_src_ip_list) & set(src_ips)
        #         overlap_dst_ip = set(instance_dst_ip_list) & set(dst_ips)
        #         overlap_dst_port = set(instance_dst_port_list) & set(dst_ports)
        #         if overlap_src_ip and overlap_dst_ip and overlap_dst_port:
        #             return "APT攻击事件境内IP:{},境外IP:{},境外端口:{}已存在".format(overlap_src_ip, overlap_dst_ip, overlap_dst_port)
        #     return None
        #
        # # 普通事件处理
        # # 校验境内IP和端口
        # if src_ips and src_ips.lower() != "none":
        #     instance_list = ReportedEventsModel.objects.filter(exception_type=exception_type).exclude(
        #         Q(src_ip__isnull=True) | Q(src_ip=''))
        #     if reported_ids:
        #         instance_list = instance_list.exclude(id__in=reported_ids)
        #
        #     # 检查IP格式
        #     src_ip_list = src_ips.split(",")
        #     if not check_ips(src_ip_list):
        #         return "境内IP校验失败"
        #
        #     # 检查端口格式
        #     if src_ports and src_ports.lower() != "none" and src_ports != "动态变化":
        #         try:
        #             src_port_list = [int(p.strip()) for p in src_ports.split(",")]
        #         except ValueError:
        #             return "境内端口格式错误，应为数字或'动态变化'"
        #
        #         # 检查已存在记录
        #         for instance in instance_list:
        #             if not instance.src_ip or instance.src_ip == "None" or instance.src_ip.lower() == "none":
        #                 continue
        #             if not instance.src_port or instance.src_port == "None" or instance.src_port.lower() == "none" or instance.src_port == "动态变化":
        #                 continue
        #
        #             instance_ip_list = instance.src_ip.split(",")
        #             if set(instance_ip_list) & set(src_ip_list):
        #                 try:
        #                     exist_port_list = [int(p.strip()) for p in instance.src_port.split(",")]
        #                     if set(exist_port_list) & set(src_port_list):
        #                         return "境内IP:{},与端口:{}已存在".format(instance.src_ip, src_ports)
        #                 except ValueError:
        #                     continue
        #
        # # 校验境外IP和端口
        # if dst_ips and dst_ips.lower() != "none":
        #     instance_list = ReportedEventsModel.objects.filter(exception_type=exception_type).exclude(
        #         Q(dst_ip__isnull=True) | Q(dst_ip=''))
        #     if reported_ids:
        #         instance_list = instance_list.exclude(id__in=reported_ids)
        #
        #     # 检查IP格式
        #     dst_ip_list = dst_ips.split(",")
        #     if not check_ips(dst_ip_list):
        #         return "境外IP校验失败"
        #
        #     # 检查端口格式
        #     if dst_ports and dst_ports.lower() != "none" and dst_ports != "动态变化":
        #         try:
        #             dst_port_list = [int(p.strip()) for p in dst_ports.split(",")]
        #         except ValueError:
        #             return "境外端口格式错误，应为数字或'动态变化'"
        #
        #         # 检查已存在记录
        #         for instance in instance_list:
        #             if not instance.dst_ip or instance.dst_ip == "None" or instance.dst_ip.lower() == "none":
        #                 continue
        #             if not instance.dst_port or instance.dst_port == "None" or instance.dst_port.lower() == "none" or instance.dst_port == "动态变化":
        #                 continue
        #
        #             instance_ip_list = instance.dst_ip.split(",")
        #             if set(instance_ip_list) & set(dst_ip_list):
        #                 try:
        #                     exist_port_list = [int(p.strip()) for p in instance.dst_port.split(",")]
        #                     if set(exist_port_list) & set(dst_port_list):
        #                         return "境外IP:{},与端口:{}已存在".format(instance.dst_ip, dst_ports)
        #                 except ValueError:
        #                     continue
        #
        # return None

    def _convert_to_bytes(self, size, unit):
        """
        将指定单位的存储大小转换为字节数
        
        Args:
            size (float): 存储大小的数值
            unit (str): 存储单位，可选值为 'KB'、'MB'、'GB'、'TB'
            
        Returns:
            int: 转换后的字节数
            
        Examples:
            _convert_to_bytes(1.5, 'MB')
            1572864
            _convert_to_bytes(1, 'GB')
            1073741824
        """
        if unit == "KB":
            return size * 1024
        elif unit == "MB":
            return size * 1024 * 1024
        elif unit == "GB":
            return size * 1024 * 1024 * 1024
        elif unit == "TB":
            return size * 1024 * 1024 * 1024 * 1024

    def _long2unit(self, bytes):
        """
        将字节数转换为最适合的存储单位表示
        
        Args:
            bytes (int): 要转换的字节数
            
        Returns:
            str: 转换后的字符串，格式为 "数值/单位"
                数值保留2位小数，单位可能为KB、MB、GB或TB
            
        Examples:
            _long2unit(1024)
            "1.00/KB"
            _long2unit(1048576)
            "1.00/MB"
            
        Note:
            - 1KB = 1024 bytes
            - 1MB = 1024 KB
            - 1GB = 1024 MB
            - 1TB = 1024 GB
            - 返回值中的斜杠用于前端显示分隔
        """
        if bytes >= 1024 and bytes < 1024 * 1024:
            return "%.2f/KB" % round(bytes / 1024.0, 2)
        elif bytes >= 1024 * 1024 and bytes < 1024 * 1024 * 1024:
            return "%.2f/MB" % round(bytes / 1024.0 / 1024.0, 2)
        elif bytes >= 1024 * 1024 * 1024 and bytes < 1024 * 1024 * 1024 * 1024:
            return "%.2f/GB" % round(bytes / 1024.0 / 1024.0 / 1024.0, 2)
        else:
            return "%.2f/TB" % round(bytes / 1024.0 / 1024.0 / 1024.0 / 1024.0, 2)

    @list_route(methods=["GET"])
    def detail(self, request):
        """
        详情
        """
        mlog.info("已通报事件详情")
        try:
            event_id = request.GET.get("event_id", "")
            instance = self.queryset.filter(id=event_id).first()
            if not instance:
                return JsonResponse({
                    'code': 500,
                    'msg': '事件不存在'
                })

            serializer = self.serializer_class(instance)
            return JsonResponse({
                'code': 200,
                'msg': '成功',
                'data': serializer.data
            })
        except Exception as e:
            mlog.exception("获取已通报事件详情：%s", str(e))
            return JsonResponse({
                'code': 500,
                'msg': '接口请求失败'
            })

    @list_route(methods=["GET"])
    def reported_events_list(self, request):
        """
        列表页
        """
        mlog.info("已通报事件管理列表")
        try:
            # 获取查询参数
            exception_type = request.GET.get("exception_type", "")
            start_time = request.GET.get("start_time", "")
            end_time = request.GET.get("end_time", "")
            event_source = request.GET.get("event_source", "")
            src_unit = request.GET.get("src_unit", "")
            judge_user = request.GET.get("judge_user", "")
            src_ip = request.GET.get("src_ip", "")
            dst_ip = request.GET.get("dst_ip", "")
            page = int(request.GET.get("page", 1))
            size = int(request.GET.get("size", 10))

            # 构建查询条件
            params = Q()
            if exception_type:
                params &= Q(exception_type=exception_type)
            if start_time and end_time:
                params &= Q(event_time__range=[
                    format3timestamp(start_time),
                    format3timestamp(end_time) + 86400 - 1
                ])
            if event_source:
                params &= Q(event_source__contains=event_source)
            if src_unit:
                params &= Q(src_unit__contains=src_unit)
            if judge_user:
                params &= Q(judge_user__contains=judge_user)
            if src_ip:
                params &= Q(src_ip__contains=src_ip)
            if dst_ip:
                params &= Q(dst_ip__contains=dst_ip)

            # 查询数据
            queryset = self.queryset.filter(params).order_by("-event_time", "-id")
            total = queryset.count()

            # 分页
            start = (page - 1) * size
            end = page * size
            instances = queryset[start:end]

            # 序列化
            serializer = self.serializer_class(instances, many=True)

            return JsonResponse({
                'code': 200,
                'msg': '成功',
                'data': {
                    'total': total,
                    'rows': serializer.data
                }
            })
        except Exception as e:
            mlog.exception("获取已通报事件管理列表：%s", str(e))
            return JsonResponse({
                'code': 500,
                'msg': '接口请求失败'
            })

    @list_route(methods=["POST"])
    def create_reported_events(self, request):
        """
        创建
        """
        mlog.info("创建已通报事件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            username = request.session['username']
            # 创建对象
            files = request.FILES.getlist("files")
            file_name_list = [file.name for file in files]
            if len(file_name_list) != len(set(file_name_list)):
                result["msg"] = "文件重复"
                return JsonResponse(result, status=200)
            #>> data = json.loads(request.body)
            data = request.data
            #>> mlog.info("data:{}".format(data))
            exception_type = data.get("exception_type", "")
            src_unit = data.get("src_unit", "")
            src_ip = data.get("src_ip", "")
            src_port = data.get("src_port", "")
            service_type = data.get("service_type", "")
            dst_ip = data.get("dst_ip", "")
            dst_port = data.get("dst_port", "")
            attack_org = data.get("attack_org", "")

            up_bytes = data.get("up_bytes", 0)
            up_bytes_unit = data.get("up_bytes_unit", "MB")
            if up_bytes != "" and up_bytes is not None:
                up_bytes = self._convert_to_bytes(float(up_bytes), up_bytes_unit)
            else:
                up_bytes = 0

            notes = data.get("notes", "")
            event_source = data.get("event_source", "")
            event_time = data.get("event_time", datetime.datetime.now().strftime(DATE_FORMAT))
            feedback_situation = data.get("feedback_situation", "")

            #>> src_ips = src_ip.split(",") if src_ip else []
            # dst_ips = dst_ip.split(",") if dst_ip else []
            mlog.info("res:{}".format(json.dumps({"exception_type": exception_type,
                                                  "src_unit": src_unit,
                                                  "src_ip": src_ip,
                                                  "src_port": src_port,
                                                  "service_type": service_type,
                                                  "dst_ip": dst_ip,
                                                  "dst_port": dst_port,
                                                  "attack_org": attack_org,
                                                  "up_bytes": up_bytes,
                                                  "notes": notes,
                                                  "event_source": event_source,
                                                  "event_time": format3timestamp(event_time),
                                                  "feedback_situation": feedback_situation}, ensure_ascii=False)
                                      ))
            # 校验ip与端口
            msg = self.check_ip_port(exception_type, src_ip, src_port, dst_ip, dst_port)
            if msg:
                result["msg"] = msg
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return JsonResponse(result, status=200)
            create_data = {
                "exception_type": exception_type,
                "src_unit": src_unit,
                "src_ip": src_ip,
                "src_port": src_port,
                "service_type": service_type,
                "dst_ip": dst_ip,
                "dst_port": dst_port,
                "attack_org": attack_org,
                "up_bytes": up_bytes,
                "notes": notes,
                "event_source": event_source,
                "event_time": format3timestamp(event_time),
                "feedback_situation": feedback_situation,
                "judge_user": username
            }
            params_error = ReportedEventsCheck().check_required_params(create_data)
            if params_error:
                result["msg"] = params_error
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return JsonResponse(result, status=200)
            instance = ReportedEventsModel.objects.create(**create_data)
            # 文件名存入数据库
            instance.files = ",".join(file_name_list)
            instance.save()
            # 本地目录是否存在，不存在则创建
            if not os.path.exists(REPORTED_EVENTS_FILES_PATH):
                os.mkdir(REPORTED_EVENTS_FILES_PATH)
            # 文件写入本地
            self.save_file(files, instance.id)

            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "创建已通报事件成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("创建已通报事件：{}".format(e))

        return JsonResponse(result, status=200)

    @staticmethod
    def _delete_reported_events_file(reported_events_id, file_name_list):
        """
            删除已通报事件附件
        """
        for file_name in file_name_list:
            file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                       hashlib.md5(str(reported_events_id)).hexdigest())

            file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name)
            if os.path.exists(file_path):
                os.remove(file_path)

    def _delete_reported_events(self, event_ids):
        obj_list = ReportedEventsModel.objects.filter(id__in=event_ids)
        for obj in obj_list:
            if obj.files:
                self._delete_reported_events_file(obj.id, obj.files.split(","))
            obj.delete()

    @list_route(methods=["POST"])
    def delete_reported_events(self, request):
        """
        删除
        """
        mlog.info("删除已通报事件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = request.body
            event_ids = json.loads(data).get("event_ids")
            # 删除事件
            self._delete_reported_events(event_ids)

            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "删除已通报事件成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("删除已通报事件：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def update_reported_events(self, request):
        """
        修改
        """
        mlog.info("编辑已通报事件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            event_ids = data.get("event_ids")
            update_data = {}

            # 定义需要处理的字段列表
            fields = [
                "exception_type", "src_unit", "src_ip", "src_port",
                "service_type", "dst_ip", "dst_port", "attack_org", "notes",
                "event_source", "feedback_situation"
            ]

            # 处理所有字段，包括空值
            for field in fields:
                if field in data:  # 只要字段在请求中存在，就更新它
                    update_data[field] = data.get(field, "")

            # 特殊处理流量字段
            up_bytes = data.get("up_bytes", 0)
            up_bytes_unit = data.get("up_bytes_unit", "MB")
            if "up_bytes" in data:  # 只要字段在请求中存在
                if up_bytes is not None and up_bytes != "":
                    update_data["up_bytes"] = int(self._convert_to_bytes(float(up_bytes), up_bytes_unit))
                else:
                    update_data["up_bytes"] = 0

            # 特殊处理时间字段
            if "event_time" in data:
                event_time = data.get("event_time")
                update_data["event_time"] = format3timestamp(event_time) if event_time else None

            # 校验ip与端口
            msg = self.check_ip_port(update_data.get("exception_type"),
                                     update_data.get("src_ip"),
                                     update_data.get("src_port"),
                                     update_data.get("dst_ip"),
                                     update_data.get("dst_port"),
                                     event_ids,
                                     )
            if msg:
                result["msg"] = msg
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return JsonResponse(result, status=200)
            params_error = ReportedEventsCheck().check_required_params(update_data)
            if params_error:
                result["msg"] = params_error
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return JsonResponse(result, status=200)
            #>> # 判断是否为白象事件
            # is_white_elephant = True if self.WHITE_ELEPHANT in data.get("attack_org", "") else False
            #
            # # 校验ip是否已存在
            # if update_data.get("src_ip"):
            #     filter_data = ReportedEventsModel.objects.filter(
            #         exception_type=update_data.get("exception_type")).exclude(
            #         id__in=event_ids).exclude(Q(src_ip__isnull=True) | Q(src_ip=''))
            #
            #     msg = self.check_ip_port("境内", update_data.get("src_ip"),
            #                              update_data.get("src_port") if not is_white_elephant else update_data.get(
            #                                  "dst_port"), filter_data, is_white_elephant)
            #     if msg:
            #         mlog.info("msg:{}".format(msg))
            #         result["msg"] = msg
            #         return JsonResponse(result, status=200)
            #
            # if update_data.get("dst_ip") and not is_white_elephant:
            #     filter_data = ReportedEventsModel.objects.filter(
            #         exception_type=update_data.get("exception_type")).exclude(
            #         id__in=event_ids).exclude(Q(dst_ip__isnull=True) | Q(dst_ip=''))
            #
            #     msg = self.check_ip_port("境外", update_data.get("dst_ip"), update_data.get("dst_port"), filter_data)
            #     if msg:
            #         mlog.info("msg:{}".format(msg))
            #         result["msg"] = msg
            #         return JsonResponse(result, status=200)

            ReportedEventsModel.objects.filter(id__in=event_ids).update(**update_data)

            result["code"] = 200
            result["msg"] = "成功"

            # 记录更新内容
            mlog.info("更新数据: %s", update_data)
            auditLog(request, "编辑已通报事件成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("修改已通报事件：%s", e)
        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def download_file(self, request):
        """
        下载文件
        """
        mlog.info("获取已通报事件文件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            event_id = request.GET.get("event_id")
            file_name = request.GET.get("file_name")
            full_file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(), hashlib.md5(str(event_id)).hexdigest())
            file_path = "{}/{}".format(REPORTED_EVENTS_FILES_PATH, full_file_name)
            if os.path.exists(file_path):
                with open(file_path) as f:
                    response = HttpResponse(f, content_type='application/octet-stream')
                    response['Content-Disposition'] = ATTACHMENT_FILENAME_CONSTANT.format(quote(file_name.encode('utf-8')))
                    auditLog(request, "下载已通报事件附件报告:{}  成功".format(file_name))
            else:
                response = HttpResponse("no such file", status=500, )
            return response
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("获取已通报事件文件：{}".format(e))
            return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def upload_file(self, request):
        """
        上传文件
        """
        mlog.info("上传已通报事件附件报告")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            files = request.FILES.getlist("files")
            event_id = request.POST.get("event_id")
            # 根据id查询事件已有文件名
            instance = ReportedEventsModel.objects.get(id=event_id)
            if instance.files is None or instance.files == "":
                instance_files = []
            else:
                instance_files = instance.files.split(",")
            # 上传的文件名
            updaload_file_name_list = [file.name for file in files]
            # 事件已有的文件名 + 上传的文件名
            exist_file_name_list = updaload_file_name_list + instance_files

            # 判断是否重复
            if len(exist_file_name_list) != len(set(exist_file_name_list)):
                # 上传文件重复，返回错误信息
                result["msg"] = "文件重复"
                return JsonResponse(result, status=200)

            # 不重复，新增文件
            self.save_file(files, event_id)

            # 将文件名存入数据库对应事件
            instance.files = ",".join(exist_file_name_list)
            instance.save()
            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "上传已通报事件附件报告:{}  成功".format(updaload_file_name_list))
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("上传已通报事件附件报告：{}".format(e))

        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def delete_file(self, request):
        """
        删除文件
        """
        mlog.info("删除已通报事件附件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            event_id = data.get("event_id")
            file_name = data.get("file_name")
            instance = ReportedEventsModel.objects.get(id=event_id)
            if file_name not in instance.files:
                result["msg"] = "文件不存在"
                return JsonResponse(result, status=200)
            else:
                file_name_list = instance.files.split(",")
                if file_name in file_name_list:
                    file_name_list.remove(file_name)
                    instance.files = ",".join(file_name_list)
                    instance.save()
                    # 删除文件
                    file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                               hashlib.md5(str(instance.id)).hexdigest())

                    file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name)
                    if os.path.exists(file_path):
                        os.remove(file_path)

            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "删除已通报事件附件:{}  成功".format(file_name))
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("删除已通报事件附件：{}".format(e))

        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def export_template(self, request):
        """
        导出模板
        """
        mlog.info("导出已通报事件模板")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            # 导出数据
            workbook = openpyxl.Workbook()
            # 删除默认创建的空白工作表
            workbook.remove(workbook.active)
            # 向工作表中添加数据

            sheet = workbook.create_sheet(title=u"{}".format("流媒体"))
            # 添加表头
            headers = ["境内单位", "境内IP", "境内端口", "服务类型", "境外IP", "境外端口", "攻击组织", "流出流量",
                       "备注", "事件来源", "事件时间", "反馈情况"]
            sheet.append(headers)
            # 添加示例数据
            sheet.append(
                ["***", "*******,*******", "111,114", "video/MP2T,流媒体传输", "*******,*******", "动态变化", "白象",
                 "10.00GB", "", "境外从境内视频设备下载数据事件", "2025-02-09", ""])

            # 设置所有单元格格式为文本
            for row in sheet.iter_rows():
                for cell in row:
                    cell.number_format = '@'

            # 自动调整列宽
            for col_idx, header in enumerate(headers, 1):
                max_length = len(str(header))  # 初始化为表头长度
                # 获取该列所有单元格
                for cell in sheet[get_column_letter(col_idx)]:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except Exception as _:
                        pass
                # 设置列宽（稍微增加一点余量）
                adjusted_width = (max_length + 2) * 1.2
                sheet.column_dimensions[get_column_letter(col_idx)].width = adjusted_width

            filename = "已通报事件导入模板.xlsx"
            # 设置响应头，以便浏览器将其识别为文件下载
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = ATTACHMENT_FILENAME_CONSTANT.format(quote(filename))
            # 将工作簿保存到响应对象中，并将其作为文件下载
            response.content = save_virtual_workbook(workbook)
            return response
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("导出已通报事件模板：{}".format(e))
            return JsonResponse(result, status=200)

    def _export_build_query_params(self, data):
        """构建查询参数Q对象"""
        params = Q()
        id_list = data.get("event_ids", [])
        if id_list:
            params &= Q(id__in=id_list)
        else:
            if data.get("exception_type"):
                params &= Q(exception_type=data["exception_type"])
            if data.get("start_time") and data.get("end_time"):
                params &= Q(event_time__range=[
                    format3timestamp(data["start_time"]),
                    format3timestamp(data["end_time"]) + 86400 - 1
                ])
            if data.get("event_source"):
                params &= Q(event_source__contains=data["event_source"])
            if data.get("src_unit"):
                params &= Q(src_unit__contains=data["src_unit"])
            if data.get("judge_user", ""):
                params &= Q(judge_user__contains=data["judge_user"])
            if data.get("src_ip"):
                params &= Q(src_ip__contains=data["src_ip"])
            if data.get("dst_ip"):
                params &= Q(dst_ip__contains=data["dst_ip"])
        return params

    @list_route(methods=["POST"])
    def export_reported_events(self, request):
        """
        导出已通报事件
        """
        mlog.info("导出已通报事件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            # >>
            # exception_type = data.get("exception_type", "")
            # start_time = data.get("start_time", "")
            # end_time = data.get("end_time", "")
            # event_source = data.get("event_source", "")
            # src_unit = data.get("src_unit", "")
            # judge_user = data.get("judge_user", "")
            # src_ip = data.get("src_ip", "")
            # dst_ip = data.get("dst_ip", "")
            # id_list = data.get("event_ids", [])
            mlog.info("导出已通报事件参数：{}".format(json.dumps(data, ensure_ascii=False)))

            # >> 构建查询条件
            # params = Q()
            # if id_list:
            #     params &= Q(id__in=id_list)
            # else:
            #     if exception_type:
            #         params &= Q(exception_type=exception_type)
            #     if start_time and end_time:
            #         params &= Q(event_time__range=[
            #             format3timestamp(start_time),
            #             format3timestamp(end_time) + 86400 - 1
            #         ])
            #     if event_source:
            #         params &= Q(event_source__contains=event_source)
            #     if src_unit:
            #         params &= Q(src_unit__contains=src_unit)
            #     if judge_user:
            #         params &= Q(judge_user__contains=judge_user)
            #     if src_ip:
            #         params &= Q(src_ip__contains=src_ip)
            #     if dst_ip:
            #         params &= Q(dst_ip__contains=dst_ip)
            params = self._export_build_query_params(data)
            # 查询数据
            res_obj_list = self.queryset.filter(params).order_by("-event_time", "-id")

            res_data = defaultdict(list)
            for i in res_obj_list:
                if i.exception_type is None or i.exception_type == "":
                    i.exception_type = "无异常类型"

                res_data[i.exception_type].append({
                    "id": i.id,
                    "exception_type": i.exception_type,
                    "src_unit": i.src_unit,
                    "src_ip": i.src_ip,
                    "src_port": i.src_port,
                    "service_type": i.service_type,
                    "dst_ip": i.dst_ip,
                    "dst_port": i.dst_port,
                    "attack_org": i.attack_org,
                    "up_bytes": long2unit_no_slash(i.up_bytes),
                    "notes": i.notes,
                    "event_source": i.event_source,
                    "event_time": timestamp3format(i.event_time, DATE_FORMAT),
                    "feedback_situation": i.feedback_situation,
                    "judge_user": i.judge_user,
                })

            # 导出数据
            workbook = openpyxl.Workbook()
            # 删除默认创建的空白工作表
            workbook.remove(workbook.active)
            # 向工作表中添加数据

            for exception_type, data in res_data.items():
                sheet = workbook.create_sheet(title=u"{}".format(exception_type))
                sheet.append(
                    ["序号", "异常类型", "境内单位", "境内IP", "境内端口", "服务类型", "境外IP", "境外端口", "攻击组织",
                     "流出流量", "备注", "事件来源", "事件时间", "反馈情况", "研判账号"])
                num = 0
                for row in data:
                    num += 1
                    sheet.append(
                        [num, row["exception_type"], row["src_unit"], row["src_ip"], row["src_port"],
                         row["service_type"], row["dst_ip"], row["dst_port"], row["attack_org"], row["up_bytes"],
                         row["notes"], row["event_source"], row["event_time"], row["feedback_situation"],
                         row["judge_user"]])

            filename = "已通报事件_{}.xlsx".format(datetime.datetime.now().strftime("%Y%m%d%H%M%S"))
            # 设置响应头，以便浏览器将其识别为文件下载
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = ATTACHMENT_FILENAME_CONSTANT.format(quote(filename))
            # 将工作簿保存到响应对象中，并将其作为文件下载
            response.content = save_virtual_workbook(workbook)
            auditLog(request, "导出已通报事件成功")
            return response
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("导出已通报事件：{}".format(e))
            return JsonResponse(result, status=200)

    def str2bytes(self, traffic_str):
        # 定义单位到字节的映射
        unit_map = {
            'B': 1,  # 字节
            'KB': 1024,  # 千字节
            'MB': 1024 ** 2,  # 兆字节
            'GB': 1024 ** 3,  # 吉字节
            'TB': 1024 ** 4,  # 太字节
        }

        # 去除空格并将字符串转换为大写
        traffic_str = traffic_str.strip().upper()

        # 找到单位
        unit = ''
        for key in ['GB', 'MB', 'KB', 'B']:
            if traffic_str.endswith(key):
                unit = key
                break

        # 如果没有找到单位，默认为字节
        if not unit:
            unit = 'B'

        # 提取数值部分
        value = float(traffic_str[:-len(unit)])

        # 转换为字节
        bytes_value = value * unit_map[unit]

        return int(bytes_value)

    def _parse_excel(self, file):
        """解析Excel文件，返回工作簿对象或错误信息"""
        if not file:
            return None, "请选择要导入的文件"
        if not file.name.endswith((".xls", ".xlsx")):
            return None, "只支持导入.xls或.xlsx格式的Excel文件"
        try:
            excel_data = BytesIO(file.read())
            wb = load_workbook(excel_data, data_only=True)
            return wb, None
        except Exception as e:
            return None, "Excel文件读取失败: {}".format(str(e))

    def _parse_row(self, row, row_num, exception_type, str2bytes, check_ip_port):
        """解析并校验单行数据，返回create_data或错误信息"""
        if len(row) < 12:
            return None, "第{row_num}行数据列数不足，请检查是否完整".format(row_num=row_num)
        row = ['' if str(i).lower() == 'none' else str(i) for i in row]
        src_unit, src_ip, src_port, service_type, dst_ip, dst_port, attack_org, up_bytes_str, notes, event_source, event_time, feedback_situation = row[
                                                                                                                                                    :12]
        # 流量转换
        if up_bytes_str:
            try:
                up_bytes = str2bytes(up_bytes_str)
            except Exception:
                return None, "第{row_num}行流量格式错误: {up_bytes_str}".format(row_num=row_num,
                                                                                up_bytes_str=up_bytes_str)
        else:
            up_bytes = 0
        # 校验ip与端口
        msg = check_ip_port(exception_type, src_ip, src_port, dst_ip, dst_port)
        if msg:
            return None, "第{row_num}行{msg}".format(row_num=row_num, msg=msg)
        # 时间格式验证
        try:
            event_time_fmt = format3timestamp(event_time)
        except Exception:
            return None, "第{row_num}行事件时间格式错误: {row}".format(row_num=row_num, row=event_time)
        create_data = {
            "exception_type": exception_type,
            "src_unit": src_unit,
            "src_ip": src_ip,
            "src_port": src_port,
            "service_type": service_type,
            "dst_ip": dst_ip,
            "dst_port": dst_port,
            "attack_org": attack_org,
            "up_bytes": up_bytes,
            "notes": notes,
            "event_source": event_source,
            "event_time": event_time_fmt,
            "feedback_situation": feedback_situation,
        }
        return create_data, None

    @list_route(methods=["POST"])
    def import_reported_events(self, request):
        """
        导入已通报事件（重构版，降低嵌套复杂度）
        """
        mlog.info("导入已通报事件")
        result = {"code": 500, "msg": "error"}
        try:
            file = request.FILES.get("file")
            wb, file_error = self._parse_excel(file)
            if file_error:
                result["msg"] = file_error
                return JsonResponse(result, status=200)
            import_stats = {"success": 0, "failed": 0, "errors": []}
            for sheet in wb.sheetnames:
                sheet_obj = wb[sheet]
                row_num = 2
                exception_type = sheet
                mlog.info("异常类型：{}".format(exception_type))
                for row in sheet_obj.iter_rows(min_row=2, values_only=True):
                    if not any(row):
                        continue
                    create_data, row_error = self._parse_row(row, row_num, exception_type, self.str2bytes, self.check_ip_port)
                    if row_error:
                        import_stats["failed"] += 1
                        import_stats["errors"].append(row_error)
                    else:
                        try:
                            ReportedEventsModel.objects.update_or_create(**create_data)
                            import_stats["success"] += 1
                        except Exception as e:
                            import_stats["failed"] += 1
                            import_stats["errors"].append("第{}行数据库写入失败: {}".format(row_num, str(e)))
                    row_num += 1
            # 结果处理
            if import_stats["failed"] == 0:
                result["code"] = 200
                auditLog(request, "导入已通报事件成功，共导入{}条数据".format(import_stats['success']))
                result["msg"] = "导入成功，共导入{}条数据".format(import_stats['success'])
            elif import_stats['success'] == 0:
                result["code"] = 500
                result["msg"] = "导入失败，共导入{}条数据。失败原因：\n".format(import_stats['failed']) + "\n".join(
                    import_stats['errors'])
            else:
                result["code"] = 2000
                auditLog(request, "部分已通报事件导入成功。成功:{}条,失败:{}条。失败原因：\n".format(
                    import_stats['success'], import_stats['failed']) + "\n".join(import_stats["errors"]))
                result["msg"] = "部分导入成功。成功:{}条,失败:{}条。失败原因：\n".format(
                    import_stats['success'], import_stats['failed']) + "\n".join(import_stats["errors"])
        except Exception as e:
            result["msg"] = "导入失败: {}".format(str(e))
            mlog.exception("导入已通报事件失败：{}".format(e))
        mlog.info("导入已通报事件result：{}".format(json.dumps(result, ensure_ascii=False, indent=4)))
        return JsonResponse(result, status=200)


    # >>
    # @list_route(methods=["POST"])
    # def import_reported_events(self, request):
    #     """
    #     导入已通报事件
    #     """
    #     mlog.info("导入已通报事件")
    #     result = {
    #         "code": 500,
    #         "msg": "error"
    #     }
    #     try:
    #         # 检查文件是否上传
    #         file = request.FILES.get("file")
    #         if file is None:
    #             result["msg"] = "请选择要导入的文件"
    #             return JsonResponse(result, status=200)
    #
    #         # 检查文件格式
    #         if not file.name.endswith(('.xls', '.xlsx')):
    #             result["msg"] = "只支持导入.xls或.xlsx格式的Excel文件"
    #             return JsonResponse(result, status=200)
    #
    #         try:
    #             # 使用 BytesIO 将文件对象转换为二进制流
    #             excel_data = BytesIO(file.read())
    #             wb = load_workbook(excel_data, data_only=True)
    #         except Exception as e:
    #             result["msg"] = "Excel文件读取失败: {}".format(str(e))
    #             return JsonResponse(result, status=200)
    #
    #         # 导入结果统计
    #         import_stats = {
    #             "success": 0,  # 成功导入数量
    #             "failed": 0,  # 失败数量
    #             "errors": []  # 错误信息列表
    #         }
    #
    #         sheet_names = wb.sheetnames
    #         for sheet in sheet_names:
    #             sheet_obj = wb[sheet]
    #             row_num = 2  # 从第二行开始读取
    #
    #             exception_type = sheet
    #             mlog.info("异常类型：{}".format(exception_type))
    #             for row in sheet_obj.iter_rows(min_row=2, values_only=True):
    #                 if not any(row):  # 跳过空行
    #                     continue
    #
    #                 try:
    #                     # 数据验证
    #                     if len(row) < 12:
    #                         raise ValueError("第{row_num}行数据列数不足，请检查是否完整".format(row_num=row_num))
    #
    #                     # 处理Excel中的数据，将"None"转换为空字符串
    #                     row = ['' if str(i).lower() == 'none' else str(i) for i in row]
    #                     mlog.info("row：{}".format(row))
    #                     # 列出所有的字段
    #                     src_unit = row[0]
    #                     src_ip = row[1]
    #                     src_port = row[2]
    #                     service_type = row[3]
    #                     dst_ip = row[4]
    #                     dst_port = row[5]
    #                     attack_org = row[6]
    #                     up_bytes_str = row[7]
    #                     notes = row[8]
    #                     event_source = row[9]
    #                     event_time = row[10]
    #                     feedback_situation = row[11]
    #
    #                     # 流量转换
    #                     if up_bytes_str:
    #                         try:
    #                             up_bytes = self.str2bytes(up_bytes_str)
    #                         except Exception:
    #                             raise ValueError("第{row_num}行流量格式错误: {up_bytes_str}".format(row_num=row_num,
    #                                                                                         up_bytes_str=up_bytes_str))
    #                     else:
    #                         up_bytes = 0
    #
    #                     # 校验ip与端口
    #                     msg = self.check_ip_port(exception_type, src_ip, src_port, dst_ip, dst_port)
    #                     if msg:
    #                         result["msg"] = msg
    #                         mlog.info(VERIFY_RES_CONSTANT.format(msg))
    #                         raise ValueError("第{row_num}行{msg}".format(row_num=row_num, msg=msg))
    #
    #                     #>> # IP校验
    #                     # is_white_elephant = True if self.WHITE_ELEPHANT in attack_org else False
    #                     # if src_ip:  # 境内IP
    #                     #     if not exception_type:
    #                     #         filter_data = ReportedEventsModel.objects.filter(
    #                     #             Q(exception_type="") | Q(exception_type__isnull=True)).exclude(
    #                     #             Q(src_ip__isnull=True) | Q(src_ip=''))
    #                     #     else:
    #                     #         filter_data = ReportedEventsModel.objects.exclude(
    #                     #             Q(src_ip__isnull=True) | Q(src_ip='')).filter(exception_type=exception_type)
    #                     #
    #                     #     msg = self.check_ip_port("境内", src_ip, src_port if not is_white_elephant else dst_port,
    #                     #                              filter_data, is_white_elephant)
    #                     #     if msg:
    #                     #         raise ValueError("第{row_num}行{msg}".format(row_num=row_num, msg=msg))
    #                     #
    #                     # if dst_ip and not is_white_elephant:  # 境外IP
    #                     #     if not exception_type:
    #                     #         filter_data = ReportedEventsModel.objects.filter(
    #                     #             Q(exception_type="") | Q(exception_type__isnull=True)).exclude(
    #                     #             Q(dst_ip__isnull=True) | Q(dst_ip=''))
    #                     #     else:
    #                     #         filter_data = ReportedEventsModel.objects.exclude(
    #                     #             Q(dst_ip__isnull=True) | Q(dst_ip='')).filter(exception_type=exception_type)
    #                     #
    #                     #     msg = self.check_ip_port("境外", dst_ip, dst_port, filter_data)
    #                     #     if msg:
    #                     #         raise ValueError("第{row_num}行{msg}".format(row_num=row_num, msg=msg))
    #
    #                     # 时间格式验证
    #                     try:
    #                         event_time = format3timestamp(event_time)
    #                     except Exception:
    #                         raise ValueError(
    #                             "第{row_num}行事件时间格式错误: {row}".format(row_num=row_num, row=event_time))
    #
    #                     create_data = {
    #                         "exception_type": exception_type,
    #                         "src_unit": src_unit,
    #                         "src_ip": src_ip,
    #                         "src_port": src_port,
    #                         "service_type": service_type,
    #                         "dst_ip": dst_ip,
    #                         "dst_port": dst_port,
    #                         "attack_org": attack_org,
    #                         "up_bytes": up_bytes,
    #                         "notes": notes,
    #                         "event_source": event_source,
    #                         "event_time": event_time,
    #                         "feedback_situation": feedback_situation,
    #                     }
    #                     mlog.info("create_data:{}".format(json.dumps(create_data, ensure_ascii=False)))
    #
    #                     # 创建
    #                     ReportedEventsModel.objects.update_or_create(**create_data)
    #                     import_stats["success"] += 1
    #
    #                 except Exception as e:
    #                     import_stats["failed"] += 1
    #                     import_stats["errors"].append(str(e))
    #                     # mlog.exception("第{}行，导入已通报事件失败：{}".format(row_num, e))
    #
    #                 row_num += 1
    #
    #         # 根据导入结果返回相应信息
    #         if import_stats["failed"] == 0:
    #             result["code"] = 200
    #             auditLog(request, "导入已通报事件成功，共导入{}条数据".format(import_stats['success']))
    #             result["msg"] = "导入成功，共导入{}条数据".format(import_stats['success'])
    #         elif import_stats['success'] == 0:
    #             result["code"] = 500
    #             result["msg"] = "导入失败，共导入{}条数据。失败原因：\n".format(import_stats['failed']) + "\n".join(
    #                 import_stats['errors'])
    #
    #         else:
    #             result["code"] = 2000
    #             auditLog(request, "部分已通报事件导入成功。成功:{}条,失败:{}条。失败原因：\n".format(
    #                 import_stats['success'], import_stats['failed']) + "\n".join(import_stats["errors"]))
    #             result["msg"] = "部分导入成功。成功:{}条,失败:{}条。失败原因：\n".format(
    #                 import_stats['success'], import_stats['failed']) + "\n".join(import_stats["errors"])
    #     except Exception as e:
    #         result["msg"] = "导入失败: {}".format(str(e))
    #         mlog.exception("导入已通报事件失败：{}".format(e))
    #
    #     mlog.info("导入已通报事件result：{}".format(json.dumps(result, ensure_ascii=False, indent=4)))
    #     return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def create_exception_type(self, request):
        """
        新增异常类型
        """
        mlog.info("新增异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            if not data.get("exception_type"):
                result["msg"] = "异常类型不能为空"
                return JsonResponse(result, status=200)
            if ReportedExceptionType.objects.filter(exception_type=data.get("exception_type")).exists():
                result["msg"] = "异常类型已存在"
                return JsonResponse(result, status=200)
            else:
                ReportedExceptionType.objects.create(exception_type=data.get("exception_type"))
                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "新增已通报事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("新增异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def get_exception_type(self, request):
        """
        获取异常类型
        """
        mlog.info("获取异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            exception_type = ReportedExceptionType.objects.all()
            result["code"] = 200
            result["msg"] = "成功"
            result["data"] = [{"id": i.id, "exception_type": i.exception_type} for i in exception_type]
            mlog.info("获取异常类型：{}".format(json.dumps(result, ensure_ascii=False)))
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("获取异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def update_exception_type(self, request):
        """
        更新异常类型
        """
        mlog.info("更新异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            if not data.get("type_id"):
                result["msg"] = "id不能为空"
                return JsonResponse(result, status=200)
            if not data.get("exception_type"):
                result["msg"] = "异常类型不能为空"
                return JsonResponse(result, status=200)
            instance = ReportedExceptionType.objects.filter(id=data.get("type_id")).first()
            if not instance:
                result["msg"] = "异常类型不存在"
                return JsonResponse(result, status=200)
            old_exception_type = instance.exception_type
            instance.exception_type = data.get("exception_type")
            mlog.info("更新异常类型完成：{}-->>{}".format(old_exception_type, instance.exception_type))
            instance.save()
            # 更新已通报事件的异常类型
            ReportedEventsModel.objects.filter(exception_type=old_exception_type).update(
                exception_type=data.get("exception_type"))
            mlog.info("更新已通报事件的异常类型完成：{}-->>{}".format(old_exception_type, data.get("exception_type")))
            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "更新已通报事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("更新异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def delete_exception_type(self, request):
        """
        删除异常类型
        """
        mlog.info("删除异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            if not data.get("type_id"):
                result["msg"] = "id不能为空"
                return JsonResponse(result, status=200)

            _instance = ReportedExceptionType.objects.filter(id=data.get("type_id")).first()
            # 查询已通报事件中是否存在该异常类型
            reported_events = ReportedEventsModel.objects.filter(exception_type=_instance.exception_type)
            if reported_events.exists():
                result["msg"] = "异常类型已存在已通报事件中，不能删除"
                return JsonResponse(result, status=200)
            else:
                ReportedExceptionType.objects.filter(id=data.get("type_id")).delete()
                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "删除已通报事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR_CONSTANT
            mlog.exception("删除异常类型：{}".format(e))

        return JsonResponse(result, status=200)

    def _build_query_params(self, data):
        """构建查询参数Q对象"""
        params = Q()
        id_list = data.get("event_ids", [])
        if id_list:
            params &= Q(id__in=id_list)
        else:
            if data.get("exception_type"):
                params &= Q(exception_type=data["exception_type"])
            if data.get("start_time") and data.get("end_time"):
                params &= Q(event_time__range=[
                    format3timestamp(data["start_time"]),
                    format3timestamp(data["end_time"]) + 86400 - 1
                ])
            if data.get("event_source"):
                params &= Q(event_source__contains=data["event_source"])
            if data.get("src_unit"):
                params &= Q(src_unit__contains=data["src_unit"])
            if data.get("src_ip"):
                params &= Q(src_ip__contains=data["src_ip"])
            if data.get("dst_ip"):
                params &= Q(dst_ip__contains=data["dst_ip"])
        return params

    def _collect_files(self, event_list):
        """收集所有需要打包的文件，返回文件名和内容的元组列表"""
        files_to_zip = []
        for instance in event_list:
            mlog.info("处理事件ID：{}，文件列表：{}".format(instance.id, instance.files))
            if not instance.files:
                continue
            for file_name in instance.files.split(","):
                file_name_hashed = "{}.{}".format(
                    hashlib.md5(file_name.encode('utf-8')).hexdigest(),
                    hashlib.md5(str(instance.id).encode('utf-8')).hexdigest()
                )
                file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name_hashed)
                mlog.info("处理文件：{}, 哈希后的文件名：{}, 完整路径：{}".format(
                    file_name, file_name_hashed, file_path))
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'rb') as f:
                            file_content = f.read()
                            mlog.info("读取文件成功，文件大小：{} 字节".format(len(file_content)))
                            files_to_zip.append((file_name, file_content))
                    except Exception as e:
                        mlog.error("读取文件失败：{}，错误：{}".format(file_path, str(e)))
                else:
                    mlog.error("文件不存在：{}".format(file_path))
        return files_to_zip

    def _create_zip_response(self, files_to_zip):
        """根据文件内容生成zip响应"""
        if not files_to_zip:
            return None, "未找到任何可下载的文件"
        zip_buffer = io.BytesIO()
        with ZipFile(zip_buffer, 'w') as zip_file:
            for file_name, file_content in files_to_zip:
                zip_file.writestr(file_name, file_content)
        zip_content = zip_buffer.getvalue()
        mlog.info("ZIP文件生成完成，成功添加文件数：{}，ZIP文件大小：{} 字节".format(
            len(files_to_zip), len(zip_content)))
        response = HttpResponse(
            zip_content,
            content_type='application/zip'
        )
        response['Content-Disposition'] = ATTACHMENT_FILENAME_CONSTANT.format(
            quote("已通报事件附件.zip".encode('utf-8'))
        )
        return response, None

    @list_route(methods=["POST"])
    def batch_download_files(self, request):
        """
        批量导出已通报事件附件（重构版，降低嵌套复杂度）
        """
        mlog.info("批量导出已通报事件附件")
        result = {"code": 500, "msg": "error"}
        try:
            data = json.loads(request.body)
            mlog.info("导出已通报事件报告参数：{}".format(json.dumps(data, ensure_ascii=False)))
            params = self._build_query_params(data)
            event_list = ReportedEventsModel.objects.filter(params)
            mlog.info("查询到的事件数量：{}".format(event_list.count()))
            files_to_zip = self._collect_files(event_list)
            response, error_msg = self._create_zip_response(files_to_zip)
            if error_msg:
                result["msg"] = error_msg
                return JsonResponse(result, status=200)
            auditLog(request, "批量导出已通报事件成功")
            return response
        except Exception as e:
            result["msg"] = "接口请求失败: {}".format(str(e))
            mlog.exception("批量导出已通报事件附件：{}".format(e))
            return JsonResponse(result, status=200)


    # >>
    # @list_route(methods=["POST"])
    # def batch_download_files(self, request):
    #     """
    #     批量导出已通报事件附件
    #     """
    #     mlog.info("批量导出已通报事件附件")
    #     result = {
    #         "code": 500,
    #         "msg": "error"
    #     }
    #     try:
    #         #>> data = json.loads(request.body)
    #         # event_ids = data.get("event_ids", [])
    #         # mlog.info("需要导出的事件ID列表：{}".format(event_ids))
    #         #
    #         # if not event_ids:
    #         #     result["msg"] = "事件ID列表不能为空"
    #         #     return JsonResponse(result, status=200)
    #
    #         data = json.loads(request.body)
    #         exception_type = data.get("exception_type", "")
    #         start_time = data.get("start_time", "")
    #         end_time = data.get("end_time", "")
    #         event_source = data.get("event_source", "")
    #         src_unit = data.get("src_unit", "")
    #         src_ip = data.get("src_ip", "")
    #         dst_ip = data.get("dst_ip", "")
    #
    #         id_list = data.get("event_ids", [])
    #         mlog.info("导出已通报事件报告参数：{}".format(json.dumps(data, ensure_ascii=False)))
    #
    #         # 构建查询条件
    #         params = Q()
    #         if id_list:
    #             params &= Q(id__in=id_list)
    #         else:
    #             if exception_type:
    #                 params &= Q(exception_type=exception_type)
    #             if start_time and end_time:
    #                 params &= Q(event_time__range=[
    #                     format3timestamp(start_time),
    #                     format3timestamp(end_time) + 86400 - 1
    #                 ])
    #             if event_source:
    #                 params &= Q(event_source__contains=event_source)
    #             if src_unit:
    #                 params &= Q(src_unit__contains=src_unit)
    #             if src_ip:
    #                 params &= Q(src_ip__contains=src_ip)
    #             if dst_ip:
    #                 params &= Q(dst_ip__contains=dst_ip)
    #
    #         # 批量查询
    #         event_list = ReportedEventsModel.objects.filter(params)
    #         mlog.info("查询到的事件数量：{}".format(event_list.count()))
    #
    #         # 创建一个内存中的zip文件
    #         zip_buffer = io.BytesIO()
    #         file_count = 0  # 记录成功添加的文件数
    #
    #         with ZipFile(zip_buffer, 'w') as zip_file:
    #             for instance in event_list:
    #                 mlog.info("处理事件ID：{}，文件列表：{}".format(instance.id, instance.files))
    #                 if instance.files:
    #                     file_name_list = instance.files.split(",")
    #                     for file_name in file_name_list:
    #                         # 构建文件路径
    #                         file_name_hashed = "{}.{}".format(
    #                             hashlib.md5(file_name.encode('utf-8')).hexdigest(),
    #                             hashlib.md5(str(instance.id).encode('utf-8')).hexdigest()
    #                         )
    #                         file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name_hashed)
    #                         mlog.info("处理文件：{}, 哈希后的文件名：{}, 完整路径：{}".format(
    #                             file_name, file_name_hashed, file_path))
    #
    #                         if os.path.exists(file_path):
    #                             try:
    #                                 # 使用二进制模式读取文件
    #                                 with open(file_path, 'rb') as f:
    #                                     file_content = f.read()
    #                                     mlog.info("读取文件成功，文件大小：{} 字节".format(len(file_content)))
    #                                     zip_file.writestr(file_name, file_content)
    #                                     file_count += 1
    #                             except Exception as e:
    #                                 mlog.error("读取文件失败：{}，错误：{}".format(file_path, str(e)))
    #                         else:
    #                             mlog.error("文件不存在：{}".format(file_path))
    #
    #         zip_content = zip_buffer.getvalue()
    #         mlog.info("ZIP文件生成完成，成功添加文件数：{}，ZIP文件大小：{} 字节".format(
    #             file_count, len(zip_content)))
    #
    #         if file_count == 0:
    #             result["msg"] = "未找到任何可下载的文件"
    #             return JsonResponse(result, status=200)
    #
    #         # 设置响应头，以便浏览器将其识别为ZIP文件下载
    #         response = HttpResponse(
    #             zip_content,
    #             content_type='application/zip'
    #         )
    #         response['Content-Disposition'] = 'attachment; filename={}'.format(
    #             quote("已通报事件附件.zip".encode('utf-8'))
    #         )
    #         auditLog(request, "批量导出已通报事件成功")
    #         return response
    #
    #     except Exception as e:
    #         result["msg"] = "接口请求失败: {}".format(str(e))
    #         mlog.exception("批量导出已通报事件附件：{}".format(e))
    #         return JsonResponse(result, status=200)

    def _process_location(self, src_region, src_unit):
        """
        处理地理位置和单位名称

        Args:
            src_region (str): 地理位置，格式如"中国 河南省 南阳市"、"中国 北京市"
            src_unit (str): 单位名称

        Returns:
            str: 处理后的单位名称，格式如"河南省xx单位"、"北京市xx单位"
        """
        if not src_region or not src_unit:
            return src_unit

        try:
            if src_unit.endswith("大学"):
                return src_unit
            # 分割地理位置
            locations = src_region.strip().split()
            if len(locations) < 2:
                return src_unit

            # 获取省份信息（第二个元素）
            province = locations[1]

            # 处理直辖市
            municipalities = ['北京市', '上海市', '天津市', '重庆市']

            if src_unit.startswith(province):
                return src_unit
            # 分支语句下内容一样，暂时去掉，保留一个
            elif province in municipalities:
                # 直辖市不需要重复"市"字
                result = "{0}{1}".format(province, src_unit)
            else:
                # 非直辖市，如果单位名已包含省份则不重复添加
                result = "{0}{1}".format(province, src_unit)
            return result.encode('utf-8')

        except Exception as e:
            mlog.error("处理地理位置信息失败：{0}".format(e))
            return src_unit

    @list_route(methods=["GET"])
    def reported_get_continuous_event_detail(self, request):
        """
        获取持续性事件详情用于回显
        """
        mlog.info("获取持续性事件详情用于回显")
        result = {
            "code": 500,
            "msg": "error",
            "data": {}
        }
        try:
            event_id = request.GET.get("event_id")
            if not event_id:
                result["msg"] = "事件ID不能为空"
                return JsonResponse(result, status=200)

            event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
            if not event_obj:
                result["msg"] = "未找到对应的持续性事件"
                return JsonResponse(result, status=200)

            # 获取事件来源
            event_type = event_obj.event_type
            event_type_menu = CommonConf().get_continuous_event_type_reverse()
            event_source = event_type_menu.get(str(event_type), str(event_type))
            if event_obj.analysis_tech == 1:
                event_source = event_source + "/5min"
            else:
                event_source = event_source + "/天"

            # 获取单位信息并处理地理位置
            src_unit = ContinuousEventsTagModel.objects.filter(
                event_id=event_id,
                tag_type=1,
                tag_name="unit"
            ).first()
            src_unit = src_unit.tag_content if src_unit else event_obj.src_com

            # 处理地理位置和单位名称
            processed_src_unit = self._process_location(event_obj.src_region, src_unit)

            # 流量转换为合适的单位显示
            up_bytes_str = self._long2unit(event_obj.up_bytesall)
            up_bytes_unit = up_bytes_str.split('/')[1]
            up_bytes = float(up_bytes_str.split('/')[0])

            #>> 服务类型，需要查询事件标签表中服务端的service字段
            # 根据事件类型判断服务端是境内还是境外，event_type是奇数，服务端为境外，event_type是偶数，服务端为境内
            if event_type % 2 == 0:
                tag_type = 1
            else:
                tag_type = 2

            service_ = ContinuousEventsTagModel.objects.filter(
                event_id=event_id,
                tag_type=tag_type,
                tag_name="service"
            ).first()
            service_type = service_.tag_content if service_ else ""

            # 构建返回数据
            result["data"] = {
                "event_id": event_id,
                "event_source": event_source,
                "src_unit": processed_src_unit,
                "up_bytes": up_bytes,
                "up_bytes_unit": up_bytes_unit,
                "notes": event_obj.judge_info,
                "files": event_obj.judge_file.split(",") if event_obj.judge_file else [],
                "src_ip": event_obj.src_ip,
                "src_port": event_obj.src_port.strip(","),
                "dst_ip": event_obj.dst_ip,
                "dst_port": event_obj.dst_port.strip(","),
                "service_type": service_type
            }

            result["code"] = 200
            result["msg"] = "成功"

        except Exception as e:
            result["msg"] = "获取持续性事件详情失败"
            mlog.exception("获取持续性事件详情失败：{}".format(e))
        return JsonResponse(result, status=200)

    def _handle_file_list(self, file_list, event_id, reported_event_id):
        """处理持续性事件原有附件的复制"""
        for file_name in file_list:
            src_file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(), hashlib.md5(str(event_id)).hexdigest())
            src_path = os.path.join(APPENDIX_LOCAL_PATH, src_file_name)
            dst_file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(), hashlib.md5(str(reported_event_id)).hexdigest())
            dst_path = os.path.join(REPORTED_EVENTS_FILES_PATH, dst_file_name)
            if not os.path.exists(REPORTED_EVENTS_FILES_PATH):
                os.makedirs(REPORTED_EVENTS_FILES_PATH)
            if os.path.exists(src_path):
                shutil.copy(src_path, dst_path)
            else:
                mlog.error("持续性事件文件不存在：{}".format(src_path))

    def _handle_new_files(self, files, event_obj):
        """处理新增附件，保存到持续性事件目录并去重"""
        judge_file = event_obj.judge_file.split(",") if event_obj.judge_file else []
        new_file_name_list = []
        for file in files:
            if file.name in judge_file:
                continue
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(), hashlib.md5(str(event_obj.event_id)).hexdigest())
            file_path = os.path.join(APPENDIX_LOCAL_PATH, file_name)
            mlog.info("持续性事件文件保存路径：{}".format(file_path))
            with open(file_path, 'wb') as f:
                for line in file.chunks():
                    f.write(line)
            new_file_name_list.append(file.name)
        event_obj.judge_file = ",".join(judge_file + new_file_name_list)
        return new_file_name_list

    def _validate_and_prepare(self, request):
        """参数校验与数据准备，返回 (result, data, file_list, files, file_name_list)"""
        result = {"code": 500, "msg": "error"}
        try:
            username = request.session['username']
            data = request.data
            file_list = request.POST.getlist("file_list")
            mlog.info("原有附件:{}".format(file_list))
            files = request.FILES.getlist("files")
            file_name_list = [file.name for file in files]
            mlog.info("新增附件:{}".format(file_name_list))
            if len(file_name_list) != len(set(file_name_list)):
                result["msg"] = "文件重复"
                return result, None, None, None, None
            event_time = data.get("event_time", datetime.datetime.now().strftime(DATE_FORMAT))
            exception_type = data.get("exception_type", "")
            create_dic = {
                "exception_type": exception_type,
                "src_unit": data.get("src_unit", ""),
                "service_type": data.get("service_type", ""),
                "attack_org": data.get("attack_org", ""),
                "up_bytes": self._convert_to_bytes(float(data.get("up_bytes", 0)), data.get("up_bytes_unit", "MB")),
                "notes": data.get("notes", ""),
                "event_source": data.get("event_source", ""),
                "event_time": format3timestamp(event_time),
                "feedback_situation": data.get("feedback_situation", ""),
                "judge_user": username,
                "src_ip": data.get("src_ip", "").strip(),
                "src_port": data.get("src_port", "").strip(),
                "dst_ip": data.get("dst_ip", "").strip(),
                "dst_port": data.get("dst_port", "").strip(),
            }
            mlog.info("create_dic:{}".format(json.dumps(create_dic, ensure_ascii=False, indent=4)))
            msg = ReportedEventsCheck().check(
                exception_type,
                create_dic["src_ip"],
                create_dic["src_port"],
                create_dic["dst_ip"],
                create_dic["dst_port"]
            )
            if msg:
                result["msg"] = msg
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return result, None, None, None, None
            params_error = ReportedEventsCheck().check_required_params(create_dic)
            if params_error:
                result["msg"] = params_error
                mlog.info(VERIFY_RES_CONSTANT.format(msg))
                return result, None, None, None, None
            event_id = data.get("event_id")
            if not event_id:
                result["msg"] = "事件ID不能为空"
                return result, None, None, None, None
            return None, create_dic, file_list, files, file_name_list
        except Exception as e:
            result["msg"] = "参数校验失败：{}".format(str(e))
            return result, None, None, None, None

    @list_route(methods=["POST"])
    def save_continuous_to_reported(self, request):
        """
        保存编辑后的持续性事件为已通报事件（重构版，降低嵌套复杂度）
        """
        mlog.info("保存编辑后的持续性事件为已通报事件")
        # 参数校验与准备
        result, create_dic, file_list, files, file_name_list = self._validate_and_prepare(request)
        if result:
            return JsonResponse(result, status=200)
        try:
            event_id = request.data.get("event_id")
            event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
            if not event_obj:
                return JsonResponse({"code": 500, "msg": "未找到对应的持续性事件"}, status=200)
            # 创建已通报事件
            reported_event = ReportedEventsModel.objects.create(**create_dic)
            # 附件处理
            try:
                if file_list:
                    self._handle_file_list(file_list, event_id, reported_event.id)
                if files:
                    self.save_file(files, reported_event.id)
                    self._handle_new_files(files, event_obj)
                # 更新已通报事件的文件列表
                reported_event.files = ",".join(file_name_list + file_list)
                reported_event.save()
            except Exception as e:
                mlog.exception("复制附件失败：{}".format(e))
                return JsonResponse({"code": 200, "msg": "创建成功，但附件复制失败"}, status=200)
            # 处理持续性事件批量逻辑
            data_dic = create_dic.copy()
            data_dic.update({"reported_event_id": reported_event.id, "files": file_name_list + file_list})
            self._process_continuous_event(data_dic, event_id)
            # 更新持续事件状态和备注
            event_obj.judge_status = 3
            event_obj.judge_info = create_dic.get("notes", "")
            event_obj.save()
            auditLog(request, "持续性事件:{} 保存为已通报事件成功".format(event_id))
            mlog.info("持续性事件:{} 保存为已通报事件成功".format(event_id))
            return JsonResponse({"code": 200, "msg": "成功"}, status=200)
        except Exception as e:
            mlog.exception("保存持续性事件为已通报事件失败：{}".format(e))
            return JsonResponse({"code": 500, "msg": "保存失败：{}".format(str(e))}, status=200)

    def _process_continuous_event(self, data_dict, old_event_id):
        """
        批量处理持续性事件 - 将同一境内IP和服务端口的最近1个月事件都标记为已通报
        """
        try:
            # 1. 获取基础参数
            exception_type = data_dict.get("exception_type")
            one_month_ago, now = get_months_ago(3)
            src_ip = data_dict.get("src_ip")
            dst_ip = data_dict.get("dst_ip")
            judge_status = 3
            judge_info = data_dict.get("notes", "")

            # 2. 构建端口查询条件
            src_port = data_dict.get("src_port", "").strip()
            dst_port = data_dict.get("dst_port", "").strip()

            src_port_conditions = Q()
            dst_port_conditions = Q()
            #>> src_port_q = []
            # dst_port_q = []
            if src_port:
                src_port_list = [",%s," % p.strip() for p in src_port.strip(",").split(",")]
                src_port_conditions = reduce(operator.or_, [Q(src_port__contains=p) for p in src_port_list])

            if dst_port:
                dst_port_list = [",%s," % p.strip() for p in dst_port.strip(",").split(",")]
                dst_port_conditions = reduce(operator.or_, [Q(dst_port__contains=p) for p in dst_port_list])

            if "APT" in exception_type:
                # 上传
                update_query = ContinuousEvents.objects.filter(
                    judge_status=1,
                    src_ip=src_ip,
                    dst_ip=dst_ip,
                    end_time__range=(one_month_ago, now)
                ).extra(where=[
                    "event_type %% 2 = 1",  # event_type 为奇数时查询 dst_port
                ]).filter(dst_port_conditions).exclude(event_id=old_event_id)
                # 下载
                download_query = ContinuousEvents.objects.filter(
                    judge_status=1,
                    src_ip=src_ip,
                    dst_ip=dst_ip,
                    end_time__range=(one_month_ago, now)
                ).extra(where=["event_type %% 2 = 0"]).filter(src_port_conditions).exclude(event_id=old_event_id)
                # mlog.info("query:{}".format(update_query.query))

                events_obj_list = update_query | download_query
            elif UNCOMMON_FOREIGN_PORT_CONSTANT in exception_type:
                events_obj_list = ContinuousEvents.objects.filter(
                    judge_status=1,
                    src_ip=src_ip,
                    dst_ip=dst_ip,
                    end_time__range=(one_month_ago, now)
                ).filter(dst_port_conditions).exclude(event_id=old_event_id)
            else:
                # 上传
                update_query = ContinuousEvents.objects.filter(
                    judge_status=1,
                    dst_ip=dst_ip,
                    end_time__range=(one_month_ago, now)
                ).extra(where=[
                    "event_type %% 2 = 1",  # event_type 为奇数时查询 dst_port
                ]).filter(dst_port_conditions).exclude(event_id=old_event_id)
                # 下载
                download_query = ContinuousEvents.objects.filter(
                    judge_status=1,
                    src_ip=src_ip,
                    end_time__range=(one_month_ago, now)
                ).extra(where=["event_type %% 2 = 0"]).filter(src_port_conditions).exclude(event_id=old_event_id)

                events_obj_list = update_query | download_query
            mlog.info("批量处理持续性事件 - 获取到的事件：{}".format(len(events_obj_list)))
            for event in events_obj_list:
                event.judge_status = judge_status
                # 备注去重处理
                existing_notes = {note for note in event.judge_info.split("\n") if note}
                new_notes = {note for note in judge_info.split("\n") if note}
                event.judge_info = "\n".join(sorted(existing_notes | new_notes))
                # 更新事件的文件列表,去重
                files_set = set((data_dict.get("files", []) +
                                 (event.judge_file.split(",") if event.judge_file else [])))
                files_set.discard('')  # 移除空字符串
                event.judge_file = ",".join(files_set)

            # 7. 批量保存所有更新
            events_to_save = events_obj_list
            continuous_event_ids = [event.event_id for event in events_to_save]

            if events_to_save:
                # 批量更新事件
                with transaction.atomic():
                    for event in events_to_save:
                        event.save()  # Django 1.11 bulk_update 可能不完全支持，使用逐个保存

                    # 将已通报事件的文件复制到持续性事件的附件目录
                    self._process_reported_file_to_continuous(
                        data_dict.get("files", []),
                        data_dict.get("reported_event_id"),
                        continuous_event_ids
                    )

            mlog.info("一月持续性事件批量处理完成")

        except Exception as e:
            error_msg = "处理持续性事件失败: %s" % str(e)
            mlog.exception(error_msg)

    def _process_reported_file_to_continuous(self, file_list, reported_event_id, continuous_event_ids):
        """
        将已通报事件的文件复制到持续性事件的附件目录
        """
        try:
            # 获取源事件的文件列表
            source_files = file_list
            if source_files:
                # 获取目标事件当前的文件列表
                new_files = []

                # 遍历源文件进行同步
                for file_name in source_files:
                    try:
                        # 构建源文件路径（从已通报事件目录）
                        src_file_name = "{}.{}".format(
                            hashlib.md5(file_name).hexdigest(),
                            hashlib.md5(str(reported_event_id)).hexdigest()
                        )
                        src_path = os.path.join(REPORTED_EVENTS_FILES_PATH, src_file_name)
                        if os.path.exists(src_path):
                            for event_id in continuous_event_ids:
                                # 构建目标文件路径（到持续性事件目录）
                                dst_file_name = "{}.{}".format(
                                    hashlib.md5(file_name).hexdigest(),
                                    hashlib.md5(str(event_id)).hexdigest()
                                )
                                dst_path = os.path.join(APPENDIX_LOCAL_PATH, dst_file_name)
                                if os.path.exists(dst_path):
                                    continue
                                else:
                                    shutil.copy(src_path, dst_path)
                                    new_files.append(file_name)
                                    mlog.info("文件同步成功: %s -> %s", src_path, dst_path)

                    except Exception as e:
                        mlog.error("文件 %s 同步失败: %s", file_name, str(e))
                        continue

        except Exception as e:
            mlog.error("文件同步过程出错: %s", str(e))
            # 文件同步失败不影响主流程，继续执行

    @list_route(methods=["GET"])
    def reported_redirect_event(self, request):
        """通过已通报事件的id，获取相关类型的规则下，事件的查询条件"""
        reported_id = request.GET.get("event_id")
        try:
            reported_event = ReportedEventsModel.objects.get(id=reported_id)
            rule = ExceptionTypeRule(reported_event).rule
            mlog.info("获取已通报id:{},跳转持续性事件条件: {}".format(reported_id, rule))
            return JsonResponse({"code": 200, "msg": "成功", "data": rule}, status=200)
        except Exception as e:
            mlog.exception("获取已通报事件信息失败: {}".format(e))
            return JsonResponse({"code": 400, "msg": "获取已通报事件信息失败"}, status=200)

    def verify_reported_to_white_params(self, data, src_ip, dst_ip):
        status = 0
        response = None
        filter_rule = data.get("filter_rule")
        dst_port = data['dst_port']
        src_port = data['src_port']
        src_com = data.get("src_com")
        exception_type = data.get("exception_type")
        if filter_rule == 1:
            if not src_ip and not dst_ip:
                return 1, JsonResponse({"msg": "境内IP和境外IP不能为空！", "code": 400})
        elif filter_rule == 2 and not src_com:
            return 1, JsonResponse({"msg": "备案单位不能为空！", "code": 400})
        elif filter_rule == 3:
            if not all([src_ip, src_port]) and not all([dst_ip, dst_port]):
                return 1, JsonResponse({"msg": "境内外IP、端口必填一组！", "code": 400})
            if src_ip and src_port and WhitelistEventsModel.objects.filter(
                    src_ip__overlap=src_ip,
                    src_port=src_port,
                    filter_rule=filter_rule,
                    exception_type=exception_type).exists():
                return 1, JsonResponse({"msg": "已存在该IP+端口的白名单事件！", "code": 400})
            if dst_ip and dst_port and WhitelistEventsModel.objects.filter(
                    dst_ip__overlap=dst_ip,
                    dst_port=dst_port,
                    filter_rule=filter_rule,
                    exception_type=exception_type).exists():
                return 1, JsonResponse({"msg": "已存在该IP+端口的白名单事件！", "code": 400})
        # 检查唯一性， 增加按照过滤规则校验
        if src_com and filter_rule == 2 and WhitelistEventsModel.objects.filter(src_com=src_com,
                                                                                filter_rule=filter_rule).exists():
            return 1, JsonResponse({"msg": "已存在该备案单位白名单事件！", "code": 400})
        if all([src_ip, dst_ip]) and filter_rule == 1 and WhitelistEventsModel.objects.filter(
                Q(src_ip__overlap=src_ip),
                Q(dst_ip__overlap=dst_ip),
                filter_rule=filter_rule,
                exception_type=exception_type
        ).exists():
            # 白名单功能，除了IP对之外【IP对过滤规则是1】，其他的过滤规则都不加异常类型的限制，自动研判也一样。
            return 1, JsonResponse({"msg": "该异常类型，已存在该源目IP白名单事件！", "code": 400})
        return status, response

    def _generate_param_dicts(self, param_data, src_ports_list, dst_ports_list):
        """
        生成参数字典列表
        """
        param_dicts = []
        for s_port in src_ports_list:
            for d_port in dst_ports_list:
                # 创建新字典，复制原始数据
                new_data = param_data.copy()
                new_data['dst_port'] = d_port if d_port else None
                new_data['src_port'] = s_port if s_port else None
                param_dicts.append(new_data)
        return param_dicts

    def _validate_param_dicts(self, param_dicts, original_data):
        """
        验证参数字典列表
        """
        for param_dict in param_dicts:
            # 根据过滤规则判断参数是否传入（使用第一个字典进行验证）
            status, response = self.verify_reported_to_white_params(original_data, param_dict["src_ip"], param_dict["dst_ip"])
            if status:
                mlog.error("参数验证失败: {}".format(response))
                return response
        return None

    @list_route(methods=['POST'])
    def reported_to_white_event(self, request):
        """
        将已通报事件转为白名单事件
        """
        try:
            mlog.info("转为白名单事件")
            username = request.session['username']
            data = request.data
            up_bytes = data.get("up_bytes", 0)
            up_bytes_unit = data.pop("up_bytes_unit", "MB")
            if up_bytes != "" and up_bytes is not None:
                up_bytes = int(self._convert_to_bytes(float(up_bytes), up_bytes_unit))
            else:
                up_bytes = 0

            param_data = {
                "filter_rule": data.get("filter_rule"),
                "src_ip": data.get("src_ip").split(",") if data.get("src_ip") else [],
                "dst_ip": data.get("dst_ip").split(",") if data.get("dst_ip") else [],
                "create_time": int(time.time()),
                "update_time": int(time.time()),
                "judge_user": username,
                "src_com": data.get("src_com"),
                "exception_type": data.get("exception_type"),
                "event_time": format3timestamp(data["event_time"]),
                "up_bytes": up_bytes,
                "cause": data.get("cause"),
                "event_source": data.get("event_source", ""),
                "notes": data.get("notes", ""),
                "service_type": data.get("service_type", ""),
                "src_unit": data.get("src_unit", ""),
            }
            src_port = data.get("src_port", "")
            dst_port = data.get("dst_port", "")

            src_ports_list = [p.strip() for p in src_port.split(',')] if src_port else [""]
            dst_ports_list = [p.strip() for p in dst_port.split(',')] if dst_port else [""]
            # 创建包含所有参数组合的字典列表
            param_dicts = self._generate_param_dicts(param_data, src_ports_list, dst_ports_list)

            # 验证参数
            validation_result = self._validate_param_dicts(param_dicts, data)
            if validation_result:
                return validation_result

            if not param_dicts:
                return JsonResponse({"msg": "参数验证失败", "code": 400})
            for param_dict in param_dicts:
                # 创建白名单事件对象
                mlog.info("创建白名单事件对象:{}".format(json.dumps(param_dict, ensure_ascii=False)))
                _serializer = WhitelistEventsSerializer(data=param_dict)
                if _serializer.is_valid():
                    _serializer.save()
            
            mlog.info("转为白名单事件成功")
            # 通过已通报事件异常类型规则，将相关持续性事件转为“未研判”
            self.judge_cont_event_to_1_by_exception_type(param_dicts)
            # 开始通过规则研判事件为“已研判,未通报”
            self.judge_reported_to_white_event(param_dicts)
            # 删除已通报事件失败
            self._delete_reported_events([data.get("event_id")])
            return JsonResponse({"data": "", "msg": "转为白名单事件成功", "code": 200})

        except Exception as e:
            mlog.exception("转为白名单事件失败: {}".format(e))
            return JsonResponse({"msg": "失败", "code": 500})

class ExceptionTypeRule(object):
    """异常类型规则"""
    def __init__(self, reported_event_obj):
        self.reported_event_obj = reported_event_obj
        self.start_time, self.end_time = self.get_time_range()
        self.data = {
            "start_time": self.start_time,
            "end_time": self.end_time,
        }
        self.rule = self.get_rule()
    
    def get_time_range(self):
        """获取时间范围"""
        event_time = self.reported_event_obj.event_time
        timedelta = 60*60*24*30
        start_time = event_time - timedelta
        end_time = event_time + timedelta
        return timestamp2format(start_time), timestamp2format(end_time)
    
    def get_rule(self):
        exception_type = self.reported_event_obj.exception_type
        if "APT" in exception_type:
            return self.apt_event_rule()
        elif UNCOMMON_FOREIGN_PORT_CONSTANT in exception_type:
            return self.uncommon_foreign_port_event_rule()
        else:
            return self.normal_event_rule()
    
    def up_or_down_port(self):
        if "上传" in self.reported_event_obj.event_source:
            return "上传", self.reported_event_obj.dst_port
        elif "下载" in self.reported_event_obj.event_source:
            return "下载",self.reported_event_obj.src_port
        else:
            return "未知",""
    
    def apt_event_rule(self):
        """APT攻击事件规则"""
        event_source, port = self.up_or_down_port()

        self.data.update({
            "sip": self.reported_event_obj.src_ip,
            "dip": self.reported_event_obj.dst_ip
        })
        if event_source == "上传":
            self.data["dst_port"] = port
        else:
            self.data["src_port"] = port
        return self.data
    
    def uncommon_foreign_port_event_rule(self):
        """非常用端口（境外）事件规则"""
        self.data.update({
            "sip": self.reported_event_obj.src_ip,
            "dip": self.reported_event_obj.dst_ip,
            "dst_port": self.reported_event_obj.dst_port
        })
        return self.data
    
    def normal_event_rule(self):
        """正常事件规则"""
        event_source, port = self.up_or_down_port()
        if event_source == "上传":
            self.data["dip"] = self.reported_event_obj.dst_ip
            self.data["dst_port"] = port
        else:
            self.data["sip"] = self.reported_event_obj.src_ip
            self.data["src_port"] = port
        return self.data



# 目录路径
EVENT_RECOMMEND_DIR = os.path.join(PATH_APP, "script/event_recommend/output")
# 正则匹配文件名中的时间戳部分
pattern = re.compile(r"event_(\d{8}_\d{6})\.(csv|xlsx)")


def get_latest_csv():
    mlog.info("获取最新事件推荐文件:{}".format(EVENT_RECOMMEND_DIR))
    files = glob.glob(os.path.join(EVENT_RECOMMEND_DIR, "event_*.csv"))  # 获取所有匹配的 CSV 文件

    xlsx_files = glob.glob(os.path.join(EVENT_RECOMMEND_DIR, "event_*.xlsx"))  # 匹配所有的 .xlsx 文件
    files.extend(xlsx_files)
    valid_files = []
    mlog.info("文件列表：{}".format(files))
    for file_path in files:
        filename = os.path.basename(file_path)  # 仅获取文件名
        match = pattern.match(filename)  # 匹配时间戳
        if match:
            timestamp_str = match.group(1)  # 提取时间戳部分，如 "20250401_091724"
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")  # 转换为 datetime
            valid_files.append((timestamp, file_path))  # 存入列表
    if not valid_files:
        return None

    # 根据时间戳排序，取最新的文件
    latest_file = max(valid_files, key=lambda x: x[0])[1]
    mlog.info("最新文件路径：{}".format(latest_file))
    return latest_file


@list_route(methods=["GET"])
def download_event_recommend_file(request):
    try:
        latest_file = get_latest_csv()
        if latest_file:
            # 读取文件
            file_handle = open(latest_file, 'rb')
            file_name = os.path.basename(latest_file)
            if file_name.endswith(".xlsx"):
                content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            else:
                content_type = "text/csv"
            response = HttpResponse(file_handle, content_type=content_type)

            # 设置响应头，确保文件作为附件下载
            response['Content-Disposition'] = ATTACHMENT_FILENAME_CONSTANT.format(file_name)
            return response
        else:
            return JsonResponse({"msg": "No CSV files found", "code": 400})
    except Exception as e:
        mlog.exception("下载事件推荐文件失败：{}".format(e))
        return JsonResponse({"msg": "Failed to download CSV file", "code": 400})
