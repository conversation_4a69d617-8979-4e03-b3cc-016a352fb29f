#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: batch_query_of_registered_units.py
# @time: 2024/12/19 12:21
# @desc:
"""
批量查询备案单位
"""
import argparse
import time
import traceback

import requests
from cncert_kj.lib import openpyxl
from time import sleep

from cncert_kj.utils import logger
from cncert_kj.utils.visit_record_unit import VisitAPI

mlog = logger.init_logger('batch_query_of_registered_units')
v_obj = VisitAPI()


class ExportUnits(object):
    def __init__(self, input_file, output_file):
        self.input_file = input_file
        self.output_file = output_file

    def get_recorded_unit(self, ip):
        """通过API查询备案单位"""
        try:
            params_ = {
                "ipLocation": ip,
                "token": v_obj.token
            }
            response = requests.get(v_obj.api, params=params_)
            response.raise_for_status()  # 确保请求成功
            data = response.json()
            mlog.info("备案单位结果-{}".format(data))
            user_ = ""
            if response.status_code == 200:
                user_ = data.get("User").strip()
            return user_
        except Exception:
            mlog.error("获取备案单位失败：{}-----error:{}".format(ip, traceback.format_exc()))
            return ""

    def batch_query_unit(self):
        # 打开输入的Excel文件并读取
        wb = openpyxl.load_workbook(self.input_file)
        sheet = wb.active
        sheet.freeze_panes = 'A2'  # 冻结第一行
        sheet.auto_filter.ref = 'A1:AZ1'  # 增加筛选

        # 假设输入文件第一列是IP列，读取所有的IP地址
        ip_addresses = []
        for row in sheet.iter_rows(min_row=2, max_col=1, values_only=True):  # 跳过表头
            ip_addresses.append(row[0])

        # 查询每个IP的备案单位
        results = []
        for ip in ip_addresses:
            print(mlog.info("正在查询 {ip} ...".format(ip=ip)))
            recorded_unit = self.get_recorded_unit(ip)
            results.append({'ip': ip, '备案单位': recorded_unit})
            # sleep(1)  # 防止过于频繁的请求，适当延迟

        # 创建一个新的Excel文件
        output_wb = openpyxl.Workbook()
        output_sheet = output_wb.active
        output_sheet.append(['ip', '备案单位'])  # 添加表头

        # 将查询结果写入输出文件
        for result in results:
            output_sheet.append([result['ip'], result['备案单位']])
        # 保存结果到输出文件
        output_wb.save(self.output_file)
        print(mlog.info("查询完成，结果已保存至 {output_file}".format(output_file=self.output_file)))


if __name__ == "__main__":
    s = time.time()
    parser = argparse.ArgumentParser(usage="python batch_query_of_registered_units.py",
                                     description="")
    parser.add_argument("--input_file", help="输入的IP表格路径", default="input_ips.xlsx")
    parser.add_argument("--output_file", help="输出的文件路径", default="output_with_recorded_units.xlsx")
    args = parser.parse_args()
    if not all([args.input_file, args.output_file]):
        mlog.info("参数错误")
    else:
        obj = ExportUnits(args.input_file, args.output_file)
        obj.batch_query_unit()
    mlog.info("脚本执行结束，共耗时：{}s".format(time.time() - s))
