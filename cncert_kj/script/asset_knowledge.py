#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    资产知识库，txt格式，五天更新一次，推送到hdfs上
"""
import argparse
import csv
import datetime
import json
import math
import os
import re
import subprocess
import time
import threading
import django

from cncert_kj.models.top_1w_models import stop_task, start_task
# 设置Django环境
from cncert_kj.utils.time_trans import DATE_FORMAT

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from collections import defaultdict, OrderedDict
from django.db.models import Q
from django.db import transaction

from cncert_kj.models.base_model import AssetTypeModel, AssetInfoModel
from cncert_kj.utils.conf_util import CommonConf
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_asset import RequestAsset
from cncert_kj.utils import logger

mlog = logger.init_logger("asset_knowledge")

CONSTANT_CMS = "内容管理系统"
CONSTANT_1 = "{}/{}"


class AssetKnowledgeUtil(object):
    def __init__(self):
        # 使用方法获取本地ip，判断ip是否是已"10.5.223."开头，如果是，则选择开发环境，否则选择线上环境
        self.class_conf()

    def class_conf(self):
        self.local_path = CommonConf().common_conf["asset_knowledge"]["local_path"]
        if CommonConf().is_dev():  # 开发环境-hdfs路径
            mlog.info("**开发环境**")
            self.HDFS_CMD = os.environ["HADOOP_HOME"] + "/bin/hadoop fs"  # 开发环境-hdfs路径
            self.hdfs_path = "hdfs://*************:8020/tmp/asset_knowledge"
        else:  # 线上-hdfs路径
            mlog.info("**线上环境**")
            self.HDFS_CMD = CommonConf().common_conf["HDFS_CMD"]  # 线上-hdfs路径
            self.hdfs_path = CommonConf().common_conf["asset_knowledge"]["hdfs_path"]

    def to_txt(self, file_name, data_list):
        local_file_path = CONSTANT_1.format(self.local_path, file_name)
        if not os.path.exists(self.local_path):
            os.makedirs(self.local_path)
        mlog.info("开始存入本地:{}".format(local_file_path))
        # 打开一个文件进行写入
        with open(local_file_path, 'w') as f:
            # 遍历列表
            for item in data_list:
                # 将每个元素写入文件，并添加换行符
                line_ = " ".join(item[:4])
                f.write(line_ + '\n')

    def process_row(self, row, asset_type_obj):
        """处理单行数据
        Args:
            row: CSV的一行数据，格式为[ip, port, date, device_type, vendor, model]
        Returns:
            dict: 处理后的资产信息字典
        """
        ip, port, _, value_1, value_2, value_3 = row
        product = '-'.join([value_1, value_2, value_3]).strip("-")

        # 构建资产信息
        return AssetInfoModel(
            asset_ip=ip,
            asset_port=port,
            protocol='tcp',
            product=product,
            asset_type=asset_type_obj.id,
            country='中国',
            asset_source=2
        )

    def deduplicate_assets(self, asset_list):
        """资产去重"""
        if not asset_list:
            return []
        # 获取除当前类型外的所有资产缓存
        other_types_cache = cache_obj._get_other_types_asset_cache(8)

        # 直接使用集合操作进行过滤，只与其他类型对比
        filtered_assets = []
        for asset in asset_list:
            asset_key = (asset.asset_ip, asset.asset_port)
            if asset_key not in other_types_cache:  # 只检查是否在其他类型中存在
                filtered_assets.append(asset)

        mlog.info("资产去重（排除其他类型）：原始{}条，过滤后{}条".format(
            len(asset_list), len(filtered_assets)))
        cache_obj._update_cache_after_save(8, [(asset.asset_ip, asset.asset_port) for asset in filtered_assets])
        return filtered_assets

        #>> # 批量构建查询条件，避免过多的OR查询
        # ip_port_pairs = [(asset.asset_ip, asset.asset_port) for asset in asset_list]
        #
        # # 分批查询，避免单次查询条件过多
        # batch_size = 1000
        # existing_pairs = set()
        #
        # for i in range(0, len(ip_port_pairs), batch_size):
        #     batch_pairs = ip_port_pairs[i:i + batch_size]
        #
        #     # 构建当前批次的查询条件
        #     q_batch = Q()
        #     for ip, port in batch_pairs:
        #         q_batch |= Q(asset_ip=ip, asset_port=port)
        #
        #     # 查询当前批次
        #     batch_existing = AssetInfoModel.objects.exclude(asset_type=8).filter(q_batch).values_list(
        #         'asset_ip', 'asset_port'
        #     )
        #
        #     # 添加到已存在集合
        #     existing_pairs.update((ip, str(port)) for ip, port in batch_existing)
        #
        # # 过滤掉数据库中已存在的记录
        # return [
        #     asset for asset in asset_list
        #     if (asset.asset_ip, asset.asset_port) not in existing_pairs
        # ]

    def read_csv_to_table(self, file_name, asset_type_obj):
        """从csv文件中读取数据并导入数据库, 针对物联网事件，csv文件路径为：/home/<USER>/ISOP/asset_knowledge/iot_event.csv"""
        try:
            csv_file_path = self.local_path + "/" + file_name
            mlog.info(u"开始读取csv文件: {}".format(csv_file_path))
            create_list = []
            # 使用集合存储已存在的(ip, port)对
            existing_pairs = set()
            success_count = 0
            error_count = 0
            with open(csv_file_path, 'rb') as f:
                reader = csv.reader(f)
                for row in reader:
                    try:
                        if len(row) != 6:
                            # mlog.warning(u"跳过无效行: {}".format(row))
                            error_count += 1
                            continue

                        asset_info = self.process_row(row, asset_type_obj)
                        # 构建唯一键元组
                        unique_key = (asset_info.asset_ip, str(asset_info.asset_port))

                        # 如果已存在相同的IP和端口组合，则跳过
                        if unique_key in existing_pairs:
                            continue

                        existing_pairs.add(unique_key)
                        create_list.append(asset_info)
                        success_count += 1

                    except Exception as e:
                        mlog.exception(u"处理行数据失败: {}, 错误: {}".format(row, e))
                        error_count += 1
                        continue
            mlog.info("读取完成")
            # 根据ip+port与数据库中已存在的资产去重
            create_list = self.deduplicate_assets(create_list)
            with transaction.atomic():
                mlog.info("开始删除旧数据")
                AssetInfoModel.objects.filter(asset_type=asset_type_obj.id).exclude(asset_source=1).delete()
                mlog.info(u"开始插入数据，共: {}条".format(len(create_list)))
                AssetInfoModel.objects.bulk_create(create_list, batch_size=100000)

            mlog.info(u"数据导入完成，成功: {}条，失败: {}条".format(success_count, error_count))

        except Exception as e:
            mlog.exception(u"数据导入失败: {}".format(e))
            return False

    def put_hdfs_file(self, file_name):
        """
        上传文件至hdfs
        /opt/apps/cdl-hadoop-3.3.1/bin/hdfs dfs -put -f
        """
        # 检查hdfs路径是否存在
        is_dir = "{} -test -d '{}'".format(self.HDFS_CMD, self.hdfs_path)
        if os.system(is_dir) != 0:
            # 如果路径不存在，则创建hdfs目录
            mkdir_cmd = "{} -mkdir -p '{}'".format(self.HDFS_CMD, self.hdfs_path)
            os.system(mkdir_cmd)

        # 构造hdfs文件路径
        hdfs_file_path = CONSTANT_1.format(self.hdfs_path, file_name)
        mlog.info("开始上传至HDFS:{}".format(hdfs_file_path))

        # >>>上传至hdfs
        # 构造hdfs上传命令
        cmd = "{} -put -f '{}/{}' '{}'".format(
            self.HDFS_CMD, self.local_path, file_name, hdfs_file_path
        )
        mlog.info("hdfs命令:{}".format(cmd))
        # 执行hdfs上传命令
        os.system(cmd)

    def get_hdfs_file_time(self, file_name):
        """获取hdfs文件时间"""
        hdfs_file_path = CONSTANT_1.format(self.hdfs_path, file_name)
        cmd = "{} -ls '{}'".format(self.HDFS_CMD, hdfs_file_path)
        mlog.info("查询hdfs命令：{}".format(cmd))
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output, _ = process.communicate()
        mlog.info("HDFS中文件时间：{}".format(output))
        return output

    def check_and_put(self, file_name):
        """检查hdfs文件时间是否变化"""
        try:
            # 检查本地文件是否存在
            local_file_path = CONSTANT_1.format(self.local_path, file_name)
            if not os.path.exists(local_file_path):
                mlog.info("本地文件不存在，推送失败：{}".format(local_file_path))
                return False
            # >>>检查hdfs路径中文件是否存在
            # is_file = "{} -test -f '{}/{}'".format(self.HDFS_CMD, self.hdfs_path, file_name)
            # if os.system(is_file) != 0:
            #     mlog.info("HDFS中文件不存在，推送失败")

            # 获取hdfs文件时间
            old_hdfs_file_time = self.get_hdfs_file_time(file_name)

            # 执行推送
            self.put_hdfs_file(file_name)

            # 获取推送后文件时间
            new_hdfs_file_time = self.get_hdfs_file_time(file_name)

            # 比较文件时间
            if old_hdfs_file_time != new_hdfs_file_time:
                mlog.info("HDFS中文件时间发生变化，推送成功")
                return True
            else:
                mlog.info("HDFS中文件时间未发生变化，推送失败")
                return False
        except Exception as e:
            mlog.exception(u"逻辑出错，推送失败: {}".format(e))
            return False

    def check_hdfs_list(self):
        cmd = "{} -ls '{}'".format(self.HDFS_CMD, self.hdfs_path)
        mlog.info("检测hdfs资产知识库推送目录:{}".format(cmd))
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output, _errors = process.communicate()

        mlog.info("Command output:{}".format(output))

    def restart_setl(self):
        """
            kill   GJK-TCLOG-SETL和GJK-NETFLOW-SETL 任务，并重启，
            执行 /opt/apps/gjk_data_proc/start_netflow_setl.sh  和  /opt/apps/gjk_data_proc/start_tclog_setl.sh
        """
        mlog.info("正在重启setl任务")
        task_name_list = ["GJK-TCLOG-SETL", "GJK-NETFLOW-SETL"]
        for task_name in task_name_list:
            if stop_task(task_name):
                mlog.info("{}任务已停止".format(task_name))
                # >>>启动任务
                # mlog.info("启动任务：{}".format(task_name))
                # start_task(task_name)
                # mlog.info("重启完成：{}".format(task_name))
            else:
                mlog.info("{}任务重启失败".format(task_name))

class AssetKnowledgeCache(object):
    # 类级别的全局缓存，按资产类型分组存储
    _global_asset_cache_by_type = {}
    _cache_timestamp = None
    _cache_expire_seconds = 3600 * 10
    _cache_lock = threading.Lock()  # 线程锁保证缓存更新安全

    @classmethod
    def _build_global_asset_cache_by_type(cls):
        """按资产类型构建全局资产缓存"""
        current_time = time.time()

        with cls._cache_lock:
            # 检查缓存是否有效
            if (cls._global_asset_cache_by_type and
                    cls._cache_timestamp is not None and
                    current_time - cls._cache_timestamp < cls._cache_expire_seconds):
                return cls._global_asset_cache_by_type

            mlog.info("开始构建按类型分组的全局资产缓存...")
            start_time = datetime.datetime.now()

            # 按资产类型分组查询
            cls._global_asset_cache_by_type = {}

            # 分批查询，按asset_type分组
            batch_size = 50000
            offset = 0

            while True:
                batch_data = AssetInfoModel.objects.values_list(
                    'asset_ip', 'asset_port', 'asset_type'
                ).order_by('id')[offset:offset + batch_size]

                if not batch_data:
                    break

                # 按类型分组存储
                for ip, port, asset_type in batch_data:
                    if asset_type not in cls._global_asset_cache_by_type:
                        cls._global_asset_cache_by_type[asset_type] = set()
                    cls._global_asset_cache_by_type[asset_type].add((ip, str(port)))

                offset += batch_size

                # >>统计总数
                # total_count = sum(len(assets) for assets in cls._global_asset_cache_by_type.values())
                # mlog.info("已缓存 {} 条资产数据".format(total_count))

            cls._cache_timestamp = current_time

            end_time = datetime.datetime.now()
            total_count = sum(len(assets) for assets in cls._global_asset_cache_by_type.values())
            mlog.info("按类型分组的全局资产缓存构建完成，共{}条，耗时{}秒".format(
                total_count, (end_time - start_time).total_seconds()))

            return cls._global_asset_cache_by_type

    def _get_other_types_asset_cache(self, asset_type_id):
        """获取除当前资产类型外的所有资产缓存"""
        all_cache = self._build_global_asset_cache_by_type()

        # 合并除当前类型外的所有资产
        other_types_assets = set()
        for asset_type, assets in all_cache.items():
            if asset_type != asset_type_id:  # 排除当前资产类型
                other_types_assets.update(assets)

        mlog.info("当前资产类型ID: {}, 其他类型资产总数: {}".format(
            asset_type_id, len(other_types_assets)))

        return other_types_assets

    def _update_cache_after_save(self, asset_type_id, new_assets_list):
        """保存资产后更新缓存"""
        with self._cache_lock:
            if self._global_asset_cache_by_type is None:
                return

            # 清空当前类型的缓存
            if asset_type_id in self._global_asset_cache_by_type:
                old_count = len(self._global_asset_cache_by_type[asset_type_id])
                self._global_asset_cache_by_type[asset_type_id].clear()
                mlog.info("清空资产类型{}的旧缓存，共{}条".format(asset_type_id, old_count))
            else:
                self._global_asset_cache_by_type[asset_type_id] = set()

            # 添加新的资产到缓存
            for asset in new_assets_list:
                self._global_asset_cache_by_type[asset_type_id].add((asset[0], asset[1]))

            mlog.info("更新资产类型{}的缓存，新增{}条".format(
                asset_type_id, len(new_assets_list)))

    @classmethod
    def clear_global_cache(cls):
        """清空全局缓存，用于强制重新构建"""
        with cls._cache_lock:
            cls._global_asset_cache_by_type = {}
            cls._cache_timestamp = None
            mlog.info("全局资产缓存已清空")

class AssetKnowledge():
    """资产知识库处理类"""


    def __init__(self, after_time, obj_, is_all_region=True):
        # >>>self.asset_knowledge_conf = CommonConf().asset_knowledge_conf  # 获取资产配置
        self.request_asset = RequestAsset(retries=3, timeout=300)
        self.after_time = after_time
        self.before_time = datetime.datetime.now().strftime("%Y-%m-%d")
        # 资产大类对象
        self.obj_ = obj_
        self.category = obj_.asset_type
        self.asset_type_id = obj_.id
        self.asset_type_en = obj_.asset_type_en
        # 定义需要处理的字段映射
        self.field_mappings = {
            'protocol': self.obj_.protocol_conf,
            'product': self.obj_.product_conf,
            'category': self.obj_.category_conf
        }
        self.oa_category_list = ["CRM", "ERP", CONSTANT_CMS]

        self.ak = AssetKnowledgeUtil()
        self.local_path = self.ak.local_path

        self.is_all_region = is_all_region
        # 查询资产的通用条件
        self.general_condition = ['is_honeypot="false"',
                                  'after="{}"'.format(self.after_time),
                                  'before="{}"'.format(self.before_time)]
        # 如果不是全境查询，添加国内限制条件
        if not self.is_all_region:
            self.general_condition.append('country="CN"')

        self.create_dir()

    def create_dir(self):
        """创建本地目录"""
        if not os.path.exists(self.local_path):
            os.makedirs(self.local_path)

    def fofa_query(self):
        """生成独立的 protocol/product/category 查询语句"""
        query_groups = OrderedDict([("protocol", []), ("product", []), ("category", [])])

        # 分离三类查询条件,统一处理所有字段
        for field_name, field_value in self.field_mappings.items():
            if field_value:
                query_groups[field_name].extend(
                    '{0}="{1}"'.format(field_name, value.strip())
                    for value in field_value.split("||")
                    if value.strip()  # 过滤空值
                )

        general__q = " && ".join(self.general_condition)

        res_list = []
        # 分别处理每个类型的查询
        for condition_type, queries in query_groups.items():
            if not queries:
                continue
            # 处理category查询，直接拼接
            if condition_type == "category":
                for query in queries:
                    q = "{} && ({})".format(general__q, query)
                    res_list.append(q)
            else:
                # 拆分大查询组（FOFA 单次查询条件数限制）
                chunk_size = 1  # 根据FOFA接口实际限制调整
                for i in range(0, len(queries), chunk_size):
                    chunk = queries[i:i + chunk_size]
                    q_part = " || ".join(chunk)
                    q = "{} && ({})".format(general__q, q_part)
                    res_list.append(q)

        return res_list

    # def process_assets(self, assets_dict):
    #     """处理资产数据"""
    #     mlog.info("开始处理资产数据")
    #     assets_list = []
    #     OA_category_list = []
    #     # 处理不同类型的资产数据
    #     try:
    #         for query_type, assets in assets_dict.items():
    #             mlog.info("--{}开始处理".format(query_type))
    #
    #             query_type_value = self.field_mappings.get(query_type, "")
    #             if query_type_value:
    #                 query_type_list = query_type_value.split("||")
    #             else:
    #                 query_type_list = []
    #
    #             for asset in assets:
    #                 ip_ = asset.get("ip", "")
    #                 if not is_ipv4_correct(ip_):
    #                     continue
    #
    #                 port_ = asset.get("port", "")
    #                 protocol = asset.get("protocol", "")
    #                 product_list = asset.get("product", [])
    #                 if not product_list:
    #                     continue
    #                 country = asset.get("country", "")
    #
    #                 # 处理protocol
    #                 if all([protocol,
    #                         protocol != "null",
    #                         protocol != u"null",
    #                         protocol != "unknown",
    #                         protocol != u"unknown"]):
    #                     pass
    #                 else:
    #                     protocol = ""
    #
    #                 # 特殊处理
    #                 # if self.category == "物联网视频专题":
    #                 if self.asset_type_id == 7:  # 视频监控专题，原物联网视频专题
    #                     if "accvisio" in protocol.lower():
    #                         continue
    #                     accvisio = False
    #                     for p in product_list:
    #                         if "accvisio" in p.lower():
    #                             accvisio = True
    #                             break
    #                     if accvisio:
    #                         continue
    #
    #                 # 根据查询类型处理product
    #                 product = ""
    #                 if query_type == "product":
    #                     # 匹配查询条件中的product
    #                     for query_product in query_type_list:
    #                         if query_product in product_list:
    #                             if query_product == "PostgreSQL" and str(port_) == "54321":
    #                                 product = "人大金仓"
    #                             else:
    #                                 product = query_product
    #                             break
    #                         else:
    #                             continue
    #                 elif query_type == "protocol":
    #                     # 拼接所有product
    #                     if product_list:
    #                         product = "|||".join(filter(None, product_list))
    #
    #                 elif query_type in self.oa_category_list:
    #                     # >>>匹配查询条件中的category
    #                     # 取product列表第一个值
    #                     if query_type == "CRM":
    #                         prefix_ = "CRM-"
    #                     elif query_type == "ERP":
    #                         prefix_ = "ERP-"
    #                     elif query_type == "内容管理系统":
    #                         prefix_ = "CMS-"
    #                     else:
    #                         continue
    #                     if product_list:
    #                         product = prefix_ + product_list[0]
    #
    #                     # OA专题单独一个列表，需要拿着ip+端口去资产表中查询，若是存在，则在列表中剔除
    #                     OA_category_list.append((ip_, str(port_), protocol, product, country))
    #                     continue
    #                 else:
    #                     continue
    #                 line_ = (ip_, str(port_), protocol, product, country)
    #                 assets_list.append(line_)
    #
    #             mlog.info("--{}处理结束".format(query_type))
    #
    #     except Exception as e:
    #         mlog.exception("处理资产数据异常:{}".format(e))
    #
    #     if assets_list:
    #         assets_list = list(set(assets_list))
    #     # 处理OA专题
    #     if OA_category_list:
    #         # 对OA_category_list去重
    #         OA_category_list = list(set(OA_category_list))
    #         # 1. 将assets_list中的IP-端口对转换为集合，提高查找效率
    #         asset_ip_port_set = set((i[0], i[1]) for i in assets_list)
    #         # 2. 使用集合操作过滤OA类别列表
    #         OA_category_list = [
    #             oa_category for oa_category in OA_category_list
    #             if (oa_category[0], oa_category[1]) not in asset_ip_port_set
    #         ]
    #         # 2. 再检查数据库中是否存在
    #         if OA_category_list:
    #             # 构建精确的IP-端口对查询
    #             q_ = Q()
    #             for oa_category in OA_category_list:
    #                 q_ |= Q(asset_ip=oa_category[0], asset_port=oa_category[1])
    #
    #             # 查询数据库中已存在的记录
    #             existing_assets = AssetInfoModel.objects.filter(q_).values_list('asset_ip', 'asset_port')
    #
    #             # 转换为set以提高查找效率
    #             existing_pairs = set((ip, str(port)) for ip, port in existing_assets)
    #
    #             # 过滤掉数据库中已存在的记录
    #             OA_category_list = [
    #                 oa_category for oa_category in OA_category_list
    #                 if (oa_category[0], oa_category[1]) not in existing_pairs
    #             ]
    #
    #         # 将过滤后的OA类别数据添加到assets_list
    #         assets_list.extend(OA_category_list)
    #
    #     mlog.info("处理资产数据完成:{}条".format(len(assets_list)))
    #     return assets_list

    def process_assets(self, assets_dict):
        """处理资产数据"""
        mlog.info("开始处理资产数据")

        try:
            # 处理所有资产数据
            assets_list, category_assets_list = self._process_all_assets(assets_dict)

            # 处理专题数据
            final_assets_list = self._process_category_assets(assets_list, category_assets_list)

            mlog.info("处理资产数据完成:{}条".format(len(final_assets_list)))
            return final_assets_list

        except Exception as e:
            mlog.exception("处理资产数据异常:{}".format(e))
            return []

    def _process_all_assets(self, assets_dict):
        """处理所有资产数据，返回普通资产列表和OA资产列表"""
        assets_list = []
        category_assets_list = []

        # 获取所有在数据库中配置的category类型
        configured_categories = self._get_query_type_list('category')
        mlog.info("configured_categories:{}".format(json.dumps(configured_categories, ensure_ascii=False)))

        for query_type, assets in assets_dict.items():
            mlog.info("--开始处理:{}".format(query_type))

            # 检查当前处理的类型是否是一个配置的category
            is_category_query = query_type.lstrip("category=") in configured_categories

            # 获取用于匹配的product列表
            product_query_list = self._get_query_type_list('product')

            # 处理当前查询类型的所有资产
            type_assets, type_category_assets = self._process_assets_by_type(
                assets, query_type, is_category_query, product_query_list
            )

            assets_list.extend(type_assets)
            category_assets_list.extend(type_category_assets)

            mlog.info("--处理完成:{}".format(query_type))

        return assets_list, category_assets_list

    def _get_query_type_list(self, query_type):
        """获取查询类型配置列表"""
        query_type_value = self.field_mappings.get(query_type, "")
        return query_type_value.split("||") if query_type_value else []

    def _process_assets_by_type(self, assets, query_type, is_category_query, product_query_list):
        """按类型处理资产数据"""
        type_assets = []
        type_category_assets = []

        # 已存在的ip + port
        existing_ip_port_set = set()

        for asset in assets:
            # 基础数据验证和提取
            asset_data = self._extract_asset_data(asset)
            if not asset_data:
                continue

            ip_, port_, protocol, product_list, country = asset_data

            if (ip_, port_) in existing_ip_port_set:
                continue
            else:
                existing_ip_port_set.add((ip_, port_))

            # 特殊处理：视频监控专题过滤
            if self._should_skip_video_asset(protocol, product_list):
                continue

            # 根据查询类型处理产品信息
            product = self._process_product_by_query_type(
                query_type, product_list, port_, is_category_query, product_query_list
            )

            if not product:
                continue

            # 分类处理：Category专题 vs 普通资产
            asset_tuple = (ip_, str(port_), protocol, product, country)

            if is_category_query:
                type_category_assets.append(asset_tuple)
            else:
                type_assets.append(asset_tuple)

        return type_assets, type_category_assets

    def _extract_asset_data(self, asset):
        """提取和验证资产基础数据"""
        ip_ = asset.get("ip", "")
        if not is_ipv4_correct(ip_):
            return None

        port_ = asset.get("port", "")
        protocol = asset.get("protocol", "")
        product_list = asset.get("product", [])
        country = asset.get("country", "")

        # 验证必要字段
        if not product_list:
            return None

        # 清理protocol字段
        protocol = self._clean_protocol(protocol)

        return ip_, port_, protocol, product_list, country

    def _clean_protocol(self, protocol):
        """清理协议字段"""
        invalid_values = {"null", "unknown", "", None}
        return "" if protocol in invalid_values else protocol

    def _should_skip_video_asset(self, protocol, product_list):
        """判断是否应该跳过视频监控资产（accvisio相关）"""
        if self.asset_type_id != 7:  # 非视频监控专题
            return False

        # 检查protocol中是否包含accvisio
        if protocol and "accvisio" in protocol.lower():
            return True

        # 检查product_list中是否包含accvisio
        return any("accvisio" in str(p).lower() for p in product_list)

    def _process_product_by_query_type(self, query_type, product_list, port_, is_category_query, product_query_list):
        """根据查询类型处理产品信息"""
        if is_category_query:
            return self._process_category_query(query_type, product_list)
        elif query_type == "product":
            return self._process_product_query(product_query_list, product_list, port_)
        elif query_type == "protocol":
            return self._process_protocol_query(product_list)
        else:
            # 对于从 _classify_assets 来的未知类型 (非 p/p/c)，返回空
            return ""

    def _process_product_query(self, query_type_list, product_list, port_):
        """处理product类型查询"""
        for query_product in query_type_list:
            if query_product in product_list:
                # 特殊处理：PostgreSQL在54321端口时映射为人大金仓
                if query_product == "PostgreSQL" and str(port_) == "54321":
                    return "人大金仓"
                return query_product
        return ""

    def _process_protocol_query(self, product_list):
        """处理protocol类型查询"""
        if not product_list:
            return ""
        return "|||".join(filter(None, product_list))

    def _process_category_query(self, category_name, product_list):
        """处理通用的Category类别查询，使用Category名称作为前缀。"""
        if not product_list:
            return ""
        category_name = category_name.lstrip("category=")

        if "oa" in self.asset_type_en:
            return self._process_oa_query(category_name, product_list)
        elif "vpn" in self.asset_type_en:
            return self._process_vpn_query(product_list)
        elif self.asset_type_id == 10:
            return self._process_router_query(product_list)
        elif self.asset_type_id == 11:
            return self._process_cmp_query(product_list)
        elif self.asset_type_id == 12:
            return self._process_nas_query(product_list)
        #>>
        # exclude_product_list = ["NGINX", "asustor", "ProFTPD", "php", "jQuery", "IBM-Postfix", "squid", "LIGHTTPD",
        #                         "iengine", "IIS", "AUTOMATTIC-WordPress", "Apple-CUPS", "MacOS", "Oracle-JSP", "APACHE",
        #                         "Microsoft", "ThinkPHP", "Tornado-Server", "GlobalSign", "roundcube", "OpenResty",
        #                         "Linux", "Python", "Bootstrap", "Jetty", "C3.js", "EMQX", "操作系统", "django", "系统"]
        # 如果product_list中包含这些exclude_product_list内的关键字，跳过，取不一样的一条product
        #
        # for product in product_list:
        #     if any(exclude_product.lower() in product.lower() for exclude_product in exclude_product_list):
        #         product_list.remove(product)
        # 使用 category_name 作为前缀
        prefix = "{}-".format(category_name)
        return prefix + product_list[0]

    def _process_oa_query(self, query_type, product_list):
        """处理OA类别查询"""
        if not product_list:
            return ""

        # 映射查询类型到前缀
        prefix_mapping = {
            "CRM": "CRM-",
            "ERP": "ERP-",
            CONSTANT_CMS: "CMS-"
        }
        prefix = ""
        for k,v in prefix_mapping.items():
            if k in query_type:
                prefix = v
                break
        return prefix + product_list[0] if prefix else ""

    def _process_vpn_query(self, product_list):
        """处理vpn类别查询"""
        if not product_list:
            return ""
        for product in product_list:
            if "vpn" in product.lower():
                return product

    def _process_router_query(self, product_list):
        """处理路由器类别查询"""
        if not product_list:
            return ""
        for product in product_list:
            if any(["router" in product.lower(),
                    "路由器" in product,
                    u"路由器" in product]):
                return product

    def _process_cmp_query(self, product_list):
        """处理云管理平台类别查询"""
        if not product_list:
            return ""
        for product in product_list:
            # 使用any函数简化条件判断
            if any(["云" in product,
                    u"云" in product,
                    "平台" in product,
                    u"平台" in product,
                    "cloud" in product.lower()]):
                return product

    def _process_nas_query(self, product_list):
        """处理nas类别查询"""
        if not product_list:
            return ""
        for product in product_list:
            if "nas" in product.lower():
                return product

    def _process_category_assets(self, assets_list, category_assets_list):
        """处理所有Category专题资产数据，进行严格去重"""
        # 对普通资产去重
        if not assets_list:
            assets_list = []
        assets_list = list(set(assets_list))

        # >> 如果没有category资产，直接返回
        # if not category_assets_list:
        #     return assets_list

        # 去重category资产
        if not category_assets_list:
            category_assets_list = []
        category_assets_list = list(set(category_assets_list))

        # 核心逻辑：过滤掉在数据库中已存在的资产
        filtered_category_assets = self._filter_existing_category_assets(assets_list, category_assets_list)

        # >>合并结果
        # assets_list.extend(filtered_category_assets)
        return filtered_category_assets
    # >>
    # def _process_oa_assets(self, assets_list, category_assets_list):
    #     """处理OA专题资产数据"""
    #     # 对普通资产去重
    #     if assets_list:
    #         assets_list = list(set(assets_list))
    #
    #     # 处理OA专题数据
    #     if not category_assets_list:
    #         return assets_list
    #
    #     # 去重OA资产
    #     category_assets_list = list(set(category_assets_list))
    #
    #     # 过滤已存在的资产
    #     filtered_oa_assets = self._filter_existing_category_assets(assets_list, category_assets_list)
    #
    #     # 合并结果
    #     assets_list.extend(filtered_oa_assets)
    #     return assets_list

    def _filter_existing_category_assets(self, assets_list, category_assets_list):
        """过滤已存在的Category资产"""
        # 1. 从内存中的assets_list过滤
        existing_ip_port_set = {(asset[0], asset[1]) for asset in assets_list}

        # >>
        # filtered_list = [
        #     asset for asset in category_assets_list
        #     if (asset[0], asset[1]) not in existing_ip_port_set
        # ]
        #
        # # 2. 从数据库中过滤
        # if filtered_list:
        #     filtered_list = self._filter_category_assets_from_db(filtered_list)

        for asset in category_assets_list:
            if (asset[0], asset[1]) not in existing_ip_port_set:
                assets_list.append(asset)
        # 2. 从数据库中过滤
        if assets_list:
            assets_list = self._filter_category_assets_from_db(assets_list)

        return assets_list


    def _filter_existing_oa_assets(self, assets_list, oa_category_list):
        """过滤已存在的OA资产"""
        # 1. 从内存中的assets_list过滤
        # existing_ip_port_set = set((asset[0], asset[1]) for asset in assets_list)
        existing_ip_port_set = {(asset[0], asset[1]) for asset in assets_list}

        filtered_oa_list = [
            oa_asset for oa_asset in oa_category_list
            if (oa_asset[0], oa_asset[1]) not in existing_ip_port_set
        ]

        # 2. 从数据库中过滤
        if filtered_oa_list:
            filtered_oa_list = self._filter_oa_assets_from_db(filtered_oa_list)

        return filtered_oa_list

    def _filter_oa_assets_from_db(self, oa_assets_list):
        """从数据库中过滤已存在的OA资产"""
        if not oa_assets_list:
            return []

        # 批量构建查询条件，避免过多的OR查询
        ip_port_pairs = [(oa[0], oa[1]) for oa in oa_assets_list]

        # 分批查询，避免单次查询条件过多
        batch_size = 1000
        existing_pairs = set()

        for i in range(0, len(ip_port_pairs), batch_size):
            batch_pairs = ip_port_pairs[i:i + batch_size]

            # 构建当前批次的查询条件
            q_batch = Q()
            for ip, port in batch_pairs:
                q_batch |= Q(asset_ip=ip, asset_port=port)

            # 查询当前批次
            batch_existing = AssetInfoModel.objects.filter(q_batch).values_list(
                'asset_ip', 'asset_port'
            )

            # 添加到已存在集合
            existing_pairs.update((ip, str(port)) for ip, port in batch_existing)

        # 过滤掉数据库中已存在的记录
        return [
            oa_asset for oa_asset in oa_assets_list
            if (oa_asset[0], oa_asset[1]) not in existing_pairs
        ]

    def _filter_category_assets_from_db(self, category_assets_list):
        """从数据库中过滤已存在的Category资产"""
        if not category_assets_list:
            return []

        # >>
        # # 批量构建查询条件，避免过多的OR查询
        # ip_port_pairs = [(asset[0], asset[1]) for asset in category_assets_list]
        #
        # # 分批查询，避免单次查询条件过多
        # batch_size = 1000
        # existing_pairs = set()
        #
        # for i in range(0, len(ip_port_pairs), batch_size):
        #     batch_pairs = ip_port_pairs[i:i + batch_size]
        #
        #     # 构建当前批次的查询条件
        #     q_batch = Q()
        #     for ip, port in batch_pairs:
        #         q_batch |= Q(asset_ip=ip, asset_port=port)
        #
        #     # 查询当前批次
        #     batch_existing = AssetInfoModel.objects.exclude(asset_type=self.asset_type_id).filter(q_batch).values_list(
        #         'asset_ip', 'asset_port'
        #     )
        #
        #     # 添加到已存在集合
        #     existing_pairs.update((ip, str(port)) for ip, port in batch_existing)
        #
        # # 过滤掉数据库中已存在的记录
        # return [
        #     asset for asset in category_assets_list
        #     if (asset[0], asset[1]) not in existing_pairs
        # ]

        # 获取除当前类型外的所有资产缓存
        other_types_cache = cache_obj._get_other_types_asset_cache(self.asset_type_id)

        # 直接使用集合操作进行过滤，只与其他类型对比
        filtered_assets = []
        for asset in category_assets_list:
            asset_key = (asset[0], asset[1])
            if asset_key not in other_types_cache:  # 只检查是否在其他类型中存在
                filtered_assets.append(asset)

        mlog.info("资产去重（排除其他类型）：原始{}条，过滤后{}条".format(
            len(category_assets_list), len(filtered_assets)))
        return filtered_assets

    def save_to_local(self, assets_list):
        """推送数据到存储"""
        if not assets_list:
            mlog.warning("没有资产数据需要推送")
            return

        try:
            t1 = datetime.datetime.now()

            # >>>生成文件名
            # file_name = "{}.txt".format(self.ak.map_file_name.get(self.category))
            file_name = "{}.txt".format(self.asset_type_en)

            # 写入本地文件
            mlog.info("开始写入,专题:{}， 数量:{}".format(self.category, len(assets_list)))
            self.ak.to_txt(file_name, assets_list)

            t2 = datetime.datetime.now()
            mlog.info("写入本地完成，耗时:{}秒".format((t2 - t1).total_seconds()))
        except Exception as e:
            mlog.exception("推送数据失败：{}".format(e))

    def save_to_db(self, assets_list):
        """将资产数据保存到数据库"""
        mlog.info("开始保存资产数据到数据库")
        try:
            if not assets_list:
                mlog.info("没有资产数据需要插入数据库")
                return

            # 先删除旧数据
            with transaction.atomic():
                mlog.info("开始删除旧数据")
                AssetInfoModel.objects.filter(asset_type=self.asset_type_id).exclude(asset_source=1).delete()

            # 分批处理数据
            batch_size = 100000  # 每批处理的记录数
            total_count = len(assets_list)
            mlog.info(u"开始分批插入数据，共: {}条".format(total_count))

            for i in range(0, total_count, batch_size):
                # 创建当前批次的对象列表
                create_list = []
                batch_end = min(i + batch_size, total_count)
                current_batch = assets_list[i:batch_end]

                for asset in current_batch:
                    create_list.append(AssetInfoModel(
                        asset_ip=asset[0],
                        asset_port=asset[1],
                        protocol=asset[2],
                        product=asset[3],
                        asset_type=self.asset_type_id,
                        country=asset[4],
                        asset_source=2
                    ))

                # 批量插入当前批次
                with transaction.atomic():
                    AssetInfoModel.objects.bulk_create(create_list, batch_size=10000)

                mlog.info(u"已插入 {}/{} 条数据".format(batch_end, total_count))

            # 数据库更新完成后，同步更新缓存
            cache_obj._update_cache_after_save(self.asset_type_id, assets_list)

            mlog.info("资产数据插入数据库完成:{}条".format(total_count))
        except Exception as e:
            mlog.exception("{},插入数据库失败: {}".format(self.category, e))

    # >>>def fetch_with_retry(self, q, scroll_id, operation="滚动查询", retry_count=3, retry_interval=5):
    #     """统一的重试获取函数，处理接口失败和token过期情况
    #
    #     Args:
    #         q: 查询条件
    #         scroll_id: 滚动ID
    #         token_: 当前token
    #         aj: AutoJudge实例
    #         operation: 操作描述，用于日志
    #         retry_count: 最大重试次数
    #         retry_interval: 重试间隔(秒)
    #
    #     Returns:
    #         tuple: (响应数据dict, 新token)，失败返回(None, token_)
    #     """
    #     for attempt in range(retry_count + 1):  # +1是为了包含第一次尝试
    #         # 第一次不是重试，所以从第二次开始才打印重试信息
    #         if attempt > 0:
    #             mlog.info("访问资产%s接口失败，等待%d秒，第%d次重试",
    #                       operation, retry_interval, attempt)
    #             time.sleep(retry_interval)
    #             # # 每次重试前刷新token
    #             # aj.do_asset_url_login()
    #             # token_ = aj.token_
    #
    #         # 调用接口
    #         res = self.request_asset.asset_search_next(q, scroll_id=scroll_id)
    #         if res:  # 如果成功获取到响应，立即返回
    #             res_data = json.loads(res)
    #             if res_data.get("code") == 200:
    #                 return res_data
    #             else:
    #                 mlog.warning("资产接口正常,但是返回数据中code不为200，返回数据:{}".format(res))
    #                 return None
    #
    #         # if not res:
    #         #     continue  # 接口调用失败，继续重试
    #         # # 尝试解析结果
    #         # try:
    #         #     res_data = json.loads(res)
    #         #     # 处理token过期
    #         #     if res_data.get("code") == 401:
    #         #         mlog.info("token过期，重新登录")
    #         #         # aj.do_asset_url_login()
    #         #         # token_ = aj.token_
    #         #         token_ = self.visit_api.get_oauth_token()
    #         #         res = self.visit_api.asset_search_next(q, token_, scroll_id)
    #         #         if res:
    #         #             return json.loads(res), token_
    #         #         continue  # token刷新后仍然失败，继续重试
    #         #     return res_data, token_  # 成功获取数据
    #         # except ValueError:
    #         #     mlog.error("接口返回数据解析失败")
    #         #     continue
    #
    #     # 所有重试都失败
    #     mlog.warning("连续%d次尝试后，资产%s接口依然失败", retry_count + 1, operation)
    #     return None

    # >>>def run(self):
    #     """主流程"""
    #
    #     scroll_id = ""
    #     st = datetime.datetime.now()
    #     mlog.info("开始时间：{}".format(st))
    #
    #     # 按查询类型收集资产
    #     result = defaultdict(list)
    #     # 查询资产
    #     for q in self.fofa_query():
    #         n = 0
    #         mlog.info("查询语句：{}".format(q))
    #         all_scroll_count = 0
    #         batch_size = 10000
    #         while True:
    #             n += 1
    #             # >>>res_data = self.fetch_with_retry(q, scroll_id)
    #             res = self.request_asset.asset_search_next(q, scroll_id=scroll_id)
    #             if res:  # 如果成功获取到响应，立即返回
    #                 res_data = json.loads(res)
    #                 if res_data.get("code") != 200:
    #                     mlog.warning("资产接口正常,但是返回数据中code不为200，返回数据:{}".format(res))
    #                     if "expired" in res.get("msg") or res.get("code") == 401:
    #                         mlog.info("token过期，重新登录")
    #                         self.request_asset.login()
    #                         res = self.request_asset.asset_search_next(q, scroll_id=scroll_id)
    #                         if res:
    #                             res_data = json.loads(res)
    #                             if res_data.get("code")!= 200:
    #                                 mlog.warning("重新登录后再次请求，资产接口正常,但是返回数据中code不为200，返回数据:{}".format(res))
    #                                 mlog.warning("退出资产查询")
    #                                 self.request_asset.close_session()
    #                                 return False
    #                     else:
    #                         mlog.warning("退出资产查询")
    #                         # 关闭会话
    #                         self.request_asset.close_session()
    #                         return False
    #             else:
    #                 mlog.warning("连续%d次尝试后，资产接口依然失败", 3 + 1)
    #                 # 关闭会话
    #                 self.request_asset.close_session()
    #                 return False
    #             if res_data:
    #                 try:
    #                     data = res_data.get("data", {}).get("data", {})
    #                     if not data:
    #                         mlog.warning("data.data为空")
    #                         break
    #                     if n == 1:
    #                         mlog.info("资产总数：{}".format(data.get("total")))
    #                         # 计算需要循环的次数
    #                         total_count = int(data.get("total"))
    #                         all_scroll_count = 1 if total_count < batch_size else int(
    #                             math.ceil(total_count / batch_size)) + 1
    #                     scroll_id = data.get("scroll_id")
    #
    #                     # 根据查询类型分类保存资产
    #                     assets = data.get("assets", [])
    #                     # 常量定义
    #                     QUERY_TYPES_LIST = ["protocol", "product", "CRM", "ERP", "内容管理系统"]
    #                     # 分类保存资产
    #                     for i in QUERY_TYPES_LIST:
    #                         if i in q:
    #                             result[i].extend(assets)
    #                             break
    #
    #                 except Exception:
    #                     mlog.exception("返回数据异常, 当前类型任务停止。 res_data:{}".format(res_data))
    #                     # 关闭会话
    #                     self.request_asset.close_session()
    #                     return False
    #             else:
    #                 mlog.error("第{}次查询error, 当前类型任务停止".format(n))
    #                 # 关闭会话
    #                 self.request_asset.close_session()
    #                 return False
    #
    #             mlog.info("第{}次查询完成，数量：{}，时间：{}".format(n, len(assets), datetime.datetime.now()))
    #             time.sleep(1)  # 防止请求过于频繁
    #
    #             if not scroll_id:
    #                 break
    #
    #         if n < all_scroll_count/2:
    #             mlog.warning("需要循环查询的次数：{}, 实际循环的次数：{}。结果不一致，退出本专题查询！".format(all_scroll_count, n))
    #             # 关闭会话
    #             self.request_asset.close_session()
    #             return False
    #     # 关闭会话
    #     self.request_asset.close_session()
    #     # 处理资产数据
    #     assets_list = self.process_assets(result)
    #
    #     if CommonConf().is_dev():  # 开发环境
    #         mlog.info("开发环境，不进行数据库操作")
    #     else:  # 线上
    #         # 存入数据库
    #         self.save_to_db(assets_list)
    #
    #         # self.save_to_local(assets_list)   # 存入本地
    #
    #     et = datetime.datetime.now()
    #     mlog.info("总耗时：{}秒".format((et - st).total_seconds()))

    def run(self):
        """主流程"""
        # >>>scroll_id = ""
        st = datetime.datetime.now()
        mlog.info("开始时间：{}".format(st))

        # 按查询类型收集资产
        result = defaultdict(list)

        try:
            # 查询资产
            for q in self.fofa_query():
                if not self._process_single_query(q, result):
                    return False

            # 处理和保存资产数据
            self._process_and_save_assets(result)

        except Exception as e:
            mlog.exception("资产查询主流程异常: {}".format(e))
            return False
        finally:
            # 确保会话被关闭
            self.request_asset.close_session()

        et = datetime.datetime.now()
        mlog.info("总耗时：{}秒".format((et - st).total_seconds()))
        return True

    def _process_single_query(self, query, result):
        """处理单个查询语句

        Args:
            query: 查询语句
            result: 结果收集字典

        Returns:
            bool: 处理是否成功
        """
        mlog.info("查询语句：{}".format(query))
        scroll_id = ""
        query_count = 0
        all_scroll_count = 0
        batch_size = 10000

        while True:
            query_count += 1

            # 执行查询
            query_result = self._execute_query(query, scroll_id)
            if not query_result:
                return False

            res_data, scroll_id = query_result

            # 处理查询结果
            process_result = self._process_query_result(
                res_data, query_count, batch_size, result, query
            )

            if not process_result:
                return False

            _, all_scroll_count = process_result

            # 检查是否继续查询
            if not scroll_id:
                break

            # 防止请求过于频繁
            time.sleep(1)

        # 验证查询完整性
        return self._validate_query_completeness(query_count, all_scroll_count)

    def _execute_query(self, query, scroll_id):
        """执行单次查询

        Args:
            query: 查询语句
            scroll_id: 滚动ID

        Returns:
            tuple: (响应数据, 新的scroll_id) 或 None
        """
        res = self.request_asset.asset_search_next(query, scroll_id=scroll_id)

        if not res:
            mlog.warning("资产接口调用失败")
            return None

        try:
            res_data = json.loads(res)
        except (ValueError, TypeError) as e:
            mlog.error("响应数据解析失败: {}".format(e))
            return None

        # 处理非200状态码
        if res_data.get("code") != 200:
            return self._handle_non_200_response(res_data, query, scroll_id)

        try:
            scroll_id = res_data.get("data", {}).get("data", {}).get("scroll_id", "")
        except Exception as e:
            mlog.exception("获取scroll_id失败: {}".format(e))
            mlog.info("请求结果: {}".format(json.dumps(res_data, ensure_ascii=False)))
            return None

        return res_data, scroll_id

    def _handle_non_200_response(self, res_data, query, scroll_id):
        """处理非200响应状态码

        Args:
            res_data: 响应数据
            query: 查询语句
            scroll_id: 滚动ID

        Returns:
            tuple: (响应数据, scroll_id) 或 None
        """
        mlog.warning("资产接口返回非200状态码，返回数据:{}".format(res_data))

        # 处理token过期
        if self._is_token_expired(res_data):
            mlog.info("token过期，重新登录")
            self.request_asset.login()

            # 重新请求
            res = self.request_asset.asset_search_next(query, scroll_id=scroll_id)
            if res:
                try:
                    new_res_data = json.loads(res)
                    if new_res_data.get("code") == 200:
                        return new_res_data, new_res_data.get("data", {}).get("data", {}).get("scroll_id", "")
                    else:
                        mlog.warning("重新登录后仍然返回非200状态码")
                except (ValueError, TypeError):
                    mlog.error("重新登录后响应数据解析失败")

        mlog.warning("退出资产查询")
        return None

    def _is_token_expired(self, res_data):
        """判断是否token过期

        Args:
            res_data: 响应数据

        Returns:
            bool: 是否token过期
        """
        return (
                res_data.get("code") == 401 or
                "expired" in str(res_data.get("msg", "")).lower()
        )

    def _process_query_result(self, res_data, query_count, batch_size, result, query):
        """处理查询结果数据

        Args:
            res_data: 响应数据
            query_count: 查询次数
            batch_size: 批次大小
            result: 结果收集字典
            query: 查询语句

        Returns:
            tuple: (data, all_scroll_count) 或 None
        """
        try:
            data = res_data.get("data", {}).get("data", {})
            if not data:
                mlog.warning("data.data为空")
                return None

            # 第一次查询时计算总循环次数
            all_scroll_count = 0
            if query_count == 1:
                total_count = int(data.get("total", 0))
                mlog.info("资产总数：{}".format(total_count))
                all_scroll_count = self._calculate_scroll_count(total_count, batch_size)

            # 分类保存资产
            assets = data.get("assets", [])
            self._classify_assets(assets, query, result)

            mlog.info("第{}次查询完成，数量：{}，时间：{}".format(
                query_count, len(assets), datetime.datetime.now()
            ))

            return data, all_scroll_count

        except Exception as e:
            mlog.exception("处理查询结果异常: {}".format(e))
            return None

    def _calculate_scroll_count(self, total_count, batch_size):
        """计算需要滚动查询的次数

        Args:
            total_count: 总数量
            batch_size: 批次大小

        Returns:
            int: 滚动查询次数
        """
        if total_count <= batch_size:
            return 1
        return int(math.ceil(total_count / batch_size)) + 1

    # 通用优化版分类方法
    def _classify_assets(self, assets, query, result):
        """
        通用资产分类方法，根据查询语句自动识别专题类型并分类存储。
        支持 protocol、product 及所有 category=XXX 查询。
        :param assets: 资产列表
        :param query: 查询语句字符串
        :param result: 分类结果字典
        """
        # 1. 优先判断 protocol/product 查询
        if 'protocol=' in query:
            result['protocol'].extend(assets)
            return
        if 'product=' in query:
            result['product'].extend(assets)
            return
        # 2. 动态识别 category=XXX 查询
        category_match = re.search(r'category\s*=\s*"([^"]+)"', query)
        if category_match:
            category_value = category_match.group(1)
            # 统一 key 格式，防止混淆
            key = 'category={}'.format(category_value)
            result[key].extend(assets)
            return

        # 3. 未识别类型，归为 other
        result['other'].extend(assets)

    def _validate_query_completeness(self, actual_count, expected_count):
        """验证查询完整性

        Args:
            actual_count: 实际查询次数
            expected_count: 期望查询次数

        Returns:
            bool: 查询是否完整
        """
        if expected_count > 0 and actual_count < expected_count / 2:
            mlog.warning("需要循环查询的次数：{}, 实际循环的次数：{}。结果不一致，退出本专题查询！".format(
                expected_count, actual_count
            ))
            return False
        return True

    def _process_and_save_assets(self, result):
        """处理和保存资产数据

        Args:
            result: 查询结果字典
        """
        # 处理资产数据
        assets_list = self.process_assets(result)

        if CommonConf().is_dev():
            mlog.info("开发环境，不进行数据库操作")
        else:
            # 存入数据库
            self.save_to_db(assets_list)
            # self.save_to_local(assets_list)   # 存入本地


def process_assets(obj_list, region, time_str, action_type="查询"):
    """处理资产查询或推送
    Args:
        obj_list: 资产类型对象列表
        region: 区域类型 1-全境 2-境内 3-境外
        time_str: 查询时间
        action_type: 操作类型，用于日志展示
    """
    if region == 1:
        region_type = "全境"
    elif region == 2:
        region_type = "境内"
    else:
        region_type = "境外"

    restart_flag = True
    for obj_ in obj_list:
        mlog.info("================开始{0}{1}专题：{2}".format(
            action_type, region_type, obj_.asset_type))

        if action_type == "查询":
            flag = handle_query_action(obj_, time_str, region_type, restart_flag)
        else:  # 推送
            flag = handle_push_action(obj_, region_type, restart_flag)

        if not flag:
            mlog.error("{0}{1}专题处理失败：{2}".format(action_type, region_type, obj_.asset_type))
            restart_flag = False

        mlog.info("================结束{0}{1}专题：{2}".format(
            action_type, region_type, obj_.asset_type))

    return restart_flag


def handle_query_action(obj_, time_str, region_type, restart_flag):
    """处理查询操作"""
    if obj_.id == 8:  # 物联网专题从文件中读取
        mlog.info("物联网专题资产从文件中读取")
        file_name = "IOT.csv"
        # 读取csv文件，插入资产表
        flag_ = AssetKnowledgeUtil().read_csv_to_table(file_name, obj_)
        if flag_ is False:
            restart_flag = False
    else:
        if region_type == "全境":
            is_all_region = True
        else:
            is_all_region = False
        flag_ = AssetKnowledge(time_str, obj_, is_all_region=is_all_region).run()
        if flag_ is False:
            restart_flag = False
    return restart_flag


def handle_push_action(obj_, region_type, restart_flag):
    """处理推送操作"""
    # 查询资产表，获取资产信息，存入本地
    mlog.info("查询专题：{}".format(obj_.asset_type))
    if region_type == "全境":
        assets_list = AssetInfoModel.objects.filter(asset_type=obj_.id).values_list("asset_ip", "asset_port",
                                                                                    "protocol", "product")
    elif region_type == "境内":
        assets_list = AssetInfoModel.objects.filter(asset_type=obj_.id, country="中国").values_list(
            "asset_ip", "asset_port", "protocol", "product")
    else:
        assets_list = AssetInfoModel.objects.filter(asset_type=obj_.id).exclude(country="中国").values_list(
            "asset_ip", "asset_port", "protocol", "product")
    mlog.info("资产数量：{}".format(len(assets_list)))
    file_name = "{0}.txt".format(obj_.asset_type_en)
    AssetKnowledgeUtil().to_txt(file_name, assets_list)
    flag_ = AssetKnowledgeUtil().check_and_put(file_name)
    if flag_ is False:
        restart_flag = False
    return restart_flag


def get_asset_types_by_names(type_names=None, exclude_names=None):
    """通过资产类型名称获取资产类型对象列表
    Args:
        type_names: 指定的资产类型名称列表
        exclude_names: 需要排除的资产类型名称列表
    Returns:
        QuerySet: 资产类型对象列表
    """
    query = AssetTypeModel.objects.order_by("id")
    if type_names:
        return query.filter(asset_type__in=type_names)
    elif exclude_names:
        return query.exclude(asset_type__in=exclude_names)
    return query.all()


def get_asset_types_by_ids(type_ids=None, exclude_types=None):
    """通过资产类型ID获取资产类型对象列表
    Args:
        type_ids: 逗号分隔的资产类型ID字符串
        exclude_types: 需要排除的资产类型ID字符串
    Returns:
        list: 资产类型对象列表
    """
    try:
        if type_ids:
            # 先将QuerySet转换为列表
            asset_type_list = list(AssetTypeModel.objects.filter(
                id__in=type_ids.split(",")).order_by("id"))

            if not asset_type_list:
                mlog.warning(u"未找到指定ID的资产类型: %s" % type_ids)
                return []

            # 分离OA和非OA类型
            non_oa_types = []
            oa_types = []

            for asset_type in asset_type_list:
                if "OA" in asset_type.asset_type:
                    oa_types.append(asset_type)
                else:
                    non_oa_types.append(asset_type)

            # 合并列表，OA类型放在最后
            return non_oa_types + oa_types

        elif exclude_types:
            return list(AssetTypeModel.objects.exclude(
                id__in=exclude_types.split(",")).order_by("id"))

        return []

    except Exception as e:
        mlog.exception(u"获取资产类型列表失败: %s" % str(e))
        return []


if __name__ == '__main__':
    """
    查询资产: nohup python asset_knowledge.py -a 1 -t 1,2 -c 3,4 -d 30 &
    推送到HDFS: nohup python asset_knowledge.py -a 2 --put_types 1,2,3 &
    查询并推送(默认模式): nohup python asset_knowledge.py -a 3 &
    """

    parser = argparse.ArgumentParser(description='资产知识库数据处理脚本')
    parser.add_argument('--action', '-a', type=str, choices=['1', '2', '3'], required=True,
                        help='操作类型：1-查询资产，2-推送到HDFS，3-查询资产并推送到HDFS(默认模式)')
    parser.add_argument('--all_types', type=str, help='全境资产类型ID，多个用逗号分隔')
    parser.add_argument('--src_types', type=str, help='境内资产类型ID，多个用逗号分隔')
    parser.add_argument('--dst_types', type=str, help='境外资产类型ID，多个用逗号分隔')
    # parser.add_argument('--put_types', '-p', type=str, help='推送到HDFS的专题服务ID，多个用逗号分隔')
    parser.add_argument('--days', '-d', type=int, default=30, help='查询天数，默认30天')

    args = parser.parse_args()
    time_str = (datetime.datetime.now() - datetime.timedelta(days=args.days)).strftime(DATE_FORMAT)

    # 缓存资产
    if args.action in ["1", "3"]:
        cache_obj = AssetKnowledgeCache()
        cache_obj._build_global_asset_cache_by_type()

    if args.action == "3":  # 默认模式：查询并推送
        # 获取全境和境内资产类型 --格式："1,2"（"数据库专题服务", "FTP专题服务"）
        default_all_types = "1,2"
        # 全境资产类型：数据库和FTP专题
        all_region_obj_list = get_asset_types_by_ids(type_ids=default_all_types)
        # 境内资产类型：除了数据库和FTP专题之外的所有专题
        src_obj_list = get_asset_types_by_ids(exclude_types=default_all_types)

        mlog.info("----------开始查询并推送处理")
        restart_flag = True
        # 查询资产
        mlog.info("----------开始查询资产")
        restart_flag = process_assets(all_region_obj_list, 1, time_str, "查询")
        restart_flag = process_assets(src_obj_list, 2, time_str, "查询")
        mlog.info("----------结束查询资产")

        # 推送到HDFS
        mlog.info("----------开始推送到HDFS")
        all_types = get_asset_types_by_ids(exclude_types="2")
        # 推送全部的
        restart_flag = process_assets(all_types, 1, time_str, "推送")
        # 推送境内的
        ftp_objs = get_asset_types_by_ids(type_ids="2")
        restart_flag = process_assets(ftp_objs, 2, time_str, "推送")
        mlog.info("----------结束推送到HDFS")
        # 重启基线任务
        if restart_flag:
            AssetKnowledgeUtil().restart_setl()
        mlog.info("----------结束查询并推送处理")

    else:  # 自定义模式
        if args.action == "1":  # 查询资产
            mlog.info("----------开始查询资产")
            if args.all_types:
                process_assets(get_asset_types_by_ids(type_ids=args.all_types), 1, time_str, "查询")
            if args.src_types:
                process_assets(get_asset_types_by_ids(type_ids=args.src_types), 2, time_str, "查询")
            mlog.info("----------结束查询资产")

        elif args.action == "2":  # 推送到HDFS
            mlog.info("----------开始推送到HDFS")
            restart_flag = True
            if args.all_types:
                mlog.info("推送全境资产到HDFS的专题ID：{}".format(args.all_types))
                restart_flag = process_assets(get_asset_types_by_ids(type_ids=args.all_types), 1, time_str, "推送")
            if args.src_types:
                mlog.info("推送境内资产到HDFS的专题ID：{}".format(args.src_types))
                restart_flag = process_assets(get_asset_types_by_ids(type_ids=args.src_types), 2, time_str, "推送")
            if args.dst_types:
                mlog.info("推送境外资产到HDFS的专题ID：{}".format(args.dst_types))
                restart_flag = process_assets(get_asset_types_by_ids(type_ids=args.dst_types), 3, time_str, "推送")
            # 重启基线任务
            if restart_flag:
                AssetKnowledgeUtil().restart_setl()
            mlog.info("----------结束推送到HDFS")
