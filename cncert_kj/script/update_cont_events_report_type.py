# -*- coding: UTF-8 -*-
"""
1、搜索结束时间为当前时间，开始时间为12小时之前的时间范围内的  重要数据库告警事件
2、随机将10%的数据 的事件来源字段修改为 ‘特定协议和报文日志研判’
3、只查netflow的
4、每天的0点和12点各跑一次
"""
import json
import sys
import time
import random
import traceback

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction

from cncert_kj.utils import logger

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('update_cont_events_report_type')


class UpdateContEventsReportType:
    """
    1、搜索结束时间为当前时间，开始时间为12小时之前的时间范围内的  重要数据库告警事件
    2、数据库告警事件 id为 [61,62,63,64]
    3、随机将10%的数据 的事件来源字段修改为 ‘特定协议和报文日志研判’ 下标=3
    4、只查netflow的 ，方便后期修改
    """

    def __init__(self):
        self.end_time = int(time.time())
        self.start_time = int(time.time()) - 60 * 60 * 12
        self.percentage = 0.1  # 百分比
        self.report_type = 3  # 特定协议和报文日志研判
        self.event_type = [61, 62, 63, 64]

    def get_time_interval_data(self):
        """
        获取时间区间内的数据
        """

        try:
            sql = '''
                   SELECT event_id FROM internal_app_bsa_gjk.continuous_events WHERE end_time >= %s and end_time <= %s 
                   and event_type IN %s and report_type=2
                   
                   '''
            continuous_events_param = CPgSqlParam(sql, params=(self.start_time, self.end_time, tuple(self.event_type),))
            continuous_events_list = json.loads(CFunction.execute(continuous_events_param))
            return continuous_events_list
        except Exception as e:
            mlog.exception("查询sql失败：{}".format(e))
            return []

    def get_random_data(self, data_list):
        """
        随机获取10%的数据
        """
        try:
            # 计算要选择的数据数量
            num_items_to_select = int(len(data_list) * self.percentage)

            # 随机选择指定数量的数据
            random_selection = random.sample(data_list, num_items_to_select)
            return random_selection
        except Exception:
            mlog.exception("获取时间区间内的数据失败：{}".format(traceback.format_exc()))
            return []

    def save_to_pg(self, event_ids):
        mlog.info("更新的event_id:{}".format(event_ids))
        try:
            update_sql = '''
                        UPDATE internal_app_bsa_gjk.continuous_events
                        SET 
                            report_type=%s
                        WHERE event_id IN %s
                        '''
            _param = CPgSqlParam(update_sql, params=(self.report_type, tuple(event_ids)))
            json.loads(CFunction.execute(_param))
        except Exception:
            mlog.error(traceback.format_exc())

    def run(self):
        try:
            st = int(time.time())
            mlog.info("start to do task")
            data = self.get_time_interval_data()
            mlog.info("data:{}".format(data))
            if not data:
                mlog.info("no data")
            else:
                random_data = self.get_random_data(data)
                if random_data:
                    event_ids = [str(i[0]) for i in random_data]
                    # 存入数据库
                    self.save_to_pg(event_ids)
                else:
                    mlog.info("no random data")
            mlog.info("end of task, run time:%s s", int(time.time()) - st)
            mlog.info("=========================================================")
        except Exception:
            mlog.exception("======执行任务失败======")


if __name__ == '__main__':
    """
        添加定时任务
        vi /home/<USER>/ISOP/bin/cron/crontabConf
        插入内容, 每天的0点和12点各跑一次
        0 0 0,12 * * * python /home/<USER>/ISOP/apps/cncert_kj/script/update_cont_events_report_type.py
    """
    obj = UpdateContEventsReportType()
    obj.run()
