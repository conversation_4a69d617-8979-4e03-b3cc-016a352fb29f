---
description: 
globs: 
alwaysApply: true
---
# 项目开发规则
*使用中文回复问题以及进行注释
## 1. 项目概述
本项目是一个基于 Python 2.7.10 的老旧系统，使用 PostgreSQL 作为主数据库，Elasticsearch 作为搜索引擎。所有开发和维护工作必须严格遵循 Python 2.7.10 的兼容性要求，确保代码逻辑和第三方库能够在该环境下正常运行。

## 2. 开发环境配置

### 2.1 Python 环境
- **版本要求**：使用 Python 2.7.10（最后一次发布于 2015 年 5 月）。不得使用 Python 3.x 或其他 Python 2.x 版本。


## 3. 代码规范

### 3.1 编码风格
- **遵循 PEP 8**：使用 Python 2 的 PEP 8 编码规范。
  - 缩进：4 个空格。
  - 行长度：最大 79 字符。
  - 命名：
    - 模块和包：小写，短名称（如 `utils`）。
    - 类：驼峰命名（如 `DatabaseManager`）。
    - 函数和变量：小写加下划线（如 `fetch_data`）。
- **字符串**：优先使用单引号（`'`），除非字符串中包含单引号。
- **注释**：
  - 使用中文对模块、类、函数写明功能描述。

### 3.3 代码组织
- **模块化**：
  - 将功能拆分为独立模块（如 `db.py`、`search.py`、`utils.py`）。
  - 每个模块顶部需包含文件描述注释。

- **导入顺序**：
  1. 标准库（如 `os`, `logging`）。
  2. 第三方库（如 `psycopg2`, `elasticsearch`）。
  3. 本地模块（如 `from . import utils`）。

## 4. 数据库操作规范

### 4.1 SQL 编写
- **参数化查询**：防止 SQL 注入，使用占位符。
  ```python
  cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
  ```
- **事务管理**：
  - 显式提交或回滚事务。
  ```python
  try:
      cursor.execute("INSERT INTO users (name) VALUES (%s)", ('Alice',))
      conn.commit()
  except psycopg2.Error as e:
      conn.rollback()
      logging.error("Transaction failed: %s", e)
      raise
  ```
- **命名规范**：
  - 表名和字段名：小写加下划线（如 `user_profiles`）。
  - 避免使用 SQL 保留字。

### 4.2 数据库迁移
- **工具**：使用 `alembic` 0.7.x（与 Python 2.7 兼容）管理数据库迁移。
- **流程**：
  1. 初始化迁移环境：`alembic init migrations`。
  2. 创建迁移脚本：`alembic revision -m "description"`。
  3. 应用迁移：`alembic upgrade head`。
- **注意事项**：迁移脚本需明确记录变更内容，并测试回滚操作。

## 5. 搜索引擎操作规范

### 5.1 索引管理
- **索引命名**：小写，带项目前缀（如 `project_users`）。
- **映射定义**：在代码中显式定义索引映射。
  ```python
  mapping = {
      "mappings": {
          "user": {
              "properties": {
                  "name": {"type": "string"},
                  "email": {"type": "string", "index": "not_analyzed"}
              }
          }
      }
  }
  es.indices.create(index='project_users', body=mapping)
  ```

### 5.2 查询规范
- **使用 DSL 查询**：避免直接拼接查询字符串。
  ```python
  query = {
      "query": {
          "match": {
              "name": "Alice"
          }
      }
  }
  result = es.search(index='project_users', body=query)
  ```
- **分页和性能**：
  - 使用 `from` 和 `size` 控制分页。
  - 避免深分页（`from > 10000`）。

## 6. 开发流程

### 6.1 版本控制
- **工具**：使用 Git 管理代码。
- **分支规范**：
  - 主分支：`master`（生产代码）。
  - 开发分支：`dev`（集成测试）。
  - 功能分支：`feature/描述`（如 `feature/add-user-auth`）。
- **提交信息**：
  - 格式：`[类型]: 简要描述`（如 `[fix]: Correct user query bug`）。
  - 类型：`feat`（新功能）、`fix`（修复）、`docs`（文档）、`refactor`（重构）等。

### 6.2 测试
- **测试框架**：使用 `unittest`（Python 2.7 内置）。
- **测试目录**：`tests/`，文件名以 `test_` 开头（如 `test_db.py`）。
- **测试要求**：
  - 数据库和 Elasticsearch 测试需使用 mock 或临时实例。
  - 覆盖主要功能，运行测试命令：
    ```bash
    python -m unittest discover tests
    ```
- **示例测试**：
  ```python
  import unittest
  class TestDB(unittest.TestCase):
      def test_connect(self):
          conn = psycopg2.connect(**DB_CONFIG)
          self.assertTrue(conn)
          conn.close()
  ```

### 6.3 使用 AI 编辑器
- **代码生成**：
  - 提供清晰的上下文（如函数功能、输入输出要求）。
  - 示例提示：“生成一个 Python 2.7 兼容的函数，查询 PostgreSQL 数据库中用户表，返回用户列表。”
- **代码审查**：
  - 使用 AI 检查代码是否符合 PEP 8 和 Python 2.7 兼容性。
  - 验证第三方包版本是否正确。
- **调试支持**：
  - 请求 AI 分析错误日志并提供修复建议。
  - 示例提示：“分析以下 Elasticsearch 连接错误并提供解决方案：...”

