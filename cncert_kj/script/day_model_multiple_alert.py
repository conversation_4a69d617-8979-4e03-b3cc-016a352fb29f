#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
IP倍数模型
IP倍数模型实现逻辑：
第一步计算均值：查询计算最近15天所有重点监控单位IP，每个IP每天流出流量平均值，计算公式类似：(sum(out_bytes) group by sip))/15，以此结果作为基线（这里我们有个阈值的下限设置是20MB，意思是IP对平均每天的流量低于20MB就忽略掉，不会纳入到基线。 另外基线会每天进行更新，算最近15天的）
第二步检测告警：查询上述统计的基线IP每个IP前一天流出流量，如果大于基线流量的5倍，则产生IP告警
每天零点01分执行一次
生成IP流量倍数告警
"""
import datetime
import json
import sys
import time
import traceback
from uuid import uuid4

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.retrieval_task_model import filter_key_unit_ips
from cncert_kj.models.static_baseline_model import get_dynamic_baseline_threshold, get_dynamic_baseline_status
from cncert_kj.models.top_1w_models import update_avg_flow, AlertRuleMode
from cncert_kj.script.alert_day_model.day_filter_rule import get_filter_rule
from cncert_kj.script.event_asset_info import GetAssetInfo
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.script.update_cont_event import update_tllog_sql, update_netflow_sql
from cncert_kj.utils import es_utils, conf_util
from cncert_kj.utils import logger, net
from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.utils.flow_log_util import format_flow_logs
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_filing_sys import RequestFilingSys

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('day_model_multiple_alert')


def get_operator(operator_):
    if operator_:
        if u"电信" in operator_:
            operator_ = "电信"
        elif u"移动" in operator_:
            operator_ = "移动"
        elif u"联通" in operator_:
            operator_ = "联通"
        elif u"联合网络通信" in operator_:
            operator_ = "联通"
        else:
            operator_ = operator_.encode("utf-8")
    else:
        operator_ = ""
    return operator_


class Avg:
    def __init__(self, out_bytes, in_bytes, start_time, end_time, asset_name):
        self.out_bytes = out_bytes
        self.in_bytes = in_bytes
        self.start_time = start_time
        self.end_time = end_time
        self.asset_name = asset_name


class AvgMultiple:
    def __init__(self, out_avg, out_multiple, in_avg, in_multiple, flow_type):
        self.out_avg = out_avg
        self.out_multiple = out_multiple
        self.in_avg = in_avg
        self.in_multiple = in_multiple
        self.flow_type = flow_type


def last_day_flow_query_dsl(ip_list, start_time, end_time):
    query_dsl = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "asset_ip": ip_list
                        }
                    },
                    {
                        "range": {
                            "end_time": {
                                "gte": start_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "size": 0,
        "aggs": {
            "asset_ip": {
                "terms": {
                    "field": "asset_ip",
                    "size": 10000
                },
                "aggs": {
                    "in_bytes": {
                        "sum": {
                            "field": "in_bytes"
                        }
                    },
                    "out_bytes": {
                        "sum": {
                            "field": "out_bytes"
                        }
                    },
                    "start_time": {
                        "min": {
                            "field": "start_time"
                        }
                    },
                    "end_time": {
                        "max": {
                            "field": "end_time"
                        }
                    },
                    "asset_name": {
                        "top_hits": {
                            "size": 1,
                            "_source": {
                                "includes": ["asset_name"]
                            }
                        }
                    }
                }
            }
        }
    }
    return query_dsl


class MultipleAlertModel:
    def __init__(self):
        self.multiple = 5
        self.detect_name = "流量倍数异常告警"
        self.tllog_flow_type = 1
        self.netflow_flow_type = 2
        self.type = 51  # 告警类型51
        # 数据库tc是2， netflow是1
        # 从配置表中获取阈值
        self.tclog_threshold = 1024 * 1024 * 10  # 10 MB 1024 * 1024 * 10
        tclog_threshold = get_dynamic_baseline_threshold(self.type, belong=2)
        self.tclog_threshold = tclog_threshold if tclog_threshold else self.tclog_threshold
        mlog.info("类型：{}； 阈值：{}， 流量类型：{}".format(self.type, self.tclog_threshold, "tclog"))

        self.netflow_threshold = 1024 * 1024 * 10  # 10 MB 1024 * 1024 * 10
        netflow_threshold = get_dynamic_baseline_threshold(self.type, belong=1)
        self.netflow_threshold = netflow_threshold if netflow_threshold else self.netflow_threshold
        mlog.info("类型：{}； 阈值：{}， 流量类型：{}".format(self.type, self.netflow_threshold, "netflow"))

        self.analysis_tech = 2
        self.neflow_fields = ','.join((
            "uuid",
            "name",
            "type",
            "start_time",
            "end_time",
            "sip",
            "src_region",
            "src_operator",
            "src_iot",
            "src_service",
            "src_unit",
            "src_com",
            "sport",
            "dip",
            "dst_region",
            "dst_operator",
            "dst_iot",
            "dst_service",
            "dst_unit",
            "dst_com",
            "dport",
            "bytes_all",
            "bytes_all_down",
            "packets_all",
            "packets_all_down",
            "analysis_tech",
            "ipv6",
            "port_distribution",
            "flow_logs"
        ))
        self.tllog_fields = ','.join((
            "uuid",
            "name",
            "type",
            "start_time",
            "end_time",
            "sip",
            "src_region",
            "src_operator",
            "src_iot",
            "src_service",
            "src_unit",
            "src_com",
            "sport",
            "dip",
            "dst_region",
            "dst_operator",
            "dst_iot",
            "dst_service",
            "dst_unit",
            "dst_com",
            "dport",
            "up_bytes_all",
            "down_bytes_all",
            "up_packets_all",
            "down_packets_all",
            "flow_list",
            "analysis_tech",
            "ipv6",
            "port_distribution",
            "flow_logs"
        ))
        self.tllog_insert_sql_template = """
        INSERT INTO internal_app_bsa_gjk.traffic_alert ({fields}) VALUES ({params})
        """
        self.netflow_insert_sql_template = """
        INSERT INTO internal_app_bsa_gjk.netflow_alert_5min ({fields}) VALUES ({params})
        """

    def get_last_day_flow(self, ip_list, flow_type):
        """
        查询 es获取 上一天流量
        :param flow_type: 流量类型 1： 通联；2； NetFlow
        :param ip_list:
        :return:
        """
        # 每次查询数量， 适配低版本ES （低版本ES terms 查询不能超过 1024）
        step = 1000
        flow_type = int(flow_type)
        if flow_type == self.tllog_flow_type:
            index_name = conf_util.TLLOG_IP_5MIN
        else:
            index_name = conf_util.NETFLOW_IP_5MIN

        today = datetime.date.today()
        start_time = int(time.mktime(today.timetuple())) - 86400
        end_time = int(time.mktime(today.timetuple()))
        res_list = []
        es = es_utils.ESUtil()
        n = 0
        for i in range(0, len(ip_list), step):
            n += 1
            mlog.info("查询第{}批， 数量:{}， 开始时间:{}".format(n, len(ip_list[i:i + step]), time.time()))
            query_dsl = last_day_flow_query_dsl(ip_list[i: i + step], start_time, end_time)
            try:
                result = es.search_es(index_name, query_dsl)
            except Exception as er:
                mlog.error("查询失败：{}, query_dsl:{}".format(er, query_dsl))
                continue
            res = result.get("aggregations", {}).get("asset_ip", {}).get("buckets", [])
            res_list.extend(res)

        data = {}
        for i in res_list:
            asset_ip = net.int32_to_ip(i.get("key", ""))
            out_bytes = int(i.get("out_bytes", {}).get("value", 0))
            in_bytes = int(i.get("in_bytes", {}).get("value", 0))
            start_time = int(i.get("start_time", {}).get("value", 0))
            end_time = int(i.get("end_time", {}).get("value", 0))
            _source = i['asset_name']["hits"]["hits"][0]["_source"]
            asset_name = _source.get("asset_name", "") if _source else ""
            data[asset_ip] = Avg(out_bytes=out_bytes, in_bytes=in_bytes, start_time=start_time, end_time=end_time,
                                 asset_name=asset_name)
        return data

    def get_last_day_flow_logs(self, ip_list, flow_type):
        """
        查询ip对索引, 将查到的数据作为 flow_logs入库
        :return:
        """
        flow_type = int(flow_type)
        if flow_type == self.tllog_flow_type:
            index_name = conf_util.TLLOG_IP_PAIR_5MIN
        else:
            index_name = conf_util.NETFLOW_IP_PAIR_5MIN

        today = datetime.date.today()
        start_time = int(time.mktime(today.timetuple())) - 86400
        end_time = int(time.mktime(today.timetuple()))
        es = es_utils.ESUtil()
        data = {}
        for i in ip_list:
            query_dsl = {
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "term": {
                                    "asset_ip": i
                                }
                            },
                            {
                                "range": {
                                    "end_time": {
                                        "gte": start_time,
                                        "lt": end_time
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": 10
            }
            mlog.info('DSL : {}'.format(query_dsl))
            try:
                result = es.search_es(index_name, query_dsl)
            except Exception as er:
                mlog.error("查询失败：{}, query_dsl:{}".format(er, query_dsl))
                continue
            if not result and result.get("hits", {}).get("hits", []):
                continue
            asset_ip = net.int32_to_ip(i)
            flow_logs = format_flow_logs(result.get("hits", {}).get("hits", []), flow_type)
            data[asset_ip] = flow_logs
        return data

    def get_avg_multiple(self, tc_status, netflow_status):
        """
        获取 均值 * 倍数  flow_type
        返回ip对应的均值, 不太流量类型的ip
        :return:
        """
        query_sql = """
            SELECT asset_ip, in_flow_avg, in_surge_multiple, out_flow_avg, out_surge_multiple, flow_type FROM internal_app_bsa_gjk.alert_rule WHERE is_delete=0;
        """
        result = json.loads(CFunction.execute(CPgSqlParam(query_sql)))
        data = {}
        peer_data = {}
        tl_ip_list = []
        netflow_ip_list = []

        for i in result:
            asset_ip = i[0]
            in_flow_avg = i[1]
            in_surge_multiple = i[2] if i[2] else 0
            out_flow_avg = i[3]
            out_surge_multiple = i[4] if i[4] else 0
            flow_type = i[5]
            data[asset_ip] = AvgMultiple(out_avg=out_flow_avg, out_multiple=out_surge_multiple,
                                         in_avg=in_flow_avg, in_multiple=in_surge_multiple, flow_type=flow_type
                                         )
            if flow_type == self.tllog_flow_type and tc_status:
                tl_ip_list.append(net.ip_to_int32(asset_ip))
            elif flow_type == self.netflow_flow_type and netflow_status:
                netflow_ip_list.append(net.ip_to_int32(asset_ip))
        return data, peer_data, tl_ip_list, netflow_ip_list

    def generate_alert(self, avg_multiple, tl_avg_data, netflow_avg_data):
        """
        对比 es 和 数据库的值 生成告警
        :return:
        """
        tllog_alert_params = []
        netflow_alert_params = []
        tllog_ip_lst = []
        netflow_ip_lst = []
        unit_ip_map = filter_key_unit_ips()
        for asset_ip, dat in avg_multiple.items():
            flow_type = dat.flow_type
            if flow_type == self.tllog_flow_type:
                tllog_alert_params, tllog_ip_lst = self.handle_tc_alert(dat, tl_avg_data, asset_ip, tllog_alert_params,
                                                                        tllog_ip_lst, unit_ip_map)

            elif flow_type == self.netflow_flow_type:
                netflow_alert_params, netflow_ip_lst = self.handle_netflow_alert(dat, netflow_avg_data, asset_ip,
                                                                                 netflow_alert_params, netflow_ip_lst,
                                                                                 unit_ip_map)
        tclog_flow_logs = self.get_last_day_flow_logs(tllog_ip_lst, self.tllog_flow_type)
        netflow_flow_logs = self.get_last_day_flow_logs(netflow_ip_lst, self.netflow_flow_type)
        # 获取运营商和地理位置信息
        with RequestFilingSys() as request_filing_obj:
            tclog_ip_info = request_filing_obj.query_icp_api(tllog_ip_lst)
            netflow_ip_info = request_filing_obj.query_icp_api(netflow_ip_lst)

        rules = get_filter_rule()
        if not rules:
            rules = {}
        src_com_filter = set(rules.get("2", []))
        if tllog_alert_params:
            self.insert_tc_alert(tllog_alert_params, tclog_ip_info, tclog_flow_logs, src_com_filter)
        if netflow_alert_params:
            self.insert_netflow_alert(netflow_alert_params, netflow_ip_info, netflow_flow_logs, src_com_filter)

    def insert_tc_alert(self, tllog_alert_params, tclog_ip_info, tclog_flow_logs, src_com_filter):
        tc_uuid_list = []

        new_tllog_alert_params = []
        for i in tllog_alert_params:
            ip_detail = tclog_ip_info.get(i[5], {})
            flow_logs = tclog_flow_logs.get(i[5], [])

            src_country = ip_detail.get("country", "")
            src_province = ip_detail.get("province", "")
            src_operator = ip_detail.get("operator", "")

            src_com = ip_detail.get("user", "")
            if src_com in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(src_com))
                continue

            src_region = " ".join([src_country, src_province])
            i[6], i[7], i[11] = src_region, src_operator, src_com
            tc_uuid_list.append(i[0])
            flow_logs = "|".join([json.dumps(log, ensure_ascii=False) for log in flow_logs])
            i.append(flow_logs)
            val = ",".join(len(i) * ["%s"])
            CFunction.execute(CPgSqlParam(
                self.tllog_insert_sql_template.format(fields=self.tllog_fields, params=val),
                params=tuple(i)))
            new_tllog_alert_params.append(tuple(i))
        if tc_uuid_list:
            if len(tc_uuid_list) == 1:
                tc_filter_condition = "uuid = '{}'".format(tc_uuid_list[0])
            else:
                tc_filter_condition = "uuid in {}".format(tuple(tc_uuid_list))
            update_tllog_sql(tc_filter_condition)
        mlog.info("生成通联倍数告警数量：{}".format(len(new_tllog_alert_params)))

    def insert_netflow_alert(self, netflow_alert_params, netflow_ip_info, netflow_flow_logs, src_com_filter):
        netflow_uuid_list = []
        new_netflow_alert_params = []
        for i in netflow_alert_params:
            ip_detail = netflow_ip_info.get(i[5], {})
            flow_logs = netflow_flow_logs.get(i[5], [])

            src_region = ip_detail.get("src_region", "")
            src_operator = ip_detail.get("operator", '')

            src_com = ip_detail.get("user", "")
            if src_com in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(src_com))
                continue
            i[6], i[7], i[11] = src_region, src_operator, src_com
            netflow_uuid_list.append(i[0])
            flow_logs = "|".join([json.dumps(log, ensure_ascii=False) for log in flow_logs])
            i.append(flow_logs)
            val = ",".join(len(i) * ["%s"])
            CFunction.execute(CPgSqlParam(
                self.netflow_insert_sql_template.format(fields=self.neflow_fields, params=val),
                params=tuple(i)))
            new_netflow_alert_params.append(tuple(i))

        if netflow_uuid_list:
            if len(netflow_uuid_list) == 1:
                filter_condition = "uuid = '{}'".format(netflow_uuid_list[0])
            else:
                filter_condition = "uuid in {}".format(tuple(netflow_uuid_list))
            update_netflow_sql(filter_condition)

        mlog.info("生成netflow倍数告警数量：{}".format(len(new_netflow_alert_params)))

    def handle_tc_alert(self, dat, tl_avg_data, asset_ip, tllog_alert_params, tllog_ip_lst, unit_ip_map):
        out_avg = dat.out_avg
        out_multiple = dat.out_multiple
        in_avg = dat.in_avg
        in_multiple = dat.in_multiple
        uuid_ = str(uuid4())
        asset_info = tl_avg_data.get(asset_ip)
        if not asset_info:
            return tllog_alert_params, tllog_ip_lst
        start_time = asset_info.start_time
        end_time = asset_info.end_time
        in_bytes = asset_info.in_bytes
        out_bytes = asset_info.out_bytes
        asset_name = asset_info.asset_name
        if out_bytes <= in_bytes:
            # 只要流出大于流入的数据， 所以其他数据均 continue
            return tllog_alert_params, tllog_ip_lst
        if out_avg > self.tclog_threshold and out_multiple and in_avg > self.tclog_threshold and in_multiple and \
                out_bytes > out_avg * out_multiple and in_bytes > in_avg * in_multiple:
            name = u"重点监控IP流出流量均值为{out_avg},当前流出流量为{out_bytes}, 超过均值的{out_excess_multiple}倍; 流入流量均值为{in_avg},当前流入流量为{in_bytes}, 超过均值的{in_excess_multiple}倍。".format(
                out_avg=long2unit(out_avg), out_bytes=long2unit(out_bytes),
                out_excess_multiple=out_multiple, in_avg=long2unit(in_avg),
                in_bytes=long2unit(in_bytes),
                in_excess_multiple=in_multiple
            )
        elif out_avg > self.tclog_threshold and out_multiple and out_bytes > out_avg * out_multiple:
            name = u"重点监控IP流出流量均值为{out_avg},当前流出流量为{out_bytes}, 超过均值的{out_excess_multiple}倍。".format(
                out_avg=long2unit(out_avg), out_bytes=long2unit(out_bytes),
                out_excess_multiple=out_multiple
            )
        else:
            return tllog_alert_params, tllog_ip_lst
        # 判断IP是否启用了动态基线
        if not unit_ip_map.get(asset_ip):
            return tllog_alert_params, tllog_ip_lst
        if is_ipv4_correct(asset_ip):
            ipv6 = False
        else:
            ipv6 = True
        tllog_alert_params.append([uuid_, name, self.type, start_time, end_time, asset_ip,
                                   "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", -1,
                                   out_bytes, in_bytes, 0, 0, "", self.analysis_tech, ipv6, 3])
        tllog_ip_lst.append(net.ip_to_int32(asset_ip))

        return tllog_alert_params, tllog_ip_lst

    def handle_netflow_alert(self, dat, netflow_avg_data, asset_ip, netflow_alert_params, netflow_ip_lst, unit_ip_map):
        out_avg = dat.out_avg
        out_multiple = dat.out_multiple
        in_avg = dat.in_avg
        in_multiple = dat.in_multiple
        uuid_ = str(uuid4())
        asset_info = netflow_avg_data.get(asset_ip)
        if not asset_info:
            return netflow_alert_params, netflow_ip_lst
        in_bytes = asset_info.in_bytes
        out_bytes = asset_info.out_bytes
        start_time = asset_info.start_time
        end_time = asset_info.end_time
        asset_name = asset_info.asset_name
        if out_bytes <= in_bytes:
            # 只要流出大于流入的数据， 所以其他数据均 continue
            return netflow_alert_params, netflow_ip_lst
        if out_avg > self.netflow_threshold and out_multiple and in_avg > self.netflow_threshold and in_multiple and \
                out_bytes > out_avg * out_multiple and in_bytes > in_avg * in_multiple:
            name = u"重点监控IP流出流量均值为{out_avg},当前流出流量为{out_bytes}, 超过均值的{out_excess_multiple}倍; 流入流量均值为{in_avg},当前流入流量为{in_bytes}, 超过均值的{in_excess_multiple}倍。".format(
                out_avg=long2unit(out_avg), out_bytes=long2unit(out_bytes),
                out_excess_multiple=out_multiple, in_avg=long2unit(in_avg),
                in_bytes=long2unit(in_bytes),
                in_excess_multiple=in_multiple
            )
        elif out_avg > self.netflow_threshold and out_multiple and out_bytes > out_avg * out_multiple:
            name = u"重点监控IP流出流量均值为{out_avg},当前流出流量为{out_bytes}, 超过均值的{out_excess_multiple}倍。".format(
                out_avg=long2unit(out_avg), out_bytes=long2unit(out_bytes),
                out_excess_multiple=out_multiple
            )
        else:
            return netflow_alert_params, netflow_ip_lst
        # 判断IP是否启用了动态基线
        if not unit_ip_map.get(asset_ip):
            return netflow_alert_params, netflow_ip_lst
        if is_ipv4_correct(asset_ip):
            ipv6 = False
        else:
            ipv6 = True
        netflow_alert_params.append([uuid_, name, self.type, start_time, end_time, asset_ip,
                                     "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", -1,
                                     out_bytes, in_bytes, 0, 0, self.analysis_tech, ipv6, 3])
        netflow_ip_lst.append(net.ip_to_int32(asset_ip))
        return netflow_alert_params, netflow_ip_lst

    def run(self):
        try:
            tc_status = get_dynamic_baseline_status(self.type, 2)
            netflow_status = get_dynamic_baseline_status(self.type, 1)
            if tc_status or netflow_status:
                # 有一个启用就执行  get_avg_multiple方法控制查询到的IP
                avg_multiple, peer_avg_multiple, tl_ip_list, netflow_ip_list = self.get_avg_multiple(tc_status,
                                                                                                     netflow_status)
                # 查数据库控制任务是否要执行
                tl_avg_data = netflow_avg_data = {}
                if tc_status:
                    mlog.info("通联IP倍数动态基线已启用")
                    tl_avg_data = self.get_last_day_flow(tl_ip_list, self.tllog_flow_type)
                else:
                    tl_avg_data = {}
                    mlog.info("没有启用的通联IP倍数动态基线")
                if netflow_status:
                    mlog.info("NetFlow IP倍数动态基线已启用")
                    netflow_avg_data = self.get_last_day_flow(netflow_ip_list, self.netflow_flow_type)
                else:
                    netflow_avg_data = {}
                    mlog.info("没有启用的netflowIP倍数动态基线")
                # 生成告警
                self.generate_alert(avg_multiple, tl_avg_data, netflow_avg_data)
                # 更新流量均值
                update_avg_flow()
                # 添加新增的通联境内IP到告警规则表
                run()
            else:
                mlog.info("没有启用的IP倍数动态基线")
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())


def get_detect_rule(belong, detect_name):
    try:
        threshold = 0
        query_sql = "SELECT rule_id, threshold FROM internal_app_bsa_gjk.detect_rule WHERE belong=%s and detect_name=%s"
        result = json.loads(CFunction.execute(CPgSqlParam(query_sql, params=(belong, detect_name))))
        if result:
            threshold = result[0][1]
        return threshold
    except Exception as _:
        mlog.error(traceback.format_exc())
        return 0


def filter_key_unit_ips():
    """
    查询启用了动态基线的IP和单位
    :return:
    """
    try:
        key_unit_sql = '''
            SELECT
                id,
                ip_range,
                tclog_dynamic_baseline
            FROM
                internal_app_bsa_gjk.key_unit WHERE tclog_dynamic_baseline = true;
        '''
        key_unit_param = CPgSqlParam(key_unit_sql)
        key_unit_res_list = json.loads(CFunction.execute(key_unit_param))
        data = {}
        for item in key_unit_res_list:
            key_unit_id = item[0]
            ip_range_list = [i.strip() for i in item[1].split(',') if i]
            ip_list = net.ips_to_list(ip_range_list)
            for i in ip_list:
                data[i] = key_unit_id
        return data

    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
    return {}


def get_key_unit():
    """
    查询境 内目标重点单位以及其IP
    :return:
    """
    query_sql = """
        SELECT  name, ip_range FROM internal_app_bsa_gjk.key_unit WHERE boundary = 2
    """
    result = json.loads(CFunction.execute(CPgSqlParam(query_sql)))
    ip_list = []
    for item in result:
        asset_name = item[0]
        ip_range = item[1]
        try:
            if not ip_range:
                continue
            asset_ips = net.ips_to_list(ip_range.split(","))
            ip_list.extend([i.strip() for i in asset_ips])
        except Exception as _:
            mlog.error("存在错误ip，无法进行转换, asset_name: {} , ip_range: {}".format(asset_name, ip_range))
            mlog.error(traceback.format_exc())
            continue
    return list(set(ip_list))


def run():
    """
    通过对比规则表ip和通联境内单位ip，找到需要新增的 ip
    :return:
    """
    try:
        alert_rule = AlertRuleMode()
        # 倍数
        multiple_ = MultipleAlertModel().multiple
        template_name = "重点监控IP, 流出突增{multiple}倍, 流入突增{multiple}倍".format(multiple=multiple_)
        flow_type = 1  # 1 : tclog    2 : NetFlow
        ip_list = get_key_unit()
        select_alert_ip = """SELECT rule_id, asset_ip as asset_ip FROM internal_app_bsa_gjk.alert_rule where is_delete = 0;"""
        res = json.loads(CFunction.execute(CPgSqlParam(select_alert_ip)))

        alert_ip_list = [item[1] for item in res]
        add_list = list(set(ip_list) - set(alert_ip_list))

        mlog.info("新增：{}".format(len(add_list)))
        for asset_ip in add_list:
            if asset_ip:
                mlog.info("正在添加规则， IP：{}".format(asset_ip))
                alert_rule.add_rule(template_name=template_name, flow_type=flow_type, asset_ip=asset_ip, peer_ip='',
                                    out_surge_multiple=multiple_, in_surge_multiple=multiple_)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())


if __name__ == '__main__':
    model = MultipleAlertModel()
    model.run()
    mlog.info("开始增强资产信息")
    analysis_tech = 2
    today = datetime.date.today()
    stime = int(time.mktime(today.timetuple())) - 86400
    etime = int(time.mktime(today.timetuple()))
    GetAssetInfo(analysis_tech=analysis_tech, start_time=stime, end_time=etime,
                 event_type=[51]).enhance_event_asset_info()
    mlog.info("开始增强恶意标签信息")
    EventMaliciousTag().run(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[51])
    mlog.info("开始增强情报信息")
    enhance_event_intelligence_info(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[51])
