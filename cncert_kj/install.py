#!/usr/bin/env python
# -*-coding:utf-8 -*-

import logging
import os
import shutil
import sys
import time
import traceback
from distutils import dir_util

from appManager.module.global_api import ComponentHelper
from appsUtils.confutil import ConfUtil

from cncert_kj.bin.scripts.menu_setting import update_menu
from cncert_kj.conf.constant import PATH_ISOP, PATH_ENV

reload(sys)
sys.setdefaultencoding('utf8')

BASE_DIR = os.environ["BASE_DIR"]
APP_NAME = 'cncert_kj'

PATH_LOG = BASE_DIR + '/logs/' + APP_NAME + '_install.log'

CUR_PATH = os.path.normpath(os.path.dirname(os.path.abspath(__file__)))

# /tmp/tmpeN6KY7    上传的app包，会临时解压到此，install会被提取至外层目录
PATH_TMP = os.path.normpath(os.path.dirname(__file__))
# /tmp/tmpeN6KY7/peafowls
PATH_TMP_APP = os.path.join(PATH_TMP, APP_NAME)
# /home/<USER>/
BASE_DIR = os.environ["BASE_DIR"]

# APP升级时，备份原app的路径  /home/<USER>/back_app
PATH_BACK_APP = os.path.join(BASE_DIR, "back_app")

# /home/<USER>/ISOP/apps/peafowls
PATH_BSA_APP = sys.argv[1]

# /home/<USER>/ISOP/static/     # TODO 有待验证，是否只需要校验app目录
PATH_BSA_STATIC = os.path.join(BASE_DIR, 'ISOP/static')

# 0-安装，1-升级 3-执行方法
ISUPDATE = sys.argv[2]


def get_logger_install(logger_name, logger_level=logging.INFO):
    """安装/升级 日志"""
    import logging.handlers
    LOGGER = logging.getLogger(logger_name)
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(filename)s %(funcName)s:%(lineno)d %(message)s')
    LOGGER.setLevel(logger_level)
    logger_file = os.path.join(PATH_ISOP, "logs")
    if not os.path.exists(logger_file):
        os.mkdir(logger_file)
    if not LOGGER.handlers:
        file_handler = logging.handlers.TimedRotatingFileHandler(logger_name, 'midnight')
        file_handler.suffix = "%Y-%m-%d"
        file_handler.setLevel(logger_level)
        file_handler.setFormatter(formatter)
        LOGGER.addHandler(file_handler)
    return LOGGER


LOGGER = get_logger_install(PATH_LOG)


# 计算时间函数
def run_time(func):
    def wrapper(*args, **kw):
        local_time = time.time()
        func(*args, **kw)
        LOGGER.info("函数名称：{}，运行时间：{}".format(func.__name__, time.time() - local_time))

    return wrapper


@run_time
def check_file_without_root(*dir_path_list):
    """
        检查目录中是否有root权限的文件
    """
    for dir_path in dir_path_list:
        if not os.path.exists(dir_path):
            continue
        cmd = "find %s -user root" % dir_path
        cmd_res = os.popen(cmd)
        res = cmd_res.read().strip()
        if res:
            msg = "发现路径 %s 中有root用户的文件：\n%s！" % (dir_path, res)
            LOGGER.error(msg)
            raise ValueError(msg)
        LOGGER.info("目录%s检查通过" % dir_path)
    LOGGER.info("检查root权限的文件完成，无异常")


def copy_settings():
    """settings替换为新的"""
    shutil.copyfile(os.path.join(PATH_BSA_APP, "settings.py"), os.path.join(BASE_DIR, "ISOP/settings.py"))
    LOGGER.info("替换配置文件成功")


class UpdateNewVersion(object):
    def __init__(self):
        # 新升级包临时解压目录
        self.path_tmp_app = PATH_TMP_APP
        # bsa的app目录
        self.path_bsa_app = PATH_BSA_APP
        # 原app的备份目录
        self.path_back = PATH_BACK_APP
        # 本地升级的备份目录
        self.path_backup_file = self.get_back_path()
        # 新的、临时的，前端静态图片路径（bsa安装逻辑在本install成功执行后，自动替换app的前端static目录，所以需要将旧的图片们先放入tmp）
        self.path_bsa_web_image = os.path.join(BASE_DIR, 'ISOP/static', APP_NAME, 'static/files/image')
        # app后端存储图片的路径
        self.path_bsa_app_image = os.path.join(self.path_bsa_app, "static/files/image")
        # 备份文件中，后端存储图片的路径
        self.path_back_app_image = os.path.join(self.path_backup_file, "static/files/image")

    def get_back_path(self):
        LOGGER.info("原APP的备份路径：%s" % self.path_back)
        if not os.path.exists(self.path_back):
            os.mkdir(self.path_back)
        backup_filename = '%s.%s.%d' % (APP_NAME, time.strftime("%Y%m%d", time.localtime()), int(time.time()))
        backup_file_path = os.path.join(self.path_back, backup_filename)
        return backup_file_path

    def roll_back(self):
        try:
            shutil.rmtree(self.path_bsa_app, ignore_errors=True)
            shutil.copytree(self.path_backup_file, self.path_bsa_app)
            LOGGER.info("回滚成功")
        except Exception:
            LOGGER.error("回滚失败")
            LOGGER.error(traceback.format_exc())

    def get_back(self, *dir_or_files):
        for dir_or_file in dir_or_files:
            LOGGER.info(">>复制以前的%s>>" % dir_or_file)
            bak_file = os.path.join(self.path_backup_file, dir_or_file)
            bsa_file = os.path.join(self.path_bsa_app, dir_or_file)
            if not os.path.exists(bak_file):
                LOGGER.info("原app无此文件，跳过，%s" % bak_file)
                continue

            if os.path.isfile(bak_file):
                shutil.copyfile(bak_file, bsa_file)

            elif os.path.isdir(bsa_file) and os.path.exists(bsa_file):
                self.copy_tree_force(bak_file, bsa_file)
        LOGGER.info("所有指定的文件都恢复成功！")

    def copy_tree_force(self, src, dst):
        """
            暴力copy目录，会先删除目标路径，再将源路径完整的copy到目标路径，慎用！
        """
        LOGGER.info("删除目录：%s" % dst)
        if os.path.exists(dst):
            shutil.rmtree(dst)
        LOGGER.info("开始复制 %s 到 %s" % (src, dst))
        shutil.copytree(src, dst)
        LOGGER.info("更新 %s 成功" % dst)

    def copy_png(self):
        # 复制图片
        if not os.path.exists(self.path_back_app_image):
            LOGGER.info("app的老版本中无static/files/image文件夹，无需复制，%s" % self.path_back_app_image)
            return

        # 复制之前的图片目录到后端路径中，/home/<USER>/ISOP/apps/peafowls/static/files/image
        self.copy_tree_force(self.path_back_app_image, self.path_bsa_app_image)

        # 复制之前的图片目录到前端路径中，/home/<USER>/ISOP/static/peafowls/static/files/image
        self.copy_tree_force(self.path_back_app_image, self.path_bsa_web_image)

    def update(self):
        """
        更新
        """
        try:
            LOGGER.info("1、开始备份当前版本的APP至：%s" % self.path_backup_file)
            shutil.copytree(self.path_bsa_app, self.path_backup_file)

            LOGGER.info("2、开始更新app源码")
            self.copy_tree_force(self.path_tmp_app, self.path_bsa_app)

            LOGGER.info("3、开始复制之前页面导入的图片")
            self.copy_png()

            LOGGER.info("4、开始恢复原app中需要的文件")
            self.get_back("logs")
            self.get_back("script/event_recommend/output")
        except Exception as e:
            LOGGER.exception("遇到错误，开始回滚-----")
            self.roll_back()
            LOGGER.error("本次更新失败!!!")
            raise e


class Install:
    """
    BSA组件安装
    """

    def __init__(self):
        #
        pass

    def install(self):
        try:
            # self.init_db_schema(["pg_struct.sql", "pg_data.sql"])
            self.remove_task(APP_NAME)
            self.add_task(APP_NAME)
            self.start_task(APP_NAME)
            copy_settings()
            self.mk_file("logfile.log")
        except Exception:
            LOGGER.error("install failure! reason: %s" % traceback.format_exc())
            self.install_rollback()
            # 此处写自定义的状态码，如100
            code = 100
            sys.exit(code)

    def mk_file(self, path):
        file_path = os.path.join(PATH_ENV, path)
        f = open(file_path, "w")
        f.close()
        f2 = open("/tmp/check_task.lockfile", "w")
        f2.close()

    def add_task(self, app_name=None):
        # 组件批量注册任务
        ch = ComponentHelper(app_name)
        ch_ret = ch.add_task()
        if ch_ret['status'] != 200:
            msg = '定时任务注册失败, 失败原因： %s' % ch_ret['msg']
            LOGGER.error(msg)
            raise ValueError(msg)
        else:
            LOGGER.info('定时任务注册成功')

    def remove_task(self, app_name=None):
        """
        组件批量注销任务
        """
        ch = ComponentHelper(app_name)
        ch.remove_task()
        LOGGER.info('定时任务注销成功')

    def start_task(self, app_name=None):
        """
        组件批量启动任务
        """
        ch = ComponentHelper(app_name)
        ch.start_task()
        LOGGER.info('定时任务启动成功')

    def replace_folder(self, bak_path, dpath):
        """
        替换部分需要保留的目录，比如配置文件等
        """
        # 注意如果报错,可能是因为没有upload文件夹
        dir_util.copy_tree(os.path.join(bak_path, 'upload'), os.path.join(dpath, 'upload'))

    def upgrade(self, dst_path):
        """
        升级
        :return:
        """
        try:
            LOGGER.info("\n" + "=" * 20 + "开始升级" + "=" * 20)
            LOGGER.info("DST_PATH:{}".format(dst_path))
            # 检查原app目录文件权限，不能有root权限的文件
            LOGGER.info("开始校验相关路径文件权限……")
            check_file_without_root(PATH_BSA_APP, PATH_BSA_STATIC)
            LOGGER.info("文件权限校验完毕")
            UpdateNewVersion().update()
            # >> 更新数据库
            # LOGGER.info("更新数据库")
            # self.init_db_schema(["pg_struct.sql"])
            # >> 重置定时任务
            LOGGER.info("重置定时任务")
            # >> 升级不注销任务  注销可能会影响相关任务运行
            # self.remove_task(APP_NAME)
            self.add_task(APP_NAME)
            self.start_task(APP_NAME)
            update_menu(APP_NAME)
            copy_settings()
            # self.init_db_schema(["pg_struct.sql", "pg_data.sql"])
            LOGGER.info(">>>升级成功！！！\n")
        except Exception as e:
            LOGGER.error("update failure! reason: %s" % traceback.format_exc())
            raise e

    def install_rollback(self):
        """
        安装回滚
        :return:
        """
        try:
            LOGGER.error("rollback app folder")
        except Exception:
            LOGGER.error("roll back failed")
            LOGGER.error(traceback.format_exc())

    def upgrade_rollback(self):
        """
        升级回滚
        :return:
        """
        try:
            LOGGER.error("rollback app folder")
        except Exception:
            LOGGER.error("roll back failed")
            LOGGER.error(traceback.format_exc())

    def init_db_schema(self, sql_file=None):
        """
        初始化数据库脚本
        :return:
        """
        try:
            sql_file_ = sql_file if sql_file else ["pg_struct.sql"]
            for sqlfile in sql_file_:
                sql_path = os.path.normpath(os.environ["ISOP_HOME"] + "/apps/" + APP_NAME + "/sql")
                sql_file_path = sql_path + "/" + sqlfile
                confutil = ConfUtil()
                cur_pg_conf = confutil.getPostgresqlConf()
                cmd = 'psql -U %s %s<"%s"' % (cur_pg_conf["username"],
                                              cur_pg_conf["database"], sql_file_path,
                                              )
                rtn = os.popen(cmd)
                cmd_rtn = rtn.readlines()
                LOGGER.error(str(cmd_rtn))
        except Exception, e:
            LOGGER.error(e)


def main():
    try:
        DST_PATH = sys.argv[1]
        flag = sys.argv[2]
        install_instance = Install()
        if flag == '0':
            LOGGER.info("=" * 20 + "开始安装" + "=" * 20)
            install_instance.install()
        if flag == '1':
            LOGGER.info("=" * 20 + "开始升级" + "=" * 20)
            install_instance.upgrade(DST_PATH)
        LOGGER.info("main 执行完成")
        sys.exit(0)
    except Exception as e:
        LOGGER.info("install failure, reason: %s" % traceback.format_exc())
        raise e


if __name__ == '__main__':
    main()
