#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
将前一天通联和netflow告警按照表格字段，生成csv文件，推送到对端ftp服务器上
"""
import argparse
import json
import tempfile
import os

from datetime import datetime, timedelta

from cncert_kj.views.tllog_alert import download_cvs_files as tllog_download_cvs_files
from cncert_kj.views.netflow_alert import download_cvs_files as netflow_download_cvs_files
from cncert_kj.utils.ftp_util import FTPClient, FtpClientConfig
from cncert_kj.utils import logger

mlog = logger.init_logger("alert_data_transfer")


class MockRequest:
    def __init__(self, get_params=None, method='GET', session_data=None, body_data=None):
        class MockGET(dict):
            def get(self, key, default=None, *args, **kwargs):
                return super(MockGET, self).get(key, default)

        class MockSession(dict):
            def __init__(self, session_data):
                super(MockSession, self).__init__(session_data or {"username": "admin"})

            def get(self, key, default=None, *args, **kwargs):
                return super(MockSession, self).get(key, default)

        self.GET = MockGET(get_params if get_params is not None else {})
        self.method = method
        self.session = MockSession(session_data)
        # 如果提供了body_data，则使用它，否则默认为空的JSON对象
        self.body = body_data if body_data is not None else "{}"

        # 添加其他可能需要的HTTP请求属性
        self.META = {}
        self.COOKIES = {}
        self.FILES = {}
        self.POST = {}

class FTPUtil(object):
    def __init__(self):
        self.ftp_number_config = {
            "host": "***********",
            "port": 21,
            "username": "jhsftp",
            "password": "jhsftp@123",
            "mode": "pasv",
            "buffer_size": 8196,
            "alive_check_interval": 5,
            "max_client": 2
        }
        self.ftp_config = FtpClientConfig(self.ftp_number_config['host'], self.ftp_number_config['port'],
                                     self.ftp_number_config['username'], self.ftp_number_config['password'],
                                     self.ftp_number_config['buffer_size'],
                                     self.ftp_number_config['mode'], self.ftp_number_config['alive_check_interval'])

        self.ftp_util = self.init_service()

    def init_service(self):
        return FTPClient(self.ftp_config)

    def upload_file(self, local_file_path, remote_path):
        """
        上传文件
        :param local_file_path: 本地文件路径
        :param remote_path: 远程FTP 文件路径
        :return:
        """
        try:
            self.ftp_util.upload_file(
                local_file=local_file_path,
                remote_file=remote_path
            )
        except Exception as e:
            mlog.exception("上传失败：{}".format(e))
            raise

    def get_file_list(self, remote_path):
        """
        获取远程目录下的文件列表
        """
        return self.ftp_util.get_file_set(remote_path)

    def delete_file(self, remote_path):
        """
        删除远程文件
        """
        try:
            self.ftp_util.delete_file(remote_path)
        except Exception as e:
            mlog.exception("删除失败：{}".format(e))
            raise

class AlertDateTransfer(object):
    def __init__(self):
        self.ftp_path = "/kj/"
        self.ftp_util = FTPUtil()

    def _get_alert_func(self, report_type):
        if report_type == 1:
            return tllog_download_cvs_files
        elif report_type == 2:
            return netflow_download_cvs_files

    def _get_st_and_et(self, date_str):
        if isinstance(date_str, str):
            st, et = date_str + " 00:00:00", date_str + " 23:59:59"
            return st, et
        else:
            raise ValueError("date_str must be a string: YYYY-MM-DD")

    def alert_file_is_exist(self, alert_name, remote_path):
        file_list = self.ftp_util.get_file_list(remote_path)
        mlog.info("远程目录下的文件列表: {}".format(file_list))
        current_date = datetime.now().strftime("%Y%m%d")
        name_ = "{}_{}".format(alert_name, current_date)
        for file_name in file_list:
            if file_name.startswith(name_):
                return True
        return False

    def run(self, report_type, date_):
        alert_type_name = "tclog" if report_type == 1 else "netflow"

        if self.alert_file_is_exist(alert_type_name, self.ftp_path):
            mlog.info("{}文件已存在".format(alert_type_name))
            return
        func_ = self._get_alert_func(report_type)
        st, et = self._get_st_and_et(date_)
        # >>创建一个模拟的请求对象
        # 根据你想要查询的时间范围设置参数
        # 这里假设st和et是已经定义的时间戳
        mock_request = MockRequest(get_params={
            'start_time': st,
            'end_time': et,
            'page': "0",
            'limit': "0"
        })

        # 调用相应的过滤函数
        response = func_(mock_request)
        # mlog.info(json.dumps(json.loads(response.content), ensure_ascii=False))

        # 将StreamingHttpResponse内容保存到临时文件
        temp_file = None
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
            temp_filename = temp_file.name
            mlog.info("临时文件保存路径: {}".format(temp_filename))

            # 从StreamingHttpResponse中读取数据并写入临时文件
            mlog.info("开始写入数据到临时文件: {}".format(temp_filename))
            data_size = 0
            if hasattr(response, 'streaming_content'):
                mlog.debug("使用streaming_content方式写入文件")
                for chunk in response.streaming_content:
                    temp_file.write(chunk)
                    data_size += len(chunk)
            else:
                mlog.debug("使用content方式写入文件")
                data_size = len(response.content)
                temp_file.write(response.content)

            temp_file.close()
            mlog.info("完成写入临时文件: {}，文件大小: {} 字节".format(temp_filename, data_size))

            # 生成远程FTP文件路径
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            # >> timestamp_str = date_.replace("-", "") + "_" + "000000"
            remote_filename = "{}_{}.csv".format(alert_type_name, timestamp_str)
            # >>
            remote_path = os.path.join(self.ftp_path, remote_filename)
            mlog.info("生成远程FTP文件路径: {}".format(remote_path))
            # 通过FTP上传文件
            mlog.info("开始上传文件到FTP服务器")
            self.ftp_util.upload_file(temp_filename, remote_path)

            #>> 先存在本地
            # current_path = os.path.join(os.getcwd(), remote_filename)
            # cmd = 'mv {} {}'.format(temp_filename, current_path)
            # os.system(cmd)

            mlog.info("成功上传文件到FTP: {}".format(remote_path))

        finally:
            # 删除临时文件
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="告警数据外发")
    parser.add_argument('-r', '--report_type', help="tllog/netflow: 1/2", choices=[1, 2], type=int)
    parser.add_argument('-d', '--date', help="推送某一天数据：YYYY-MM-DD")

    args = parser.parse_args()
    mlog.info("=========开始处理告警数据外发")
    report_type = args.report_type
    _date = args.date
    if not _date:
        _date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    mlog.info("%s告警--处理日期：%s" % ("tllog" if report_type == 1 else "netflow", _date))

    AlertDateTransfer().run(report_type, _date)
    mlog.info("=========处理告警数据外发完成")
