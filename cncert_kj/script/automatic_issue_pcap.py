#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
1、自动下发pcap
2、用于持续性事件的pcap规则自动下发
3、每5分钟跑一次
"""
import json
import sys
import os
import pytz
import time
import traceback

from datetime import datetime, timedelta
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.retrieval_task_model import RetrievalTaskModel
from cncert_kj.utils import logger
from cncert_kj.utils.time_trans import timestamp2format, DATE_TIME_FORMAT

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('automatic_issue_pcap')


class Lock(object):
    """
    自定义锁，防止一次任务执行过久，还未结束下次任务就开始了，防止重复执行
    """

    def __init__(self):
        self.LOCK_PATH = "/tmp/AUTOMATIC_ISSUE_PCAP.LOCK"

    def is_locked(self):
        return True if os.path.exists(self.LOCK_PATH) else False

    def get_lock(self):
        f = open(self.LOCK_PATH, "w")
        f.write("locked")
        f.flush()
        f.close()

    def release_lock(self):
        if self.is_locked():
            os.remove(self.LOCK_PATH)


class AutomaticIssuePCAP(object):
    """
    1、查询事件表 event_type 为71 的事件
    2、pcap 规则中的时间段为当前时间到未来一周的
    3、对于持续性事件，如果下发过就不再下发了
    4、第一次时，时间设置为当前时间
    """

    def __init__(self):
        self.conf_key = "automatic_issue_pcap_timestamp"
        self.time_format = DATE_TIME_FORMAT
        self.event_type = "71"
        self.days = 14
        self.retrieval_task = RetrievalTaskModel()

    def query_automatic_issue_pcap_timestamp(self):
        """
        获取上次处理的时间
        第一次执行时默认当前时间
        """
        sql = """
                SELECT conf_value FROM internal_app_bsa_gjk.continuous_events_conf WHERE conf_key='{conf_key}'
            """.format(conf_key=self.conf_key)
        result = CFunction.execute(CPgSqlParam(sql))
        result = json.loads(result)
        if not result:
            # 如果没有该数据则进行初始化
            insert_sql = """
                INSERT INTO internal_app_bsa_gjk.continuous_events_conf (conf_key, conf_value) VALUES ('{}', {})
            """.format(self.conf_key, int(time.time()))
            mlog.info("insert_sql:{}".format(insert_sql))
            CFunction.execute(CPgSqlParam(insert_sql))
            return int(time.time())
        value = result[0][0]
        return value

    def get_event(self, last_end_time):
        """
        获取本次需要更新的事件
        """
        sql = """
            SELECT event_id, src_ip, dst_ip, start_time, end_time, event_type 
            FROM internal_app_bsa_gjk.continuous_events
            WHERE end_time >= {end_time} and event_type IN ({event_type})
        """.format(end_time=last_end_time, event_type=self.event_type)
        result = json.loads(CFunction.execute(CPgSqlParam(sql)))
        mlog.info("最新生成的事件数量:{}".format(len(result)))
        return result

    def update_ddos_timestamp(self, end_time):
        conf_sql = '''
        UPDATE internal_app_bsa_gjk.continuous_events_conf 
        SET conf_value={end_time} WHERE conf_key='{conf_key}'
        '''.format(end_time=end_time, conf_key=self.conf_key)
        param = CPgSqlParam(conf_sql)
        CFunction.execute(param)
        mlog.info("更新记录时间为{} - {}".format(end_time, timestamp2format(end_time)))

    def get_event_id_by_retrieval_task(self):
        """
        获取下发过pcap的event_id
        """
        sql = """
            SELECT event_id FROM internal_app_bsa_gjk.retrieval_task;
        """
        result = json.loads(CFunction.execute(CPgSqlParam(sql)))
        result = [item[0] for item in result if item]
        return result

    def run(self):
        try:
            mlog.info("==========================开始自动下发pcap任务============================")
            last_end_time = max_end_time = self.query_automatic_issue_pcap_timestamp()
            mlog.info("查询脚本上次执行时间: automatic_issue_pcap_timestamp:{}  - {}".format(last_end_time,
                                                                                             timestamp2format(
                                                                                                 last_end_time)))

            mlog.info("获取新生成事件信息")
            event_list = self.get_event(last_end_time)
            event_ids = self.get_event_id_by_retrieval_task()
            num = 0
            for event_info in event_list:
                event_id = event_info[0]
                if event_id in event_ids:
                    continue
                src_ip = event_info[1]
                dst_ip = event_info[2]
                end_time = event_info[4]
                if end_time > max_end_time:
                    max_end_time = end_time
                # event_type = event_info[5]
                # 开始时间为事件结束时间   ，结束时间是事件结束时间往后推
                time_format = datetime.utcfromtimestamp(end_time)
                st = time_format.strftime(self.time_format)

                # 计算结束时间
                et = datetime.now(pytz.utc) + timedelta(days=self.days)
                et = et.strftime(self.time_format)
                mlog.info("开始时间:{}----结束时间:{}".format(st, et))

                task_data = {
                    "event_id": event_id,
                    "start_time": st,
                    "end_time": et,
                    "rules": {
                        "src_ip": src_ip,
                        "dst_ip": dst_ip
                    }
                }

                res = self.retrieval_task.create_task(task_data)
                if res == 10000:
                    mlog.info("event_id: {} 报文规则已存在，留存检索任务下发成功！".format(event_id))
                elif res:
                    mlog.info("event_id: {} 报文规则下发成功，请审核！".format(event_id))
                else:
                    mlog.info("报文规则下发失败:{}".format(task_data))
                num += 1
            mlog.info("本次共下发 {} 条pcap".format(num))
            # 更新时间
            self.update_ddos_timestamp(max_end_time)

        except Exception as e:
            mlog.error("下发pcap任务出错")
            mlog.error(e)
            mlog.error(traceback.format_exc())
        mlog.info("==========================下发pcap任务结束============================")


if __name__ == '__main__':
    """
    添加定时任务
    vi /home/<USER>/ISOP/bin/cron/crontabConf
    插入内容, 每5分钟跑一次
    * */5 * * * * python /home/<USER>/ISOP/apps/cncert_kj/script/automatic_issue_pcap.py
    """
    try:
        obj = AutomaticIssuePCAP()
        obj.run()
    except Exception as e:
        mlog.error("运行失败")
        mlog.error(traceback.format_exc())
