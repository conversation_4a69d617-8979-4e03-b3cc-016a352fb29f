#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: event_enrichment.py
# @time: 2025/7/9 14:08
# @desc:
"""
事件增强脚本；对一下模块进行增强
1.非常用端口应用类型增强

2.资产应用类型增强

3.威胁情报增强

4.恶意标签增强

5.备案单位（境外云标签）增强

6.ddos标签增强

"""

import json
import sys
import Queue
import time
import traceback
import threading

from cncert_kj.script.asset_event_add_app_type import AssetEventAppTypeEnhancer
from cncert_kj.script.check_ddos_event import check_ddos_event_func
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.script.uncommon_port_judge import UncommonPortJudge
from cncert_kj.script.update_cont_events_com import UpdateContEventsCom
from cncert_kj.script.cont_event_asset_type_tag_enhancement import ContinuousEventAssetTypeTagEnhancement
from cncert_kj.utils import logger, conf_util
from cncert_kj.utils.kafka_op import KafkaService, get_consumer

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('event_enrichment')


class EventEnrichment:

    def __init__(self):
        self.common_conf = conf_util.CommonConf().common_conf
        self.topic = self.common_conf.get("kafka_topic").get("cont_events_topic")
        self.timeout_ms = 3000
        self.kafka_service = KafkaService()
        self.group_id = "event_enrichment_group"
        self.client_id = "event_enrichment_client"
        self.sub_topic = self.common_conf.get("kafka_topic").get("cont_events_sub_topic")

    def get_consumer_from_kafka(self, group_id, client_id):
        """
            订阅kafka
        """
        if not self.kafka_service.kafka_sender.is_exists(self.topic):
            mlog.warning("topic:{},不存在".format(self.topic))
            return None

        consumer = get_consumer(topic=self.topic, group_id=group_id, client_id=client_id)
        return consumer
        # 获取新增的事件

    def get_event_from_kafka(self, consumer):
        """
            通过kafka获取事件
        """
        s = time.time()
        if not consumer:
            mlog.warning("consumer不存在")
            return []

        queue = []
        msg_pack = consumer.poll(timeout_ms=self.timeout_ms)  # 1秒超时

        count = 1

        for tp, messages in msg_pack.items():
            for message in messages:
                mlog.debug("第{}次获取消息".format(count))
                topic = message.topic
                partition = message.partition
                offset = message.offset
                try:
                    info = {
                        "Topic": topic,
                        "Partition": partition,
                        "Offset": offset,
                        "Key": message.key,
                        "Value": json.loads(message.value.decode("utf-8"))
                    }
                    queue.append(info)
                    count += 1
                except Exception:
                    mlog.exception("获取消息失败")
        mlog.debug("获取kafka数据耗时：{}s".format(time.time() - s))
        return queue

    def parse_consumer_data(self, consumer_data):
        """解析消费者数据，分获取新增和更新事件ID"""
        event_ids = set()

        for item in consumer_data:
            data = item.get("Value", {})
            events = data.get("events", [])

            if not events:
                continue
            event_ids.update([i["event_id"] for i in events])

        return event_ids

    def save_data_to_kafka(self, consumer_data):
        for item in consumer_data:
            content = item.get("Value", {})
            events = content.get("events", [])
            if not events:
                continue
            self.kafka_service.send_kafka(self.sub_topic, json.dumps(content, ensure_ascii=False))

    def update_cont_events_com_run(self, report_type, event_ids):
        # 备案单位增强调用方式

        obj = UpdateContEventsCom()
        obj.type = report_type
        obj.init_params()
        obj.run(event_ids=event_ids)

    def uncommon_port_judge_run(self, report_type, event_ids):
        # 非常用端口应用类型增强脚本调用方式
        if report_type == 1:
            conf_key = "tllog_uncommon_port_judge_timestamp"
        else:
            conf_key = "netflow_uncommon_port_judge_timestamp"

        UncommonPortJudge(record_key=conf_key).run(report_type=report_type, event_ids=event_ids)

    def event_malicious_tag_run(self, report_type, event_ids):
        # 恶意标签增强脚本调用方式
        EventMaliciousTag().run(report_type=report_type, event_ids=event_ids)

    def check_ddos_event_run(self, report_type, event_ids):
        mlog.info("check_ddos_event_run, report_type:{}".format(report_type))
        # ddos标签增强脚本调用方式
        check_ddos_event_func(event_ids)

    def enhance_event_intelligence_info_run(self, report_type, event_ids):
        if report_type == 1:
            conf_key = "tclog_threat_nti_timestamp"
        else:
            conf_key = "threat_nti_timestamp"

        enhance_event_intelligence_info(report_type=report_type, conf_key=conf_key, event_ids=event_ids)

    def asset_event_app_type_enhancer(self, report_type, event_ids):
        # 资产应用类型增强脚本调用方式
        AssetEventAppTypeEnhancer(report_type=report_type, event_ids=event_ids).run()

    def cont_event_asset_type_tag_enhancement_run(self, report_type, event_ids):
        mlog.info("cont_event_asset_type_tag_enhancement_run, report_type:{}".format(report_type))

        ContinuousEventAssetTypeTagEnhancement().run(event_ids)


def run_task(task_func, report_type, event_ids, result_queue, semaphore):
    with semaphore:
        func_name = task_func.__name__
        mlog.info("{} 开始执行".format(func_name))
        try:
            if func_name == "cont_event_asset_type_tag_enhancement_run":
                task_func(event_ids)
            else:
                task_func(report_type, event_ids)
            result_queue.put(("{}(type={})".format(func_name, report_type), "success", None))
        except Exception as e:
            mlog.error("{} 执行失败：{}".format(func_name, e))
            mlog.error(traceback.format_exc())
            result_queue.put(("{}(type={})".format(func_name, report_type), "failed", str(e)))


def execute_tasks(kafka_event_ids, obj):
    """
    并发执行所有增强任务
    """

    # 多类型任务列表（需双类型执行）
    dual_type_tasks = [
        obj.update_cont_events_com_run,
        obj.uncommon_port_judge_run,
        obj.event_malicious_tag_run,
        obj.enhance_event_intelligence_info_run,
        obj.asset_event_app_type_enhancer,
    ]

    # 单次任务（只执行一次）
    single_run_tasks = [
        obj.check_ddos_event_run,
        obj.cont_event_asset_type_tag_enhancement_run,
    ]
    result_queue = Queue.Queue()
    threads = []
    # 限制最大并发线程数为 6
    semaphore = threading.Semaphore(6)
    # 启动双类型任务线程
    for task_func in dual_type_tasks:
        for report_type in [1, 2]:
            t = threading.Thread(target=run_task,
                                 args=(task_func, report_type, kafka_event_ids, result_queue, semaphore))
            t.start()
            threads.append(t)

    # 启动单次任务线程（不区分 report_type）
    for task_func in single_run_tasks:
        t = threading.Thread(target=run_task, args=(task_func, None, kafka_event_ids, result_queue, semaphore))
        t.start()
        threads.append(t)

    for t in threads:
        t.join()
    return result_queue


def main():
    obj = EventEnrichment()
    consumer = obj.get_consumer_from_kafka(obj.group_id, obj.client_id)

    if not consumer:
        mlog.error("获取kafka消费者失败")
        return
    while True:
        try:
            # 获取本次消费的和该类型的历史所有事件id
            mlog.info("开始获取kafka数据")
            consumer_data = obj.get_event_from_kafka(consumer)
            kafka_event_ids = obj.parse_consumer_data(consumer_data)
            if not kafka_event_ids:
                mlog.info("本次没有需要处理的事件")
                time.sleep(10)
                consumer.commit()
                continue
            try:
                mlog.info("准备提交kafka偏移量")
                consumer.commit()
                mlog.info("kafka偏移量已提交")
            except Exception as e:
                mlog.error("kafka提交失败: {}".format(e))

            mlog.info("本次处理的事件id数量为：%s" % len(kafka_event_ids))

            mlog.info("----------------------开始并行处理事件增强-------------------------------")
            s = time.time()
            result_queue = execute_tasks(kafka_event_ids, obj)

            # 打印所有任务执行结果
            while not result_queue.empty():
                name, status, err = result_queue.get()
                mlog.info("任务 {name} 执行结果: {status}".format(name=name, status=status))
                if err:
                    mlog.info("任务 {name} 错误信息: {err}".format(name=name, err=err))
            mlog.info("本次处理用时：%s" % (time.time() - s))

            mlog.info("----------------------保存数据到kafka-------------------------------")
            try:
                obj.save_data_to_kafka(consumer_data)
                mlog.info("保存数据完成")
            except Exception as e:
                mlog.error("保存数据失败: {}".format(e))
        except Exception as e:
            mlog.error("处理异常：{}".format(e))
            mlog.error(traceback.format_exc())
            time.sleep(10)


if __name__ == '__main__':
    main()
