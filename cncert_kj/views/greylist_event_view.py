#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import datetime
import hashlib
import json
import os
import shutil
import time
import traceback
from collections import defaultdict
from urllib import quote

from audit.audit import Audit
from django.db import transaction
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from rest_framework.decorators import list_route
from rest_framework.viewsets import ViewSet

from cncert_kj.lib import openpyxl
from cncert_kj.lib.openpyxl.writer.excel import save_virtual_workbook
from cncert_kj.models.base_model import GreylistEventsModel, GreylistExceptionType, ContinuousEvents, \
    ContinuousEventsTagModel, DetectRuleModel, ReportedEventsModel, WhitelistEventsModel, WhitelistExceptionType
from cncert_kj.models.base_paginator import Pagination
from cncert_kj.models.base_serializers import GreylistEventsSerializer, WhitelistEventsSerializer
from cncert_kj.models.event_manage_model import GREYLIST_EVENTS_FILES_PATH, EventManageModel
from cncert_kj.script.update_cont_event import JudgeToReportedEventUtil
from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import CommonConf
from cncert_kj.utils.time_trans import format3timestamp, timestamp2day, timestamp2format, DATE_FORMAT
from cncert_kj.views.cont_event import APPENDIX_LOCAL_PATH
from cncert_kj.views.reported_events_view import REPORTED_EVENTS_FILES_PATH, ReportedEventsCheck

mlog = logger.init_logger('greylist_event_view')
auditLog = Audit()

EVENT_ID_IS_NOT_NONE = "事件ID不能为空"
EVENT_DOES_NOT_EXIST = "事件不存在"
REQUEST_ERROR = "接口请求失败"
DELETE_GREY_LIST_EVENT_SUCCESS = "删除灰名单事件成功"
SIP_AND_DIP_IS_NOT_NONE = "境内IP和境外IP不能为空！"


class GreylistEventsView(ViewSet):
    """
    灰名单事件管理
    """

    def save_file(self, file_list, instance_id):
        for file in file_list:
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(), hashlib.md5(str(instance_id)).hexdigest())
            # 写入文件
            file_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, file_name)
            mlog.info("文件保存路径：{}".format(file_path))
            with open(file_path, 'wb') as f:
                for line in file.chunks():
                    f.write(line)

    @list_route(methods=["GET"])
    def events_list(self, request):
        mlog.info("灰名单事件列表")
        try:
            filter_params = {}
            src_ip = request.query_params.get('src_ip', None)
            dst_ip = request.query_params.get('dst_ip', None)
            src_com = request.query_params.get('src_com', None)
            src_unit = request.query_params.get("src_unit", "")
            cause = request.query_params.get('cause', None)
            event_source = request.query_params.get('event_source', None)
            start_time = request.query_params.get("start_time", "")
            end_time = request.query_params.get("end_time", "")
            exception_type = request.query_params.get("exception_type", "")
            judge_user = request.query_params.get("judge_user", "")

            # 过滤条件
            if src_ip:
                filter_params['src_ip__contains'] = [src_ip]
            if dst_ip:
                filter_params['dst_ip__contains'] = [dst_ip]
            if src_com:
                filter_params['src_com__contains'] = src_com
            if cause:
                filter_params['cause'] = cause
            if event_source:
                filter_params['event_source__contains'] = event_source
            if src_unit:
                filter_params['src_unit__contains'] = src_unit
            if judge_user:
                filter_params['judge_user__contains'] = judge_user
            if exception_type:
                filter_params['exception_type'] = exception_type

            # 时间范围过滤
            if start_time:
                filter_params['event_time__gte'] = int(format3timestamp(start_time))  # 转换为整数

            if end_time:
                filter_params['event_time__lte'] = int(format3timestamp(end_time))  # 转换为整数

            # 查询数据并分页
            queryset = GreylistEventsModel.objects.filter(**filter_params).order_by("-event_time", "-id")
            paginator = Pagination()
            paginated_queryset = paginator.paginate_queryset(queryset, request)

            # 序列化数据
            serializer = GreylistEventsSerializer(paginated_queryset, many=True)

            exception_type = GreylistExceptionType.objects.all()

            exception_type_data = {i.id: i.exception_type for i in exception_type}

            data = []
            for item in serializer.data:
                item["exception_type"] = exception_type_data.get(item["exception_type"], "")
                item['src_ip'] = ','.join(item["src_ip"])
                item['dst_ip'] = ','.join(item["dst_ip"])
                data.append(item)

            # 返回分页数据
            return JsonResponse({"data": data, "total": queryset.count(), "msg": "成功", "code": 200})
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "查询失败", "code": 500})

    @list_route(methods=['POST'])
    def create_event(self, request):
        """创建灰名单事件"""
        try:
            with transaction.atomic():
                mlog.info("创建灰名单事件")
                username = request.session['username']
                # 持续性事件原有的附件
                file_list = request.POST.getlist("file_list")
                mlog.info("file_list:{}".format(file_list))

                files = request.FILES.getlist("files")
                file_name_list = [file.name for file in files]
                if len(file_name_list) != len(set(file_name_list)):
                    return JsonResponse({"msg": "文件重复！", "code": 400})

                data = request.data
                event_id = data.get("event_id")
                new_data = dict()
                src_ip = data.get("src_ip")
                dst_ip = data.get("dst_ip")
                src_ip = src_ip.split(",") if src_ip else []
                dst_ip = dst_ip.split(",") if dst_ip else []
                dst_port = str(data.get('dst_port'))
                src_port = str(data.get('src_port'))
                dst_port = dst_port if dst_port else None
                src_port = src_port if src_port else None
                new_data["create_time"] = int(time.time())
                new_data["update_time"] = int(time.time())
                new_data["judge_user"] = username
                new_data["files"] = ",".join(file_name_list + file_list)
                exception_type = data.get("exception_type")
                new_data["event_time"] = format3timestamp(data.get("event_time"))
                new_data["src_ip"] = list(set(src_ip))
                new_data["dst_ip"] = list(set(dst_ip))
                new_data["exception_type"] = exception_type
                new_data["src_unit"] = data.get("src_unit")
                new_data["src_com"] = data.get("src_com")
                new_data["service_type"] = data.get("service_type")
                new_data["cause"] = data.get("cause")
                new_data["notes"] = data.get("notes")
                new_data["event_source"] = data.get("event_source")
                new_data['dst_port'] = dst_port if dst_port else None
                new_data['src_port'] = src_port if src_port else None
                # 处理端口， 用于查询持续性事件
                c_dst_port = ',' + str(dst_port) + "," if dst_port else None
                c_src_port = ',' + str(src_port) + "," if src_port else None

                up_bytes = data.get("up_bytes", 0)
                up_bytes_unit = data.get("up_bytes_unit", "MB")
                if up_bytes != "" and up_bytes is not None:
                    up_bytes = int(self._convert_to_bytes(float(up_bytes), up_bytes_unit))
                else:
                    up_bytes = 0
                new_data["up_bytes"] = up_bytes

                if not src_ip and not dst_ip:
                    return JsonResponse({"msg": SIP_AND_DIP_IS_NOT_NONE, "code": 400})
                if not all([src_ip, src_port]) and not all([dst_ip, dst_port]):
                    return JsonResponse({"msg": "境内外IP、端口必填一组 ！", "code": 400})
                s = GreylistEventsModel.objects.filter(
                    src_ip__overlap=src_ip,
                    dst_ip__overlap=dst_ip,
                    src_port=src_port,
                    dst_port=dst_port,
                    exception_type=exception_type
                )
                # 检查唯一性
                if s.exists():
                    return JsonResponse({"msg": "该异常类型，已存在该源目IP+端口白名单事件！", "code": 400})
                event = GreylistEventsModel.objects.create(**new_data)
                auditLog(request, "新建灰名单事件 成功")
                # 本地目录是否存在，不存在则创建
                if not os.path.exists(GREYLIST_EVENTS_FILES_PATH):
                    os.mkdir(GREYLIST_EVENTS_FILES_PATH)
                self.handle_continuous(event_id, event, file_list, files, file_name_list)
                self.exist_exception(event, exception_type, data, src_ip, src_port, dst_ip, dst_port, c_src_port,
                                     c_dst_port)
                return JsonResponse({"msg": "事件创建成功", "code": 200})
        except Exception:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "创建失败", "code": 500})

    def handle_continuous(self, event_id, event, file_list, files, file_name_list):
        if event_id:
            # 从持续性事件转的才需要进行下面步骤
            event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
            if not event_obj:
                return JsonResponse({"msg": "未找到对应的持续性事件", "code": 400}, status=200)

            # 处理附件
            try:
                if file_list:
                    self.handle_file_list(file_list, event_id, event)

                if files:
                    # 文件写入本地
                    self.save_file(files, event.id)
                    # 更新持续性事件的附件列表
                    self.handle_files(files, event_obj)

                # 更新灰名单事件的文件列表
                event.files = ",".join(file_name_list + file_list)
                event.save()

            except Exception as e:
                mlog.exception("复制附件失败：{}".format(e))
                return JsonResponse({"msg": "创建成功，但附件复制失败", "code": 200}, status=200)

    def handle_file_list(self, file_list, event_id, event):
        for file_name in file_list:
            # 构建源文件路径
            src_file_name = "{}.{}".format(
                hashlib.md5(file_name).hexdigest(),
                hashlib.md5(str(event_id)).hexdigest()
            )
            src_path = os.path.join(APPENDIX_LOCAL_PATH, src_file_name)

            # 构建目标文件路径
            dst_file_name = "{}.{}".format(
                hashlib.md5(file_name).hexdigest(),
                hashlib.md5(str(event.id)).hexdigest()
            )
            dst_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, dst_file_name)
            mlog.info("灰名单事件文件保存路径：{}".format(dst_path))
            # 确保目标目录存在
            if not os.path.exists(GREYLIST_EVENTS_FILES_PATH):
                os.makedirs(GREYLIST_EVENTS_FILES_PATH)

            # 复制文件
            if os.path.exists(src_path):
                shutil.copy(src_path, dst_path)
            else:
                mlog.error("持续性事件文件不存在：{}".format(src_path))

    def handle_files(self, files, event_obj):
        judge_file = event_obj.judge_file.split(",") if event_obj.judge_file else []
        new_file_name_list = []
        for file in files:
            if file.name in judge_file:  # 去重
                continue
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(),
                                       hashlib.md5(str(event_obj.event_id)).hexdigest())
            # 写入文件
            file_path = os.path.join(APPENDIX_LOCAL_PATH, file_name)
            mlog.info("持续性事件文件保存路径：{}".format(file_path))
            with open(file_path, 'wb') as f:
                for line in file.chunks():
                    f.write(line)
            new_file_name_list.append(file.name)
        event_obj.judge_file = ",".join(judge_file + new_file_name_list)
        event_obj.save()

    def exist_exception(self, event, exception_type, data, src_ip, src_port, dst_ip, dst_port, c_src_port, c_dst_port):
        exception_type_obj = GreylistExceptionType.objects.filter(id=exception_type).first()
        if exception_type_obj:
            detect_rule_obj = DetectRuleModel.objects.filter(
                detect_name__contains=exception_type_obj.exception_type
            )
            event_type = []
            if detect_rule_obj:
                event_type = [i.type for i in detect_rule_obj]
            # 根据异常类型，源目IP
            q = Q()
            if event_type:
                # 需求提出人：@wangzhidan
                # 灰名单按照IP + 端口，写的时候只会写一端的端口，端口是境内端口，就是境内IP + 境内端口，端口是境外端口，就按照境外IP + 境外端口
                if src_ip and src_port:
                    q |= Q(event_type__in=event_type, judge_status=1, src_ip__in=src_ip,
                           src_port__contains=c_src_port)
                if dst_ip and dst_port:
                    q |= Q(event_type__in=event_type, judge_status=1, dst_ip__in=dst_ip,
                           dst_port__contains=c_dst_port)
            if q:
                cont_events = ContinuousEvents.objects.filter(q)
                for cont_event in cont_events:
                    EventManageModel()._update_event_with_grey_info(cont_event, event)

                cont_events.update(
                    judge_status=0,
                    judge_info=data.get("notes", "")
                )
                mlog.info("符合该灰名单事件过滤规则的所有持续性事件，状态改为其他：2，备注同步到事件详情！")
            else:
                mlog.info("没有符合该灰名单事件过滤规则的所有持续性事件，不修改状态以及同步备注信息！")
        else:
            mlog.error("异常类型不存在，无法更新异常规则以及同步研判状态和备注信息到持续性事件")

    @list_route(methods=['POST'])
    def update_event(self, request):
        """更新灰名单事件"""
        try:
            with transaction.atomic():
                mlog.info("更新灰名单事件")
                data = request.data
                event_id = data.get('id')
                data["update_time"] = int(time.time())

                src_ip = data.get("src_ip")
                dst_ip = data.get("dst_ip")
                src_ip = src_ip.split(",") if src_ip else []
                dst_ip = dst_ip.split(",") if dst_ip else []
                exception_type = data.get("exception_type")
                judge_user = data.get("judge_user")
                data["event_time"] = format3timestamp(data["event_time"])
                data["src_ip"] = list(set(src_ip))
                data["dst_ip"] = list(set(dst_ip))
                dst_port = str(data.get('dst_port'))
                src_port = str(data.get('src_port'))
                dst_port = dst_port if dst_port else None
                src_port = src_port if src_port else None
                data['dst_port'] = dst_port if dst_port else None
                data['src_port'] = src_port if src_port else None
                up_bytes = data.get("up_bytes", 0)
                up_bytes_unit = data.pop("up_bytes_unit", "MB")
                if judge_user:
                    # 不更新judge_user
                    data.pop("judge_user")
                if up_bytes != "" and up_bytes is not None:
                    up_bytes = int(self._convert_to_bytes(float(up_bytes), up_bytes_unit))
                else:
                    up_bytes = 0
                data["up_bytes"] = up_bytes

                if not src_ip and not dst_ip:
                    return JsonResponse({"msg": SIP_AND_DIP_IS_NOT_NONE, "code": 400})
                if not all([src_ip, src_port]) and not all([dst_ip, dst_port]):
                    return JsonResponse({"msg": "境内外IP、端口必填一组！", "code": 400})
                s = GreylistEventsModel.objects.filter(
                    src_ip__overlap=src_ip,
                    dst_ip__overlap=dst_ip,
                    src_port=src_port,
                    dst_port=dst_port,
                    exception_type=exception_type
                ).exclude(id=event_id)
                # 检查唯一性
                if s.exists():
                    return JsonResponse({"msg": "该异常类型，已存在该源目IP+端口白名单事件！", "code": 400})

                event = GreylistEventsModel.objects.filter(id=event_id).first()
                if not event:
                    return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})

                serializer = GreylistEventsSerializer(event, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()
                    auditLog(request, "更新灰名单事件 成功")
                    return JsonResponse({"data": serializer.data, "msg": "事件更新成功", "code": 200})
                mlog.error("更新灰名单事件失败：{}".format(json.dumps(serializer.errors, ensure_ascii=False)))
                return JsonResponse(
                    {"msg": "数据校验失败:{}".format(json.dumps(serializer.errors, ensure_ascii=False)), "code": 400})
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "更新失败", "code": 500})

    @list_route(methods=["GET"])
    def download_file(self, request):
        """
        下载文件
        """
        mlog.info("获取灰名单事件文件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            event_id = request.GET.get("event_id")
            file_name = request.GET.get("file_name")
            full_file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(), hashlib.md5(str(event_id)).hexdigest())
            file_path = "{}/{}".format(GREYLIST_EVENTS_FILES_PATH, full_file_name)
            if os.path.exists(file_path):
                with open(file_path) as f:
                    response = HttpResponse(f, content_type='application/octet-stream')
                    response['Content-Disposition'] = 'attachment; filename={}'.format(quote(file_name.encode('utf-8')))
                    auditLog(request, "下载灰名单事件附件报告:{}  成功".format(file_name))
            else:
                response = HttpResponse("no such file", status=500, )
            return response
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("获取灰名单事件文件：{}".format(e))
            return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def upload_file(self, request):
        """
        上传文件
        """
        mlog.info("上传灰名单事件附件报告")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            files = request.FILES.getlist("files")
            event_id = request.POST.get("event_id")
            # 根据id查询事件已有文件名
            instance = GreylistEventsModel.objects.get(id=event_id)
            if instance.files is None or instance.files == "":
                instance_files = []
            else:
                instance_files = instance.files.split(",")
            # 上传的文件名
            updaload_file_name_list = [file.name for file in files]
            # 事件已有的文件名 + 上传的文件名
            exist_file_name_list = updaload_file_name_list + instance_files

            # 判断是否重复
            if len(exist_file_name_list) != len(set(exist_file_name_list)):
                # 上传文件重复，返回错误信息
                result["msg"] = "文件重复"
                return JsonResponse(result, status=200)

            # 不重复，新增文件
            self.save_file(files, event_id)

            # 将文件名存入数据库对应事件
            instance.files = ",".join(exist_file_name_list)
            instance.save()
            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "上传灰名单事件附件报告:{}  成功".format(updaload_file_name_list))
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("上传灰名单事件附件报告：{}".format(e))

        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def delete_file(self, request):
        """
        删除文件
        """
        mlog.info("删除已通报事件附件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            event_id = data.get("event_id")
            file_name = data.get("file_name")
            instance = GreylistEventsModel.objects.get(id=event_id)
            if file_name not in instance.files:
                result["msg"] = "文件不存在"
                return JsonResponse(result, status=200)
            else:
                file_name_list = instance.files.split(",")
                if file_name in file_name_list:
                    file_name_list.remove(file_name)
                    instance.files = ",".join(file_name_list)
                    instance.save()
                    # 删除文件
                    file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                               hashlib.md5(str(instance.id)).hexdigest())

                    file_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, file_name)
                    if os.path.exists(file_path):
                        os.remove(file_path)

            result["code"] = 200
            result["msg"] = "成功"
            auditLog(request, "删除灰名单事件附件:{}  成功".format(file_name))
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("删除灰名单事件附件：{}".format(e))

        return JsonResponse(result, status=200)

    @list_route(methods=['GET'])
    def delete_event(self, request):
        """删除灰名单事件"""
        try:
            with transaction.atomic():
                mlog.info("删除灰名单事件")
                event_id = request.GET.get('id')
                if not event_id:
                    return JsonResponse({"msg": EVENT_ID_IS_NOT_NONE, "code": 400})

                event_ids = event_id.split(",")
                events = GreylistEventsModel.objects.filter(id__in=event_ids)
                if not events.exists():
                    return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})
                for obj in events:
                    if obj.files:
                        file_name_list = obj.files.split(",")
                        for file_name in file_name_list:
                            file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                                       hashlib.md5(str(obj.id)).hexdigest())

                            file_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, file_name)
                            if os.path.exists(file_path):
                                os.remove(file_path)
                    obj.delete()
                auditLog(request, DELETE_GREY_LIST_EVENT_SUCCESS)
                return JsonResponse({"msg": "事件删除成功", "code": 200})
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "删除失败", "code": 500})

    @list_route(methods=['GET'])
    def retrieve_event(self, request):
        """编辑回显事件信息"""
        try:
            event_id = request.GET.get('id')
            if not event_id:
                return JsonResponse({"msg": EVENT_ID_IS_NOT_NONE, "code": 400})

            event = GreylistEventsModel.objects.filter(id=event_id).first()
            if not event:
                return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})

            data = {
                'id': event.id,
                'exception_type': event.exception_type,
                'src_unit': event.src_unit,
                'src_com': event.src_com,
                'src_ip': ','.join(event.src_ip),
                'src_port': str(event.src_port) if event.src_port is not None else '',
                'service_type': event.service_type,
                'dst_ip': ','.join(event.dst_ip),
                'dst_port': str(event.dst_port) if event.dst_port is not None else '',
                'cause': event.cause,
                'notes': event.notes,
                'event_source': event.event_source,
                'event_time': timestamp2day(event.event_time) if event.event_time else "",
                'create_time': timestamp2format(event.create_time),
                'update_time': timestamp2format(event.update_time),
                'up_bytes': "",
                'up_bytes_unit': "",
                'files': event.files.split(",") if event.files else [],
            }
            # up_bytes  处理下
            up_bytes_str = self._long2unit(event.up_bytes)
            data['up_bytes'] = float(up_bytes_str.split('/')[0])
            data['up_bytes_unit'] = up_bytes_str.split('/')[1]

            return JsonResponse({"data": data, "msg": "事件详情获取成功", "code": 200})
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "获取失败", "code": 500})

    @list_route(methods=['GET'])
    def event_detail(self, request):
        """获取事件详情"""
        try:
            mlog.info("查询事件详情")
            event_id = request.GET.get('id')
            if not event_id:
                return JsonResponse({"msg": EVENT_ID_IS_NOT_NONE, "code": 400})

            event = GreylistEventsModel.objects.filter(id=event_id).first()
            if not event:
                return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})
            exception_type = GreylistExceptionType.objects.all()
            exception_type_data = {i.id: i.exception_type for i in exception_type}
            serializer = GreylistEventsSerializer(event)
            data = serializer.data
            data['exception_type'] = exception_type_data.get(event.exception_type)
            data['src_ip'] = ','.join(event.src_ip)
            data['dst_ip'] = ','.join(event.dst_ip)
            data['files'] = event.files.split(",") if event.files else []
            data['src_port'] = str(event.src_port) if event.src_port is not None else ''
            data['dst_port'] = str(event.dst_port) if event.dst_port is not None else ''
            return JsonResponse({"data": data, "msg": "事件详情获取成功", "code": 200})

        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "获取失败", "code": 500})

    @list_route(methods=['GET'])
    def export_event(self, request):
        """导出灰名单事件数据"""
        try:
            mlog.info("导出灰名单事件数据")
            # 获取请求参数
            event_id = request.query_params.get('id', '')
            src_ip = request.query_params.get('src_ip', None)
            dst_ip = request.query_params.get('dst_ip', None)
            src_com = request.query_params.get('src_com', None)
            src_unit = request.query_params.get("src_unit", "")
            cause = request.query_params.get('cause', None)
            event_source = request.query_params.get('event_source', None)
            start_time = request.query_params.get("start_time", "")
            end_time = request.query_params.get("end_time", "")
            exception_type = request.query_params.get("exception_type", "")
            judge_user = request.query_params.get("judge_user", "")
            # 构造查询过滤条件
            filter_params = {}
            if event_id:
                filter_params['id__in'] = event_id.split(",")
            if src_ip:
                filter_params['src_ip__contains'] = [src_ip]
            if dst_ip:
                filter_params['dst_ip__contains'] = [dst_ip]
            if src_com:
                filter_params['src_com__contains'] = src_com
            if cause:
                filter_params['cause'] = cause
            if event_source:
                filter_params['event_source__contains'] = event_source
            if src_unit:
                filter_params['src_unit__contains'] = src_unit
            if judge_user:
                filter_params['judge_user__contains'] = judge_user
            if exception_type:
                filter_params['exception_type'] = exception_type
            # 时间范围过滤
            if start_time:
                filter_params['event_time__gte'] = int(format3timestamp(start_time))  # 转换为整数

            if end_time:
                filter_params['event_time__lte'] = int(format3timestamp(end_time))  # 转换为整数

            # 查询符合条件的数据
            events = GreylistEventsModel.objects.filter(**filter_params).order_by("-event_time")
            serializer = GreylistEventsSerializer(events, many=True)

            exception_type = GreylistExceptionType.objects.all()

            exception_type_data = {i.id: i.exception_type for i in exception_type}

            data = []
            for item in serializer.data:
                item["exception_type"] = exception_type_data.get(item["exception_type"], "")
                data.append(item)

            # 表头
            headers = [
                '序号', '异常类型', '境内重点单位', '境内备案单位', '境内IP', '境内端口',
                '服务类型', '境外IP', '境外端口', '流出流量', '添加原因', '备注',
                '事件来源', '事件时间', '创建时间', '更新时间'
            ]

            res_data = self.generate_exception_data(data)
            file_name = "灰名单事件表_" + datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            workbook = openpyxl.Workbook()
            workbook.remove(workbook.active)
            for exception_type, data in res_data.items():
                sheet = workbook.create_sheet(title=u"{}".format(exception_type))
                sheet.append(headers)
                num = 0
                for row in data:
                    row["up_bytes"] = row["up_bytes"] if row["up_bytes"] else '0'
                    num += 1
                    sheet.append(
                        [
                            num,
                            row["exception_type"],
                            row["src_unit"],
                            row["src_com"],
                            ','.join(row["src_ip"]),
                            row["src_port"],
                            row["service_type"],
                            ','.join(row["dst_ip"]),
                            row["dst_port"],
                            ''.join([str(row["up_bytes"]), row["up_bytes_unit"]]),
                            row["cause"],
                            row["notes"],
                            row["event_source"],
                            row["event_time"],
                            row["create_time"],
                            row["update_time"],
                        ]
                    )
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename={}.xlsx'.format(quote(file_name))
            response.content = save_virtual_workbook(workbook)
            return response
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({
                "msg": "导出失败",
                "code": 500
            })

    def generate_exception_data(self, data):
        res_data = defaultdict(list)
        for event in data:
            exception_type = event["exception_type"]
            if exception_type is None or exception_type == "":
                exception_type = "无异常类型"
            res_data[exception_type].append(event)
        return res_data

    @list_route(methods=["POST"])
    def create_exception_type(self, request):
        """
        新增异常类型
        """
        mlog.info("新增异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            data = json.loads(request.body)
            if not data.get("exception_type"):
                result["msg"] = "异常类型不能为空"
                return JsonResponse(result, status=200)
            if GreylistExceptionType.objects.filter(exception_type=data.get("exception_type")).exists():
                result["msg"] = "异常类型已存在"
                return JsonResponse(result, status=200)
            else:
                GreylistExceptionType.objects.create(exception_type=data.get("exception_type"))
                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "新增灰名单事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("新增异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def get_exception_type(self, request):
        """
        获取异常类型
        """
        mlog.info("获取异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            exception_type = GreylistExceptionType.objects.all().order_by("id")
            result["code"] = 200
            result["msg"] = "成功"
            result["data"] = [{"id": i.id, "exception_type": i.exception_type} for i in exception_type]
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("获取异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["POST"])
    def update_exception_type(self, request):
        """
        更新异常类型
        """
        mlog.info("更新异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            with transaction.atomic():
                data = json.loads(request.body)
                type_id = data.get("type_id")
                exception_type = data.get("exception_type")
                if not type_id:
                    result["msg"] = "id不能为空"
                    return JsonResponse(result, status=200)
                if not exception_type:
                    result["msg"] = "异常类型不能为空"
                    return JsonResponse(result, status=200)
                instance = GreylistExceptionType.objects.filter(id=type_id).first()
                if not instance:
                    result["msg"] = "异常类型不存在"
                    return JsonResponse(result, status=200)
                old_exception_type = instance.exception_type
                instance.exception_type = exception_type
                mlog.info("更新异常类型完成：{}-->>{}".format(old_exception_type, instance.exception_type))
                instance.save()
                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "更新灰名单事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("更新异常类型：{}".format(e))
        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def delete_exception_type(self, request):
        """
        删除异常类型
        """
        mlog.info("删除异常类型")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            type_id = request.query_params.get("type_id")
            if not type_id:
                result["msg"] = "id不能为空"
                return JsonResponse(result, status=200)

            # 查询灰名单事件中是否存在该异常类型
            reported_events = GreylistEventsModel.objects.filter(exception_type=type_id)
            if reported_events.exists():
                result["msg"] = "异常类型已存在灰名单事件中，不能删除"
                return JsonResponse(result, status=200)
            else:
                GreylistExceptionType.objects.filter(id=type_id).delete()
                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "删除灰名单事件 异常类型 成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("删除异常类型：{}".format(e))

        return JsonResponse(result, status=200)

    @list_route(methods=["GET"])
    def grey_list_get_continuous_event_detail(self, request):
        """
        获取持续性事件详情用于回显
        """
        mlog.info("获取持续性事件详情用于回显")
        result = {
            "code": 500,
            "msg": "error",
            "data": {}
        }
        try:
            event_id = request.GET.get("event_id")
            if not event_id:
                result["msg"] = EVENT_ID_IS_NOT_NONE
                return JsonResponse(result, status=200)

            event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
            if not event_obj:
                result["msg"] = "未找到对应的持续性事件"
                return JsonResponse(result, status=200)

            # 获取事件来源
            event_type = event_obj.event_type
            event_type_menu = CommonConf().get_continuous_event_type_reverse()
            event_source = event_type_menu.get(str(event_type), str(event_type))
            if event_obj.analysis_tech == 1:
                event_source = event_source + "/5min"
            else:
                event_source = event_source + "/天"

            # 获取单位信息并处理地理位置
            src_unit = ContinuousEventsTagModel.objects.filter(
                event_id=event_id,
                tag_type=1,
                tag_name="unit"
            ).first()
            src_unit = src_unit.tag_content if src_unit else event_obj.src_com

            # 处理地理位置和单位名称
            processed_src_unit = self._process_location(event_obj.src_region, src_unit)

            # 流量转换为合适的单位显示
            up_bytes_str = self._long2unit(event_obj.up_bytesall)
            up_bytes_unit = up_bytes_str.split('/')[1]
            up_bytes = float(up_bytes_str.split('/')[0])

            # 服务类型，需要查询事件标签表中服务端的service字段
            # 根据事件类型判断服务端是境内还是境外，event_type是奇数，服务端为境外，event_type是偶数，服务端为境内
            if event_type % 2 == 0:
                tag_type = 1
            else:
                tag_type = 2

            service_ = ContinuousEventsTagModel.objects.filter(
                event_id=event_id,
                tag_type=tag_type,
                tag_name="service"
            ).first()
            service_type = service_.tag_content if service_ else ""

            # 构建返回数据
            result["data"] = {
                "event_id": event_id,
                "event_source": event_source,
                "src_unit": processed_src_unit,
                "src_com": event_obj.src_com,
                "up_bytes": up_bytes,
                "up_bytes_unit": up_bytes_unit,
                "notes": event_obj.judge_info,
                "src_ip": event_obj.src_ip,
                "src_port": event_obj.src_port.strip(",").split(",")[0],
                "dst_ip": event_obj.dst_ip,
                "dst_port": event_obj.dst_port.strip(",").split(",")[0],
                "service_type": service_type,
                "files": event_obj.judge_file.split(",") if event_obj.judge_file else []
            }

            result["code"] = 200
            result["msg"] = "成功"

        except Exception as e:
            result["msg"] = "获取持续性事件详情失败"
            mlog.exception("获取持续性事件详情失败：{}".format(e))
        return JsonResponse(result, status=200)

    @staticmethod
    def _long2unit(bytes):
        """
        将字节数转换为最适合的存储单位表示

        Args:
            bytes (int): 要转换的字节数

        Returns:
            str: 转换后的字符串，格式为 "数值/单位"
                数值保留2位小数，单位可能为KB、MB、GB或TB

        Examples:
            _long2unit(1024)
            "1.00/KB"
            _long2unit(1048576)
            "1.00/MB"

        Note:
            - 1KB = 1024 bytes
            - 1MB = 1024 KB
            - 1GB = 1024 MB
            - 1TB = 1024 GB
            - 返回值中的斜杠用于前端显示分隔
        """
        if 1024 <= bytes < 1024 * 1024:
            return "%.2f/KB" % round(bytes / 1024.0, 2)
        elif 1024 * 1024 <= bytes < 1024 * 1024 * 1024:
            return "%.2f/MB" % round(bytes / 1024.0 / 1024.0, 2)
        elif 1024 * 1024 * 1024 <= bytes < 1024 * 1024 * 1024 * 1024:
            return "%.2f/GB" % round(bytes / 1024.0 / 1024.0 / 1024.0, 2)
        else:
            return "%.2f/TB" % round(bytes / 1024.0 / 1024.0 / 1024.0 / 1024.0, 2)

    def _convert_to_bytes(self, size, unit):
        """
        将指定单位的存储大小转换为字节数

        Args:
            size (float): 存储大小的数值
            unit (str): 存储单位，可选值为 'KB'、'MB'、'GB'、'TB'

        Returns:
            int: 转换后的字节数

        Examples:
            _convert_to_bytes(1.5, 'MB')
            1572864
            _convert_to_bytes(1, 'GB')
            1073741824
        """
        if unit == "KB":
            return size * 1024
        elif unit == "MB":
            return size * 1024 * 1024
        elif unit == "GB":
            return size * 1024 * 1024 * 1024
        elif unit == "TB":
            return size * 1024 * 1024 * 1024 * 1024

    def _process_location(self, src_region, src_unit):
        """
        处理地理位置和单位名称

        Args:
            src_region (str): 地理位置，格式如"中国 河南省 南阳市"、"中国 北京市"
            src_unit (str): 单位名称

        Returns:
            str: 处理后的单位名称，格式如"河南省xx单位"、"北京市xx单位"
        """
        if not src_region or not src_unit:
            return src_unit

        try:
            # 分割地理位置
            locations = src_region.strip().split()
            if len(locations) < 2:
                return src_unit

            # 获取省份信息（第二个元素）
            province = locations[1]

            # 处理直辖市
            municipalities = ['北京市', '上海市', '天津市', '重庆市']
            if src_unit.startswith(province):
                return src_unit

            # 分支语句下内容一样，暂时去掉，保留一个
            elif province in municipalities:
                # 直辖市不需要重复"市"字
                result = "{0}{1}".format(province, src_unit)
            else:
                # 非直辖市，如果单位名已包含省份则不重复添加
                result = "{0}{1}".format(province, src_unit)

            return result.encode('utf-8')

        except Exception as e:
            mlog.error("处理地理位置信息失败：{0}".format(e))
            return src_unit

    @list_route(methods=['GET'])
    def echo_grey_list(self, request):
        """
        回显数据，因数据结构不一样，使用变量区分开
        1、白名单事件
        2、已通报事件
        """
        try:
            grey_event_id = request.query_params.get('grey_event_id')
            event_type = request.query_params.get('event_type')
            mlog.info("回显数据，grey_event_id:{}".format(grey_event_id))
            mlog.info("回显数据，event_type:{}".format(event_type))
            res = GreylistEventsModel.objects.filter(id=grey_event_id).first()
            result = {
                "code": 500,
                "msg": "NO found data",
                "data": {}
            }
            if not res:
                result["msg"] = "没有找到该灰名单事件"
                return JsonResponse(result)
            # 流量转换为合适的单位显示
            up_bytes_str = self._long2unit(res.up_bytes)
            up_bytes_unit = up_bytes_str.split('/')[1]
            up_bytes = float(up_bytes_str.split('/')[0])

            src_ip = ",".join(res.src_ip) if res.src_ip else ""
            src_port = str(res.src_port) if res.src_port is not None else ''
            dst_ip = ",".join(res.dst_ip) if res.dst_ip else ""
            dst_port = str(res.dst_port) if res.dst_port is not None else ''

            if event_type == '1':
                result["data"] = {
                    "event_id": res.id,
                    "event_source": res.event_source,
                    "src_unit": res.src_unit,
                    "src_com": res.src_com,
                    "up_bytes": up_bytes,
                    "up_bytes_unit": up_bytes_unit,
                    "notes": res.notes,
                    "src_ip": src_ip,
                    "src_port": src_port,
                    "dst_ip": dst_ip,
                    "dst_port": dst_port,
                    "service_type": res.service_type
                }
                result["code"] = 200
                result["msg"] = "成功"
            elif event_type == '2':
                result["data"] = {
                    "event_id": res.id,
                    "event_source": res.event_source,
                    "src_unit": res.src_unit,
                    "up_bytes": up_bytes,
                    "up_bytes_unit": up_bytes_unit,
                    "notes": res.notes,
                    "files": res.files.split(",") if res.files else [],
                    "src_ip": src_ip,
                    "src_port": src_port,
                    "dst_ip": dst_ip,
                    "dst_port": dst_port,
                    "service_type": res.service_type
                }
                result["code"] = 200
                result["msg"] = "成功"
            return JsonResponse(result)
        except Exception as e:
            mlog.exception("回显数据失败：{0}".format(e))
            return JsonResponse({"code": 500, "msg": "error", "data": {}})

    @list_route(methods=['POST'])
    def grey_to_white_event(self, request):
        """
        将灰名单事件转为白名单事件
        1、研判之前灰名单事件（逻辑参考从持续性事件加入已通报事件）
        2、删除之前灰名单事件
        3、删除之前灰名单事件附件
        """
        try:
            with transaction.atomic():
                mlog.info("转为白名单事件")
                username = request.session['username']
                data = request.data
                event_id = data.get("event_id")
                data["create_time"] = int(time.time())
                data["update_time"] = int(time.time())
                data["judge_user"] = username

                filter_rule = data.get("filter_rule")
                src_ip = data.get("src_ip")
                dst_ip = data.get("dst_ip")
                src_ip = src_ip.split(",") if src_ip else []
                dst_ip = dst_ip.split(",") if dst_ip else []
                dst_port = data['dst_port']
                src_port = data['src_port']
                src_com = data.get("src_com")
                exception_type = data.get("exception_type")
                data["event_time"] = format3timestamp(data["event_time"])
                data["src_ip"] = list(set(src_ip))
                data["dst_ip"] = list(set(dst_ip))

                data['dst_port'] = dst_port
                data['src_port'] = src_port
                # 处理端口， 用于查询持续性事件
                c_dst_port = ',' + str(dst_port) + "," if dst_port else ''
                c_src_port = ',' + str(src_port) + "," if src_port else ''

                up_bytes = data.get("up_bytes", 0)
                up_bytes_unit = data.pop("up_bytes_unit", "MB")
                if up_bytes != "" and up_bytes is not None:
                    up_bytes = int(self._convert_to_bytes(float(up_bytes), up_bytes_unit))
                else:
                    up_bytes = 0
                data["up_bytes"] = up_bytes
                # 根据过滤规则判断参数是否传入
                mlog.info("参数：dst_port{}".format(dst_port))
                mlog.info("参数：src_port{}".format(src_port))
                status, response = self.verify_grey_to_white_params(data, src_ip, dst_ip)
                if status:
                    return response
                grey_events = GreylistEventsModel.objects.filter(id=event_id)
                if not grey_events.exists():
                    return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})
                serializer = WhitelistEventsSerializer(data=request.data)
                if serializer.is_valid():
                    # 创建事件
                    serializer.save()
                    auditLog(request, "转为白名单事件 成功")

                    self.grey_to_white_process(
                        exception_type,
                        filter_rule,
                        data,
                        src_ip,
                        dst_ip,
                        src_port,
                        dst_port,
                        c_src_port,
                        c_dst_port,
                        src_com
                    )
                    self.del_grey_to_white_file(grey_events)
                    auditLog(request, DELETE_GREY_LIST_EVENT_SUCCESS)

                    return JsonResponse({"data": serializer.data, "msg": "转为白名单事件成功", "code": 200})
                mlog.error("转为白名单事件失败：{}".format(json.dumps(serializer.errors, ensure_ascii=False)))
                return JsonResponse(
                    {"msg": "数据校验失败：{}".format(json.dumps(serializer.errors, ensure_ascii=False)), "code": 400})
        except Exception as _:
            mlog.error(traceback.format_exc())
            return JsonResponse({"msg": "失败", "code": 500})

    def verify_grey_to_white_params(self, data, src_ip, dst_ip):
        status = 0
        response = None
        filter_rule = data.get("filter_rule")
        dst_port = str(data['dst_port'])
        src_port = str(data['src_port'])
        src_com = data.get("src_com")
        exception_type = data.get("exception_type")
        if filter_rule == 1:
            if not src_ip and not dst_ip:
                return 1, JsonResponse({"msg": SIP_AND_DIP_IS_NOT_NONE, "code": 400})
        elif filter_rule == 2 and not src_com:
            return 1, JsonResponse({"msg": "备案单位不能为空！", "code": 400})
        elif filter_rule == 3:
            if not all([src_ip, src_port]) and not all([dst_ip, dst_port]):
                return 1, JsonResponse({"msg": "境内外IP、端口必填一组！", "code": 400})
            if src_ip and src_port and WhitelistEventsModel.objects.filter(
                    src_ip__overlap=src_ip,
                    src_port=src_port,
                    filter_rule=filter_rule,
                    exception_type=exception_type).exists():
                return 1, JsonResponse({"msg": "已存在该IP+端口的白名单事件！", "code": 400})
            if dst_ip and dst_port and WhitelistEventsModel.objects.filter(
                    dst_ip__overlap=dst_ip,
                    dst_port=dst_port,
                    filter_rule=filter_rule,
                    exception_type=exception_type).exists():
                return 1, JsonResponse({"msg": "已存在该IP+端口的白名单事件！", "code": 400})
        # 检查唯一性， 增加按照过滤规则校验
        if src_com and filter_rule == 2 and WhitelistEventsModel.objects.filter(src_com=src_com,
                                                                                filter_rule=filter_rule).exists():
            return 1, JsonResponse({"msg": "已存在该备案单位白名单事件！", "code": 400})
        if all([src_ip, dst_ip]) and filter_rule == 1 and WhitelistEventsModel.objects.filter(
                Q(src_ip__overlap=src_ip),
                Q(dst_ip__overlap=dst_ip),
                filter_rule=filter_rule,
                exception_type=exception_type
        ).exists():
            # 白名单功能，除了IP对之外【IP对过滤规则是1】，其他的过滤规则都不加异常类型的限制，自动研判也一样。
            return 1, JsonResponse({"msg": "该异常类型，已存在该源目IP白名单事件！", "code": 400})
        return status, response

    def grey_to_white_process(self, exception_type, filter_rule, data, src_ip, dst_ip, src_port, dst_port, c_src_port,
                              c_dst_port, src_com):
        exception_type_obj = WhitelistExceptionType.objects.filter(id=exception_type).first()
        if not exception_type_obj:
            mlog.error("异常类型不存在，无法更新异常规则以及同步研判状态和备注信息到持续性事件")
            return
        detect_rule_obj = DetectRuleModel.objects.filter(
            detect_name__contains=exception_type_obj.exception_type
        )
        event_type = []
        if detect_rule_obj:
            event_type = [i.type for i in detect_rule_obj]
        # 根据异常类型，源目IP，  或者备案单位的
        q = Q()
        has_condition = False
        # 增加按照过滤规则研判持续性事件
        if event_type:
            if filter_rule == 3:
                if all([src_ip, src_port]):
                    q |= Q(judge_status__in=(0, 1), src_ip__in=src_ip,
                           src_port__contains=c_src_port)
                    has_condition = True
                if all([dst_ip, dst_port]):
                    q |= Q(judge_status__in=(0, 1), dst_ip__in=dst_ip,
                           dst_port__contains=c_dst_port)
                    has_condition = True
            elif filter_rule == 1:
                if all([src_ip, dst_ip]):
                    q |= Q(event_type__in=event_type, judge_status__in=(0, 1), src_ip__in=src_ip,
                           dst_ip__in=dst_ip)
                    has_condition = True

        # 处理 filter_rule == 2 的 src_com
        if filter_rule == 2 and src_com:
            q |= Q(src_com=src_com, judge_status__in=(0, 1))
            has_condition = True

        if has_condition:
            ContinuousEvents.objects.filter(q).update(
                judge_status=2,
                judge_info=data.get("notes", "")
            )
            mlog.info("符合该白名单事件过滤规则的所有持续性事件，状态改为已研判未通报：2，备注同步到事件详情！")
        else:
            mlog.info("没有符合该白名单事件过滤规则的所有持续性事件，不修改状态以及同步备注信息！")

    def del_grey_to_white_file(self, grey_events):
        for obj in grey_events:
            if obj.files:
                file_name_list = obj.files.split(",")
                for file_name in file_name_list:
                    file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                               hashlib.md5(str(obj.id)).hexdigest())

                    file_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, file_name)
                    if os.path.exists(file_path):
                        os.remove(file_path)
            obj.delete()

    @list_route(methods=['POST'])
    def grey_to_reported_event(self, request):
        """
        将灰名单事件转为已通报事件
        # 正常的新增逻辑中增加
        1、同步附件
        2、研判之前灰名单事件（逻辑参考从持续性事件加入已通报事件）
        3、删除之前灰名单事件
        4、删除之前灰名单事件附件
        """

        mlog.info("转为已通报事件")
        result = {
            "code": 500,
            "msg": "error"
        }
        try:
            with transaction.atomic():
                username = request.session['username']
                # 创建对象
                files = request.FILES.getlist("files")
                file_name_list = [file.name for file in files]
                if len(file_name_list) != len(set(file_name_list)):
                    result["msg"] = "文件重复"
                    return JsonResponse(result, status=200)
                data = request.data
                event_id = data.get("event_id")
                # 灰名单性事件原有的附件
                file_list = request.POST.getlist("file_list")
                mlog.info("file_list:{}".format(file_list))

                exception_type = data.get("exception_type", "")
                src_unit = data.get("src_unit", "")
                src_ip = data.get("src_ip", "").strip()
                src_port = data.get("src_port", "").strip()
                service_type = data.get("service_type", "")
                dst_ip = data.get("dst_ip", "").strip()
                dst_port = data.get("dst_port", "").strip()
                attack_org = data.get("attack_org", "")

                up_bytes = data.get("up_bytes", 0)
                up_bytes_unit = data.get("up_bytes_unit", "MB")
                if up_bytes != "" and up_bytes is not None:
                    up_bytes = self._convert_to_bytes(float(up_bytes), up_bytes_unit)
                else:
                    up_bytes = 0

                notes = data.get("notes", "")
                event_source = data.get("event_source", "")
                event_time = data.get("event_time", datetime.datetime.now().strftime(DATE_FORMAT))
                feedback_situation = data.get("feedback_situation", "")

                # 校验ip与端口
                msg = self.check_ip_port(exception_type, src_ip, src_port, dst_ip, dst_port)
                if msg:
                    result["msg"] = msg
                    mlog.info("校验结果：{}".format(msg))
                    return JsonResponse(result, status=200)
                create_data = {
                    "exception_type": exception_type,
                    "src_unit": src_unit,
                    "src_ip": src_ip,
                    "src_port": src_port,
                    "service_type": service_type,
                    "dst_ip": dst_ip,
                    "dst_port": dst_port,
                    "attack_org": attack_org,
                    "up_bytes": up_bytes,
                    "notes": notes,
                    "event_source": event_source,
                    "event_time": format3timestamp(event_time),
                    "feedback_situation": feedback_situation,
                    "judge_user": username
                }
                params_error = ReportedEventsCheck().check_required_params(create_data)
                if params_error:
                    result["msg"] = params_error
                    mlog.info("校验结果：{}".format(msg))
                    return JsonResponse(result, status=200)
                grey_event = GreylistEventsModel.objects.filter(id=event_id).first()
                if not grey_event:
                    return JsonResponse({"msg": EVENT_DOES_NOT_EXIST, "code": 400})
                instance = ReportedEventsModel.objects.create(**create_data)
                # 文件名存入数据库
                instance.files = ",".join(file_name_list + file_list)
                instance.save()
                # 本地目录是否存在，不存在则创建
                if not os.path.exists(REPORTED_EVENTS_FILES_PATH):
                    os.mkdir(REPORTED_EVENTS_FILES_PATH)
                # 文件写入本地
                self.save_report_file(files, instance.id)
                # 同步灰名单事件附件
                c_dst_port = ',' + str(grey_event.dst_port) + "," if grey_event.dst_port else ''
                c_src_port = ',' + str(grey_event.src_port) + "," if grey_event.src_port else ''
                if file_list:
                    for file_name in file_list:
                        # 构建源文件路径
                        src_file_name = "{}.{}".format(
                            hashlib.md5(file_name).hexdigest(),
                            hashlib.md5(str(event_id)).hexdigest()
                        )
                        src_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, src_file_name)

                        # 构建目标文件路径
                        dst_file_name = "{}.{}".format(
                            hashlib.md5(file_name).hexdigest(),
                            hashlib.md5(str(instance.id)).hexdigest()
                        )
                        dst_path = os.path.join(REPORTED_EVENTS_FILES_PATH, dst_file_name)

                        # 复制文件
                        if os.path.exists(src_path):
                            shutil.copy(src_path, dst_path)
                        else:
                            mlog.error("持续性事件文件不存在：{}".format(src_path))
                # 删除灰名单事件/附件
                self.del_grey_event_file(grey_event)

                # 根据灰名单事件的研判逻辑，找到该灰名单事件研判的持续性事件的ID列表，然后传入已通报事件的研判逻辑中进行重新研判
                self.judge_grey_event(grey_event, src_ip, dst_ip, c_dst_port, c_src_port)

                grey_event.delete()
                auditLog(request, DELETE_GREY_LIST_EVENT_SUCCESS)

                result["code"] = 200
                result["msg"] = "成功"
                auditLog(request, "转为已通报事件成功")
        except Exception as e:
            result["msg"] = REQUEST_ERROR
            mlog.exception("转为已通报事件：{}".format(e))

        return JsonResponse(result, status=200)

    def del_grey_event_file(self, grey_event):
        if grey_event.files:
            file_name_list = grey_event.files.split(",")
            for file_name in file_name_list:
                file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(),
                                           hashlib.md5(str(grey_event.id)).hexdigest())

                file_path = os.path.join(GREYLIST_EVENTS_FILES_PATH, file_name)
                if os.path.exists(file_path):
                    os.remove(file_path)

    def judge_grey_event(self, grey_event, src_ip, dst_ip, c_dst_port, c_src_port):
        exception_type_obj = GreylistExceptionType.objects.filter(id=grey_event.exception_type).first()
        if exception_type_obj:
            detect_rule_obj = DetectRuleModel.objects.filter(
                detect_name__contains=exception_type_obj.exception_type
            )
            event_type = []
            if detect_rule_obj:
                event_type = [i.type for i in detect_rule_obj]
            # 根据异常类型，源目IP
            q = Q()

            if event_type:
                if src_ip and c_src_port:
                    src_ips = src_ip.split(",")
                    q |= Q(event_type__in=event_type, judge_status__in=(0, 1), src_ip__in=src_ips,
                           src_port__contains=c_src_port)
                if dst_ip and c_dst_port:
                    dst_ips = dst_ip.split(",")
                    q |= Q(event_type__in=event_type, judge_status__in=(0, 1), dst_ip__in=dst_ips,
                           dst_port__contains=c_dst_port)
            if q:
                mlog.info("q:{}".format(q))
                event_id_list = ContinuousEvents.objects.filter(q).values_list("event_id", flat=True).distinct()
                mlog.info("event_id_list:{}".format(event_id_list))
                JudgeToReportedEventUtil().judge_event_to_reported_event(
                    event_id_list=event_id_list,
                    judge_status=0
                )
                JudgeToReportedEventUtil().judge_event_to_reported_event(
                    event_id_list=event_id_list,
                    judge_status=1
                )

    def check_ip_port(self, exception_type=None, src_ips=None, src_ports=None, dst_ips=None, dst_ports=None,
                      reported_ids=None):
        """
        校验ip与端口
        Args:
            exception_type (str): 异常类型
            src_ips (str): 境内IP列表
            src_ports (str): 境内端口列表
            dst_ips (str): 境外IP列表
            dst_ports (str): 境外端口列表

        Returns:
            str: 错误信息，如果没有错误则返回None
        """
        return ReportedEventsCheck().check(exception_type, src_ips, src_ports, dst_ips, dst_ports, reported_ids)

    def save_report_file(self, file_list, instance_id):
        for file in file_list:
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(), hashlib.md5(str(instance_id)).hexdigest())
            # 写入文件
            file_path = os.path.join(REPORTED_EVENTS_FILES_PATH, file_name)
            mlog.info("文件保存路径：{}".format(file_path))
            with open(file_path, 'wb') as f:
                for line in file.chunks():
                    f.write(line)

    @list_route(methods=["GET"])
    def grey_redirect_event(self, request):
        """
                灰名单事件跳转持续性事件页面参数获取
        灰名单研判规则
        exception_type_obj = GreylistExceptionType.objects.filter(id=exception_type).first()
        if exception_type_obj:
            detect_rule_obj = DetectRuleModel.objects.filter(
                detect_name__contains=exception_type_obj.exception_type
            )
            event_type = []
            if detect_rule_obj:
                event_type = [i.type for i in detect_rule_obj]
            # 根据异常类型，源目IP
            q = Q()
            if event_type:
                # 需求提出人：@wangzhidan
                # 灰名单按照IP + 端口，写的时候只会写一端的端口，端口是境内端口，就是境内IP + 境内端口，端口是境外端口，就按照境外IP + 境外端口
                if src_ip and src_port:
                    q |= Q(event_type__in=event_type, judge_status=1, src_ip__in=src_ip,
                           src_port__contains=c_src_port)
                if dst_ip and dst_port:
                    q |= Q(event_type__in=event_type, judge_status=1, dst_ip__in=dst_ip,
                           dst_port__contains=c_dst_port)
            if q:
                cont_events = ContinuousEvents.objects.filter(q)
                for cont_event in cont_events:
                    EventManageModel()._update_event_with_grey_info(cont_event, event)

                cont_events.update(
                    judge_status=0,
                    judge_info=data.get("notes", "")
                )

        """
        try:
            # 1、通过事件ID获取灰名单事件信息
            event_id = request.query_params.get("event_id")
            grey_event = GreylistEventsModel.objects.filter(id=event_id).first()

            src_ip = grey_event.src_ip
            src_port = grey_event.src_port
            dst_ip = grey_event.dst_ip
            dst_port = grey_event.dst_port
            exception_type = grey_event.exception_type
            event_time = grey_event.event_time
            start_time = timestamp2format(event_time - 60 * 60 * 24 * 30)
            end_time = timestamp2format(event_time + 60 * 60 * 24 * 30)
            data = {
                "start_time": start_time,
                "end_time": end_time,
            }

            # 2、根据灰名单事件信息进行判断，生成跳转参数
            exception_type_obj = GreylistExceptionType.objects.filter(id=exception_type).first()
            event_type = []
            if exception_type_obj:
                detect_rule_obj = DetectRuleModel.objects.filter(
                    detect_name__contains=exception_type_obj.exception_type
                )
                event_type = []
                if detect_rule_obj:
                    event_type = [str(i.type) for i in detect_rule_obj]
            if event_type:
                data['type'] = ','.join(set(event_type))
            if src_ip and src_port:
                data['sip'] = '|'.join(src_ip)
                data['src_port'] = src_port
            if dst_ip and dst_port:
                data['dip'] = '|'.join(dst_ip)
                data['dst_port'] = dst_port
            return JsonResponse({"data": data, "msg": "成功", "code": 200})
        except Exception as e:

            mlog.error("获取灰名单事件跳转参数失败：{}".format(e))
            mlog.error(traceback.format_exc())
            return JsonResponse({"code": 400, "msg": "获取灰名单事件跳转参数失败"})
