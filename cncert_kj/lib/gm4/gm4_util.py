# -*-coding:utf-8-*-

import base64
import binascii

from cncert_kj.lib.gm4.sm4 import CryptSM4, SM4_ENCRYPT, SM4_DECRYPT


class SM4:
    """
    国密sm4加解密
        str_data = 'usocket("qc1")'
        key = "abcedfghijklmnop"
        SM4 = SM4()
        print u"待加密内容：", str_data
        encoding = SM4.encrypt(key, str_data)
        print u"国密sm4加密后的结果：", encoding
        print u"国密sm4解密后的结果：", SM4.decrypt(key, encoding)
        str_ = base64.b64encode(encoding)
        print u"国密4加密后base64加密后的结果: ", str_

    """
    def __init__(self):
        self.crypt_sm4 = CryptSM4()

    def str_to_hex_str(self, hex_str):
        u"""
        字符串转hex
        :param hex_str: 字符串
        :return: hex
        """
        hex_data = hex_str.encode('utf-8')
        str_bin = binascii.unhexlify(hex_data)
        return str_bin.decode('utf-8')

    def encrypt(self, encrypt_key, value):
        u"""
        国密sm4加密
        :param encrypt_key: sm4加密key
        :param value: 待加密的字符串
        :return: sm4加密后的hex值
        """
        crypt_sm4 = self.crypt_sm4
        crypt_sm4.set_key(encrypt_key.encode(), SM4_ENCRYPT)
        date_str = str(value)
        encrypt_value = crypt_sm4.crypt_ecb(date_str.encode())  # bytes类型
        return base64.b64encode(encrypt_value)

    def decrypt(self, decrypt_key, encrypt_value):
        u"""
        国密sm4解密
        :param decrypt_key:sm4加密key
        :param encrypt_value: 待解密的hex值
        :return: 原字符串
        """
        crypt_sm4 = self.crypt_sm4
        crypt_sm4.set_key(decrypt_key.encode(), SM4_DECRYPT)
        decrypt_value = crypt_sm4.crypt_ecb(base64.b64decode(encrypt_value))
        return decrypt_value.decode('utf-8')


