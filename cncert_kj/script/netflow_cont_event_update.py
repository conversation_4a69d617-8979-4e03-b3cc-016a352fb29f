#!/usr/bin/env python
# -*- coding:utf-8 -*-

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction

from cncert_kj.script.update_cont_event import update_netflow_sql, UpdateContEventUtil
from cncert_kj.utils import lock_util, logger

mlog = logger.init_logger('netflow_cont_event_update')


def update_timeout_events():
    try:
        sql = '''
        UPDATE
            internal_app_bsa_gjk.continuous_events
        SET
            status = 0
        WHERE
            status = 1 AND end_time + 86400 <= floor(extract(epoch from now())) and report_type = 2
        '''
        pg_sql = CPgSqlParam(sql)
        CFunction.execute(pg_sql)
    except Exception as e:
        mlog.exception("更新状态失败：{}".format(e))


if __name__ == '__main__':
    try:
        mlog.info("=================开始任务=================")
        mlog.info("开始将netflow持续性事件表中状态为1，end_time在前一天之前的数据状态修改为0")
        update_timeout_events()

        mlog.info("=======开始处理netflow事件=======")
        netflow_last_time = update_netflow_sql()
        # 更新记录时间
        if netflow_last_time:
            # mlog.info("更新记录id：{}-{}".format("netflow_scan_timestamp", netflow_last_time))
            # UpdateContEventUtil(2).update_conf_value("netflow_scan_timestamp", netflow_last_time)
            mlog.info("更新记录id：{}-{}".format("update_cont_event_netflow_altert_id", netflow_last_time))
            UpdateContEventUtil(2).update_conf_value("update_cont_event_netflow_altert_id", netflow_last_time)

        mlog.info("=======事件处理完成=======")
        mlog.info("=================结束任务=================")
    except Exception as e:
        mlog.exception("持续性事件入库失败：{}".format(e))
