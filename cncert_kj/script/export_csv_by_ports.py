# -*- coding: utf-8 -*-
import argparse
import csv
import json
import sys
import time
from datetime import datetime, timedelta

from cncert_kj.lib import openpyxl
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.lib.json_utils import JsonTools
from cncert_kj.models import type_conf_model
from cncert_kj.utils import logger, time_trans, bytes_trans
from cncert_kj.utils.conf_util import CommonConf, JUDGE_STATUS_MAP, STATUS_MAP, ANALYSIS_TECH_MAP, REPORT_TYPE, \
    PORT_DISTRIBUTION_MAP
from cncert_kj.utils.flow_log_util import SUFFIX

conf_util = CommonConf()
EVENT_TYPE_MAP = conf_util.get_continuous_event_type_reverse()

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('export_csv_by_ports')


class ExportCsvByPorts(object):
    """
    需求简介
    时间就是前一天一整天  端口是1005和1224 协议类型是TCP   产出一个CSV，告警表的所有字段都要

    2、输出csv文件时，同一个目的ip要放在一起

    逻辑简述
    1、前一天时间范围  示例：  2024-10-30 00:00:00  到 2024-10-31 00:00:00
    2、根据时间范围取出所有事件，然后根据事件关联的告警id 查出所有告警
    3、通联根据告警类型来判定告警是上传还是下载；下载取原始日志中的sport匹配，上传取原始日志中的dport匹配
    4、告警导出一个csv，事件导出一个csv
    """

    def __init__(self, port_list, proto):
        # 获取当前时间的日期部分并减去一天
        yesterday = datetime.now() - timedelta(days=1)
        # 获取前一天的开始时间（即前一天的 00:00:00）
        start_of_yesterday = datetime(yesterday.year, yesterday.month, yesterday.day, 0, 0, 0)
        # 获取前一天的结束时间（即当天的 00:00:00）
        end_of_yesterday = start_of_yesterday + timedelta(days=1)
        # 转换为时间戳格式
        self.start_time = int(time.mktime(start_of_yesterday.timetuple()))
        self.ent_time = int(time.mktime(end_of_yesterday.timetuple()))
        self.json_tool = JsonTools()
        self.download_type = self.get_download_type()
        self.ports = port_list
        self.proto = proto

    def get_download_type(self):
        """
        获取下载告警类型
        """
        download_type = []
        type_conf_list = type_conf_model.get_type_conf()
        for item in type_conf_list:
            if "下载" in item[1]:
                download_type.append(int(item[2]))
        return download_type

    def get_tclog_data(self):
        filter_sql = '''
            SELECT
                name,--0
                type,--1
                start_time,--2
                end_time,--3
                sip,--4
                src_region,--5
                src_operator,--6
                src_iot,--7
                src_service,--8
                src_unit,--9
                sport,--10
                src_com,--11
                dip,--12
                dst_region,--13
                dst_operator,--14
                dst_iot,--15
                dst_service,--16
                dst_unit,--17
                dport,--18
                dst_com,--19
                up_bytes_all,--20
                down_bytes_all,--21
                up_packets_all,--22
                down_packets_all,--23
                flow_list,--24
                uuid,--25
                flow_logs,--26
                analysis_tech,--27
                port_distribution--28
            FROM internal_app_bsa_gjk.traffic_alert
            WHERE end_time >= %s and end_time <= %s AND 
            (sport IN %s OR dport IN %s);'''
        mlog.info("通联filter_sql：{}".format(filter_sql))
        filter_param = CPgSqlParam(filter_sql,
                                   params=(self.start_time, self.ent_time, tuple(self.ports), tuple(self.ports)))
        filter_res_list = json.loads(CFunction.execute(filter_param))
        mlog.info("通联 len：{}".format(len(filter_res_list)))

        return filter_res_list

    def get_netflow_data(self):
        filter_sql = '''
            SELECT
                name,--0
                type,--1
                start_time,--2
                end_time,--3
                sip,--4
                src_region,--5
                src_operator,--6
                src_iot,--7
                src_service,--8
                src_unit,--9
                sport,--10
                src_com,--11
                dip,--12
                dst_region,--13
                dst_operator,--14
                dst_iot,--15
                dst_service,--16
                dst_unit,--17
                dport,--18
                dst_com,--19
                bytes_all,--20
                bytes_all_down,--21
                packets_all,--22
                packets_all_down,--23
                service_info,--24
                uuid,--25
                flow_logs,--26
                analysis_tech,--27
                port_distribution--28
            FROM internal_app_bsa_gjk.netflow_alert_5min
            WHERE end_time >= %s and end_time <= %s AND dport IN %s;
                    '''
        mlog.info("netflow filter_sql：{}".format(filter_sql))
        filter_param = CPgSqlParam(filter_sql,
                                   params=(self.start_time, self.ent_time, tuple(self.ports),))
        filter_res_list = json.loads(CFunction.execute(filter_param))
        mlog.info("netflow len：{}".format(len(filter_res_list)))

        # 查询数据库,获取所有下载类型的邮件类型数据
        return filter_res_list

    def get_event_data(self):
        """
        获取事件
        """
        filter_sql = '''
                    WITH related_alerts AS (
                        SELECT 
                        event_id,
                        UNNEST(STRING_TO_ARRAY(related_alerts, ';')) AS alert_uuid,
                        report_type
                        FROM 
                            internal_app_bsa_gjk.continuous_events
                        WHERE  
                            end_time >= %s AND end_time <= %s
                    )
                    SELECT 
                        ce.event_id,--0
                        ce.src_ip,--1
                        ce.src_region,--2
                        ce.src_operator,--3
                        ce.src_com,--4
                        ce.src_info,--5
                        ce.src_threat_mark,--6
                        ce.dst_ip,--7
                        ce.dst_region,--8
                        ce.dst_operator,--9
                        ce.dst_com,--10
                        ce.dst_info,--11
                        ce.dst_threat_mark,--12
                        ce.start_time,--13
                        ce.end_time,--14
                        ce.status,--15
                        ce.event_type,--16
                        ce.up_bytesall,--17
                        ce.down_bytesall,--18
                        ce.judge_status,--19
                        ce.judge_info,--20
                        ce.judge_file,--21
                        ce.iot_tag,--22
                        ce.key_service_tag,--23
                        ce.key_unit_tag,--24
                        ce.report_type,--25
                        ce.related_alerts,--26
                        ce.analysis_tech,--27
                        ce.uuid,--28
                        ce.reverse_tag,--29
                        ce.related_alerts_count,--30
                        ce.key_unit,--31
                        ce.app_type,--32
                        -- 聚合通联告警信息
		                ARRAY_AGG(a1.sport) AS tclog_sport,--33
		                ARRAY_AGG(a1.dport) AS tclog_dport,--34
		                -- 聚合Netflow告警信息
                        ARRAY_AGG(a2.dport) AS netflow_dport--35
                    FROM 
                        internal_app_bsa_gjk.continuous_events ce
                    LEFT JOIN 
                        related_alerts r ON ce.event_id = r.event_id
                    LEFT JOIN 
                        internal_app_bsa_gjk.traffic_alert a1 ON r.alert_uuid = a1.uuid AND r.report_type = 1 -- 通联告警
                    LEFT JOIN 
                        internal_app_bsa_gjk.netflow_alert_5min a2 ON r.alert_uuid = a2.uuid AND r.report_type <> 1  -- Netflow告警
                    WHERE
                        -- 根据 flow_logs 做 LIKE 搜索
                            a1.sport IN %s OR a1.dport IN %s OR a2.dport IN %s
                    GROUP BY 
                        ce.event_id  -- 根据事件分组
                    HAVING  
                        ce.end_time >= %s AND ce.end_time <= %s;

                    '''
        mlog.info("事件 filter_sql：{}".format(filter_sql))
        filter_param = CPgSqlParam(filter_sql, params=(
            self.start_time, self.ent_time, tuple(self.ports), tuple(self.ports), tuple(self.ports), self.start_time,
            self.ent_time))
        filter_res_list = json.loads(CFunction.execute(filter_param))
        mlog.info("事件 len：{}".format(len(filter_res_list)))

        return filter_res_list

    # def event_is_port(self, filter_data, report_type):
    #     """
    #     如果目标端口在其中，就命中
    #     """
    #     for item in filter_data:
    #         if not item:
    #             continue
    #         flow_logs_list = [json.loads(i) for i in item.split("|")]
    #         for flow_detail in flow_logs_list:
    #             if report_type == 1 and item[1] in self.download_type:
    #                 port = flow_detail.get("sport", "")
    #             else:
    #                 port = flow_detail.get("dport", "")
    #             protocol = flow_detail.get("protocol", "").upper()
    #
    #             if port in self.ports and protocol == self.proto:
    #                 return True, port
    #
    #     return False, 0
    def handle_port(self, event_item, report_type):
        if report_type == 1 and event_item[16] in self.download_type:
            port = ",".join([str(p) for p in event_item[33] if p and p != -1])
        elif report_type == 1 and event_item[16] not in self.download_type:
            port = ",".join([str(p) for p in event_item[34] if p and p != -1])
        else:
            port = ",".join([str(p) for p in event_item[35] if p and p != -1])
        return port

    def export_event(self, event_data):
        event_title = ["事件ID",
                       "源IP", "源地理位置", "源运营商", "源备案单位", "源信息", "源标签",
                       "目的IP", "目的地理位置", "目的运营商", "目的备案单位", "目的信息", "目的标签",
                       "开始时间", "结束时间", "事件状态", "事件类型", "上行/流出总流量", "下行/流入总流量",
                       "事件研判状态", "研判信息", "研判附件", "物联网标记", "重点数据服务标记", "重点单位标记",
                       "报告类型", "关联告警uuid", "检测手段", "UUID", "是否了转换源目信息", "告警次数",
                       "重点单位", "应用类型", "命中端口"]
        result_event_data = [event_title]
        for event_item in event_data:
            report_type = event_item[25]

            port = self.handle_port(event_item, report_type)

            str_src_info = event_item[5].split("&") if event_item[5] else []
            str_dst_info = event_item[11].split("&") if event_item[11] else []
            if str_src_info:
                str_src_info = list(set(str_src_info))
            if str_dst_info:
                str_dst_info = list(set(str_dst_info))
            # 构造导出的数据
            result_event_data.append([
                event_item[0], event_item[1], '"{}"'.format(event_item[2]), '"{}"'.format(event_item[3]),
                '"{}"'.format(event_item[4]), '"{}"'.format('&'.join(str_src_info)),
                '"{}"'.format('&'.join(str_dst_info)),
                event_item[7], '"{}"'.format(event_item[8]), '"{}"'.format(event_item[9]),
                '"{}"'.format(event_item[10]), '"{}"'.format(event_item[11]), event_item[12],
                time_trans.timestamp2format(event_item[13]),
                time_trans.timestamp2format(event_item[14]), STATUS_MAP.get(str(event_item[15]), ""),
                EVENT_TYPE_MAP.get(str(event_item[16]), ""), bytes_trans.long2unit(event_item[17]),
                bytes_trans.long2unit(event_item[18]),
                JUDGE_STATUS_MAP.get(str(event_item[19]), ""), event_item[20].replace("\n", "&"), event_item[21],
                event_item[22], event_item[23], event_item[24],
                REPORT_TYPE.get(str(event_item[25]), ""), event_item[26],
                ANALYSIS_TECH_MAP.get(str(event_item[27]), ""), event_item[28], event_item[29], event_item[30],
                event_item[31], event_item[32], '"{}"'.format(port)
            ])
        # 文件路径
        time_format = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime(time.time()))
        output_file = '端口_持续性事件_{}.csv'.format(time_format)

        # 打开文件并写入 CSV 内容
        with open(output_file, 'wb') as csvfile:
            # 创建 CSV writer 对象
            csvwriter = csv.writer(csvfile, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
            # 写入数据
            for row in result_event_data:
                csvwriter.writerow(row)
        print('持续性事件 导出csv完成')

    def export_tclog_netflow_data(self, tclog_data, netflow_data):
        # 导出数据
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.freeze_panes = 'A2'  # 冻结第一行
        sheet.auto_filter.ref = 'A1:AZ1'  # 增加筛选
        sheet.title = u"通联日志异常告警"
        sheet.append(
            ['告警类型', '告警类型ID', '开始时间', '结束时间',
             '源IP', '源地理位置', '源运营商', '源物联网信息', '源数据服务', '源单位', '源端口', '源备案单位',
             '目的IP', '目的地理位置', '目的运营商', '目的物联网信息', '目的数据服务', '目的单位', '目的端口',
             '目的备案单位',
             '流出流量', '流入流量', '上行包数', '下行包数', '流量列表', 'UUID', '原始日志', '检测手段', '端口分布'])
        # 向工作表中添加数据
        for row in tclog_data:
            if row[1] in self.download_type:
                port = row[10]
            else:
                port = row[18]
            if port not in self.ports:
                continue
            if str(row[26]).endswith(SUFFIX):
                flow_logs = ""
            else:
                flow_logs = ",\n".join(row[26].split("|"))
            sheet.append([
                row[0], row[1], time_trans.timestamp2format(row[2]), time_trans.timestamp2format(row[3]),
                row[4], '"{}"'.format(row[5]), '"{}"'.format(row[6]), row[7], row[8], row[9], row[10], row[11],
                row[12], '"{}"'.format(row[13]), '"{}"'.format(row[14]), row[15], row[16], row[17], row[18], row[19],
                bytes_trans.long2unit(row[20]), bytes_trans.long2unit(row[21]), row[22], row[23],
                '"{}"'.format(row[24]), row[25], flow_logs, ANALYSIS_TECH_MAP.get(str(row[27]), ""),
                PORT_DISTRIBUTION_MAP.get(row[28], "")
            ])

        # 导出数据
        sheet2 = workbook.create_sheet(title=u"netflow异常告警")
        sheet2.freeze_panes = 'A2'  # 冻结第一行
        sheet2.auto_filter.ref = 'A1:AZ1'  # 增加筛选
        sheet2.append(
            ['告警类型', '告警类型ID', '开始时间', '结束时间',
             '源IP', '源地理位置', '源运营商', '源物联网信息', '源数据服务', '源单位', '源端口', '源备案单位',
             '目的IP', '目的地理位置', '目的运营商', '目的物联网信息', '目的数据服务', '目的单位', '目的端口',
             '目的备案单位',
             '流出流量', '流入流量', '上行包数', '下行包数', '服务信息', 'UUID', '原始日志', '检测手段', '端口分布']
        )
        # 向工作表中添加数据
        for row2 in netflow_data:
            flow_logs = ",\n".join(row2[26].split("|"))
            sheet2.append([
                row2[0], row2[1], time_trans.timestamp2format(row2[2]), time_trans.timestamp2format(row2[3]),
                row2[4], '"{}"'.format(row2[5]), '"{}"'.format(row2[6]), row2[7], row2[8], row2[9], row2[10],
                row2[11],
                row2[12], '"{}"'.format(row2[13]), '"{}"'.format(row2[14]), row2[15], row2[16], row2[17], row2[18],
                row2[19],
                bytes_trans.long2unit(row2[20]), bytes_trans.long2unit(row2[21]), row2[22], row2[23],
                '"{}"'.format(row2[24]), row2[25], flow_logs, ANALYSIS_TECH_MAP.get(str(row2[27]), ""),
                PORT_DISTRIBUTION_MAP.get(row2[28], "")
            ])

        # 假设 output_file 是你要导出文件的路径和文件名，例如 'output.xlsx'
        time_format = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime(time.time()))
        output_file = '端口_异常告警_{}.xlsx'.format(time_format)
        # 保存工作簿到指定文件路径
        workbook.save(output_file)
        print('异常告警 导出xlsx完成')

    def run(self):
        # 根据时间范围获取所有事件
        s1 = time.time()
        event_data = self.get_event_data()
        print("获取持续性事件数据结束，共耗时：{}s".format(time.time() - s1))

        # 开始导出持续性事件
        s2 = time.time()
        self.export_event(event_data)
        print("导出持续性事件数据结束，共耗时：{}s".format(time.time() - s2))

        # 获取通联告警数据
        s3 = time.time()
        tclog_data = self.get_tclog_data()
        # 获取netflow告警数据
        netflow_data = self.get_netflow_data()
        print("获取通联和netflow数据结束，共耗时：{}s".format(time.time() - s3))

        # 开始导出通联和netflow数据
        s4 = time.time()
        self.export_tclog_netflow_data(tclog_data, netflow_data)
        print("导出通联和netflow数据结束，共耗时：{}s".format(time.time() - s4))


if __name__ == "__main__":
    s = time.time()
    parser = argparse.ArgumentParser(usage="python export_csv_by_ports.py",
                                     description="")
    parser.add_argument("--ports", help="端口；逗号分隔", default="1005,1224")
    parser.add_argument("--proto", help="协议", default="TCP")
    args = parser.parse_args()
    if not all([args.ports, args.proto]):
        mlog.error("参数错误")
    else:
        ports = args.ports.split(',')
        ports = [int(i) for i in ports]
        obj = ExportCsvByPorts(ports, args.proto)
        obj.run()
    mlog.info("脚本执行结束，共耗时：{}s".format(time.time() - s))
