# coding:utf-8
"""
global redis client for tat
"""
import redis

# constant used by redis client
SQL_STATUS = 'tat_fm_spark_sql_status'
FM_ACTIVE_JOBS = 'tat_fm_active_spark_jobs'

# redis conf file
_REDIS_CONF_FILE = '/home/<USER>/redis/conf/redis.conf'


def _get_redis_conf():
    """
    get localhost redis port and password
    """
    port = '6379'
    password = None
    with open(_REDIS_CONF_FILE, 'r') as f:
        lines = f.readlines()
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                continue
            elif line.startswith('port'):
                port = line.split(' ')[1]
            elif line.startswith('requirepass'):
                password = line.split(' ')[1]
    return port, password

_redis_port, _redis_pwd = _get_redis_conf()
# global redis client
REDIS_CLIENT = redis.Redis(host='localhost', port=_redis_port, password=_redis_pwd, db=2)
