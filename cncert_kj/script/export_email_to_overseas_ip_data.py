#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: export_email_to_overseas_ip_data.py
# @time: 2024/10/25 12:22
# @desc:
"""
多个单位的邮箱向一个境外ip流出数据的情况
"""

import csv
import json
import os
import sys
import time

from cncert_kj.conf.constant import CONF_PATH
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.lib.json_utils import JsonTools
from cncert_kj.models import type_conf_model
from cncert_kj.utils import bytes_trans
from cncert_kj.utils import logger

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('export_email_to_overseas_ip_data')


class ExportEmailToOverseasIpData(object):
    """
    需求简介
    1、pop    *******
        pop2   *******
        这种情况  ，就把这两条数据导出来
        pop    *******
        pop2   *******
        pop3   *******
        这种情况  ，就把pop    ******* 和 pop3   ******* 这两条数据导出来
        pop    *******
        pop2   *******
        pop3   *******
        这种情况  ，一条数据都不导出来，
    3、输出：源ip、目的ip、单位名称、源ip备案单位、目的ip备案单位、流入流量、流出流量、协议
    4、输出csv文件时，同一个目的ip要放在一起

    逻辑简述
    1、查询通联和netflow告警表的数据
    2、只取上传下载类型的告警
    3、根据告警类型来判定告警是上传还是下载；下载sip 命中境外ip，上传dip命中境外ip
    4、根据传入的邮件类型，命中端口，只要dport在命中的端口里，就是符合要求的数据
    """

    def __init__(self):
        self.ent_time = int(time.time())
        # 默认查询7天
        self.start_time = self.ent_time - 3600 * 24 * 7
        self.json_tool = JsonTools()
        self.ports = self.get_port()
        self.ports_str = ','.join("'{0}'".format(x) for x in self.ports)
        self.upload_type = self.get_upload_type()
        self.download_type = self.get_download_type()
        self.type_list = self.upload_type + self.download_type
        self.type_list_str = ','.join("'{0}'".format(x) for x in self.type_list)

    def get_port(self):
        """
        根据邮件类型获取对应的端口
        """
        common_conf = self.json_tool.read_file(os.path.join(CONF_PATH, "app_type_conf.json"))
        ports = [int(k) for k, v in common_conf.items() if v["app_type_name"] == "邮件类应用"]
        return ports

    def get_upload_type(self):
        """
        获取上传告警类型
        """
        upload_type = []
        type_conf_list = type_conf_model.get_type_conf()
        for item in type_conf_list:
            if "上传" in item[1]:
                upload_type.append(int(item[2]))
        return upload_type

    def get_download_type(self):
        """
        获取下载告警类型
        """
        download_type = []
        type_conf_list = type_conf_model.get_type_conf()
        for item in type_conf_list:
            if "下载" in item[1]:
                download_type.append(int(item[2]))
        return download_type

    def get_app_type(self, dport):
        """
        根据端口获取应用类型
        """
        app_type = ""
        if not dport:
            return app_type
        common_conf = self.json_tool.read_file(os.path.join(CONF_PATH, "app_type_conf.json"))
        app_type = common_conf.get(str(dport)).get("app_protocol_name") if common_conf.get(str(dport)) else ""
        return app_type

    def get_tclog_data(self):
        # 通联的需注意。下载类型的 sip匹配目标ip
        if not self.ports:
            mlog.info("没有找到对应的邮件类型，请检查输入的邮件类型是否正确")
            return []
        # 查询数据库,获取所有上传下载类型的邮件类型数据

        filter_sql = '''
            SELECT 
               start_time,
               end_time,
               sip,
               src_region,
               src_operator,
               sport,
               src_unit,
               src_com,
               dip,
               dst_region,
               dst_operator,
               dport,
               dst_unit,
               dst_com,
               up_bytes_all,
               down_bytes_all,
               up_packets_all,
               down_packets_all,
               dport,
               type
            FROM internal_app_bsa_gjk.traffic_alert
            WHERE dport IN %s AND type IN %s AND start_time >= %s and end_time <= %s;
            '''
        mlog.info("通联filter_sql：{}".format(filter_sql))
        filter_param = CPgSqlParam(filter_sql,
                                   params=(tuple(self.ports), tuple(self.type_list), self.start_time, self.ent_time))
        filter_res_list = json.loads(CFunction.execute(filter_param))
        return filter_res_list

    def get_netflow_data(self):
        if not self.ports:
            mlog.info("没有找到对应的邮件类型，请检查输入的邮件类型是否正确")
            return []
        # 查询数据库,获取所有上传类型的邮件类型数据

        filter_sql = '''
            SELECT 
               start_time,
               end_time,
               sip, --2
               src_region,
               src_operator,
               sport,
               src_unit,--6
               src_com,--7
               dip,--8
               dst_region,
               dst_operator,
               dport,
               dst_unit,--12
               dst_com,--13
               bytes_all,--14
               bytes_all_down,--15
               packets_all,
               packets_all_down,
               dport,
               type
            FROM internal_app_bsa_gjk.netflow_alert_5min
            WHERE dport IN %s AND type IN %s AND start_time >= %s and end_time <= %s;
            '''
        mlog.info("netflow filter_sql：{}".format(filter_sql))
        filter_param = CPgSqlParam(filter_sql,
                                   params=(tuple(self.ports), tuple(self.type_list), self.start_time, self.ent_time))
        filter_res_list = json.loads(CFunction.execute(filter_param))
        # 查询数据库,获取所有下载类型的邮件类型数据
        return filter_res_list

    def run(self):
        # 获取符合条件的通联数据
        tclog_data = self.get_tclog_data()
        netflow_data = self.get_netflow_data()
        data = tclog_data + netflow_data
        # 将目标ip一样的数据放在一起
        data_map = {}
        for item in data:
            # 只有同一目的ip不同的邮箱类型，且总数大于1才导出
            if item[8] not in data_map:
                data_map[item[8]] = {item[11]: [
                    item[2],
                    item[7],
                    item[6],
                    item[8],
                    item[13],
                    item[12],
                    bytes_trans.long2unit(item[14]),
                    bytes_trans.long2unit(item[15]),
                    self.get_app_type(item[18])
                ]}
            else:
                if item[11] not in data_map[item[8]]:
                    data_map[item[8]][item[11]] = [
                        item[2],
                        item[7],
                        item[6],
                        item[8],
                        item[13],
                        item[12],
                        bytes_trans.long2unit(item[14]),
                        bytes_trans.long2unit(item[15]),
                        self.get_app_type(item[18])
                    ]
        # 构造导出的数据
        title = ["源IP", "源备案系统", "源单位名称", "目的IP", "目的备案系统", "目的单位名称", "流出流量", "流入流量",
                 "应用类型"]
        result = [title]
        for v in data_map.values():
            for item in v.values():
                result.append(item)

        # 文件路径
        time_format = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime(time.time()))
        output_file = 'email_{}.csv'.format(time_format)

        # 打开文件并写入 CSV 内容
        with open(output_file, 'wb') as csvfile:
            # 创建 CSV writer 对象
            csvwriter = csv.writer(csvfile, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
            # 写入数据
            for row in result:
                csvwriter.writerow(row)

        print('CSV file written successfully!')


if __name__ == "__main__":
    obj = ExportEmailToOverseasIpData()
    obj.run()
