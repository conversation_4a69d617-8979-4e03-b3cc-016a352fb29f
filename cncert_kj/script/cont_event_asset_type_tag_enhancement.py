#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: cont_event_asset_type_tag_enhancement.py
# @time: 2025/8/11 13:53
# @desc:
"""
持续性事件境内外标签增强资产类型
"""
import django
import argparse
import json
import os
import sys
import time
import traceback

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from collections import defaultdict
from django.db import transaction
from datetime import datetime, timedelta

from cncert_kj.models.base_model import ContinuousEventsTagModel, ContinuousEvents, AssetTypeModel, AssetInfoModel
from cncert_kj.utils import logger
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('cont_event_asset_type_tag_enhancement')


class ContinuousEventAssetTypeTagEnhancement(object):
    def __init__(self):
        self.time_format = DATE_TIME_FORMAT
        self.start_time = None
        self.end_time = None

    def print_time_zone(self, start_timestamp, end_timestamp):
        start_of_yesterday_dt = datetime.fromtimestamp(start_timestamp)
        end_of_yesterday_dt = datetime.fromtimestamp(end_timestamp)
        start_of_yesterday_str = start_of_yesterday_dt.strftime(self.time_format)
        end_of_yesterday_str = end_of_yesterday_dt.strftime(self.time_format)
        mlog.info("本次任务运行查询的时间区间：{}--{}".format(start_of_yesterday_str, end_of_yesterday_str))

    def get_time_zone(self):
        """
        获取运行区间
        :return:
        """
        now = datetime.now()
        start_of_yesterday = now - timedelta(days=1)
        start_of_yesterday = start_of_yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_yesterday = start_of_yesterday + timedelta(days=1) - timedelta(seconds=1)
        timestamp_start_of_yesterday = int(time.mktime(start_of_yesterday.timetuple()))
        timestamp_end_of_yesterday = int(time.mktime(end_of_yesterday.timetuple()))
        return timestamp_start_of_yesterday, timestamp_end_of_yesterday

    def get_event_data(self, event_ids):
        mlog.info("开始获取事件")
        try:
            if event_ids:
                events = ContinuousEvents.objects.filter(event_id__in=event_ids).all()

            else:
                events = ContinuousEvents.objects.filter(end_time__range=(self.start_time, self.end_time)).all()

            mlog.info("获取事件结束")
            return events
        except Exception as e:
            mlog.exception("获取事件失败：{}".format(e))
            return []

    def get_asset_type(self, ips):

        mlog.info("开始获取ip对应的资产类型")

        # 获取资产类型映射对象
        asset_type_mapping_relationship = self.get_asset_type_mapping_relationship()

        asset_type_map = defaultdict(set)
        if not ips:
            return asset_type_map
        queryset = AssetInfoModel.objects.filter(asset_ip__in=ips).values_list("asset_ip", "asset_type")
        for asset_ip, asset_type in queryset:
            asset_type_map[asset_ip].add(asset_type_mapping_relationship.get(asset_type, ""))
        mlog.info("获取ip对应的资产类型结束")
        return asset_type_map

    def get_asset_type_mapping_relationship(self):
        mlog.info("开始获取资产类型映射关系")
        asset_type_mapping_relationship = dict(AssetTypeModel.objects.filter().values_list("id", "asset_type"))
        mlog.info("获取资产类型映射关系结束")
        return asset_type_mapping_relationship

    def get_asset_type_en_mapping(self):
        asset_type_en_mapping = dict(AssetTypeModel.objects.filter().values_list("asset_type", "asset_type_en"))
        return asset_type_en_mapping

    def handle_event_tag(self, src_ip_map, dst_ip_map, asset_type_map):
        mlog.info("开始处理事件标签")

        src_data = {}
        dst_data = {}

        src_not_fount_num = 0
        dst_not_fount_num = 0

        for event_id, src_ip in src_ip_map.items():
            asset_type = asset_type_map.get(src_ip)
            if not asset_type:
                # mlog.debug("未找到资产类型：{}".format(src_ip))
                src_not_fount_num += 1
                continue
            src_data[event_id] = asset_type

        for event_id, dst_ip in dst_ip_map.items():
            asset_type = asset_type_map.get(dst_ip)
            if not asset_type:
                # mlog.debug("未找到资产类型：{}".format(dst_ip))
                dst_not_fount_num += 1
                continue
            dst_data[event_id] = asset_type
        mlog.info("境内未找到资产类型数量：{}".format(src_not_fount_num))
        mlog.info("境外未找到资产类型数量：{}".format(dst_not_fount_num))

        mlog.info("处理事件标签结束")

        return src_data, dst_data

    def handle_save_data(self, src_data, dst_data):

        mlog.info("开始处理写入数据")
        asset_type_en_mapping = self.get_asset_type_en_mapping()

        result_data = []
        for event_id, asset_types in src_data.items():
            if len(asset_types) > 5:
                mlog.debug("事件{}的资产类型数量{}; 大于5:".format(event_id, len(asset_types)))
            for asset_type in asset_types:
                tag_content = asset_type.rstrip("专题")
                tag_name = asset_type_en_mapping.get(asset_type)
                result_data.append(
                    ContinuousEventsTagModel(
                        event_id=event_id,
                        tag_name=tag_name,
                        tag_type=1,
                        tag_content=tag_content
                    )
                )

        for event_id, asset_types in dst_data.items():
            for asset_type in asset_types:
                tag_content = asset_type.rstrip("专题")
                tag_name = asset_type_en_mapping.get(asset_type)
                result_data.append(
                    ContinuousEventsTagModel(
                        event_id=event_id,
                        tag_name=tag_name,
                        tag_type=2,
                        tag_content=tag_content
                    )
                )
        mlog.info("处理写入数据结束")
        return result_data

    def save_data(self, result_data, event_ids):
        mlog.info("开始写入数据:{}".format(len(result_data)))
        asset_type_en_mapping = self.get_asset_type_en_mapping()
        tag_name_list = list(asset_type_en_mapping.values())

        with transaction.atomic():
            ContinuousEventsTagModel.objects.filter(event_id__in=event_ids, tag_name__in=tag_name_list).delete()
            ContinuousEventsTagModel.objects.bulk_create(result_data, batch_size=1000)
        mlog.info("写入数据结束")

    def run(self, event_ids=None):
        # 1、获取事件数据
        events = self.get_event_data(event_ids)

        mlog.info("事件数量：{}".format(len(events)))

        # 2、获取ip集合，境内外ip，事件id对象
        ips = set()
        src_ip_map = {}
        dst_ip_map = {}
        event_ids = set()

        for queryset in events:
            src_ip_map[queryset.event_id] = queryset.src_ip
            dst_ip_map[queryset.event_id] = queryset.dst_ip
            ips.add(queryset.src_ip)
            event_ids.add(queryset.event_id)

        # 3、获取ip对应的资产类型
        asset_type_map = self.get_asset_type(ips)

        # 4、处理境内外事件标签
        src_data, dst_data = self.handle_event_tag(src_ip_map, dst_ip_map, asset_type_map)

        # 5、构造写入pg数据格式
        result_data = self.handle_save_data(src_data, dst_data)

        # 6、写入pg
        self.save_data(result_data, event_ids)


if __name__ == '__main__':
    """
    1、默认查前一天的，示例：2025-08-10 00:00:00--2025-08-10 23:59:59
        python cont_event_asset_type_tag_enhancement.py
    2、指定时间范围增强所有类型事件
        python cont_event_asset_type_tag_enhancement.py -s 1747652400 -e 1747745495
    """
    try:
        mlog.info("=================开始任务=================")
        st = int(time.time())
        parser = argparse.ArgumentParser()
        parser.add_argument("--start_time", "-s", help="开始时间戳", type=int)
        parser.add_argument("--end_time", "-e", help="结束时间戳", type=int)
        args = parser.parse_args()
        mlog.info("参数:start_time：{}-end_time：{}".format(args.start_time, args.end_time))

        obj = ContinuousEventAssetTypeTagEnhancement()

        if all([args.start_time, args.end_time]):
            obj.start_time = args.start_time
            obj.end_time = args.end_time
        else:
            obj.start_time, obj.end_time = obj.get_time_zone()

        obj.print_time_zone(obj.start_time, obj.end_time)

        obj.run()

        mlog.info("=================结束任务:耗时：{}=================".format(int(time.time()) - st))

    except Exception as e:
        mlog.exception("事件标签增强资产类型失败：{}".format(e))
        mlog.error(traceback.format_exc())
