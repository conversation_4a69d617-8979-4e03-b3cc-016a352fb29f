#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
monitor_script_cmd.py巡检脚本root用户下执行后通过 database_middle_script.sh执行的巡检
"""
import datetime
import json
import logging
import os
import subprocess
import time
import traceback

import paramiko
import requests
from mlogging import TimedRotatingFile<PERSON><PERSON>ler_MP

from cncert_kj.conf.constant import MALICIOUS_TAG_IP, MALICIOUS_TAG_TOKEN
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.script.check_ddos_event import timestamp_to_utc_str
from cncert_kj.script.do_excel_group_by_province import CertSSHClient
from cncert_kj.utils import es_utils
from cncert_kj.utils.conf_util import CommonConf, NETFLOW_IP_5MIN, NETFLOW_IP_DAY, NETFLOW_IP_PAIR_5MIN, TLLOG_IP_5MIN, \
    TLLOG_IP_DAY, TLLOG_IP_PAIR_5MIN
from cncert_kj.utils.oauth_server import OAuthUtil
from cncert_kj.utils.request_asset import RequestAsset
from cncert_kj.utils.request_nti import NtiUtil
from cncert_kj.utils.time_trans import timestamp2format, DATE_TIME_FORMAT

# 告警/事件最大结束时间SQL
DATA_BASE = [
    {
        "TcLog_Alarm": "SELECT end_time FROM internal_app_bsa_gjk.traffic_alert ORDER BY end_time  DESC LIMIT 1"
    },
    {
        "NetFlow_Alarm": "SELECT end_time FROM internal_app_bsa_gjk.netflow_alert_5min ORDER BY end_time  DESC LIMIT 1"
    },
    {
        "TcLog_Continuous_Event": "SELECT end_time FROM internal_app_bsa_gjk.continuous_events WHERE report_type = 1 ORDER BY end_time DESC LIMIT 1"
    },
    {
        "NetFlow_Continuous_Event": "SELECT end_time FROM internal_app_bsa_gjk.continuous_events WHERE report_type = 2 ORDER BY end_time DESC LIMIT 1"
    },
    {
        "TcLog_Big_Flow_Event": "SELECT end_time FROM  internal_app_bsa_gjk.tclog_daily_agg_big_datas ORDER BY end_time  DESC limit  1"
    },
    {
        "NetFlow_Big_Flow_Event": "SELECT end_time FROM  internal_app_bsa_gjk.netflow_daily_agg_big_datas ORDER BY end_time  DESC limit  1"
    }
]

data_count = {
    "TcLog_Alarm": "SELECT count(1) FROM internal_app_bsa_gjk.traffic_alert WHERE end_time > %s and end_time < %s",
    "NetFlow_Alarm": "SELECT count(1) FROM internal_app_bsa_gjk.netflow_alert_5min WHERE end_time > %s and end_time < %s",
    "TcLog_Continuous_Event": "SELECT count(1) FROM internal_app_bsa_gjk.continuous_events WHERE end_time > %s and end_time < %s AND report_type=1",
    "NetFlow_Continuous_Event": "SELECT count(1) FROM internal_app_bsa_gjk.continuous_events WHERE end_time > %s and end_time < %s AND report_type=2"
}
# 查询到的报文检测任务状态
BWJC_STATUS = {
    0: "未审核",
    1: "预审通过",
    2: "审核通过",
    3: "未通过",
    4: "已生效",
    5: "下发失败",
    6: "数据不存在"
}

# 查询到的留存检索任务状态
LCJS_STATUS = {
    0: "已发送",
    1: "运行中",
    2: "暂停中",
    3: "已完成",
    6: "操作数据不存在"
}
IP = "************"
VERIFY = False
TC_NEW_TIME = "\t\t通联最新执行时间"
NETFLOW_NEW_TIME = "\t\tNetFlow最新执行时间"


def init_logger(logfile="monitor"):
    """
    获取日志句柄的方法
    """
    logger = logging.getLogger(logfile)
    logger.setLevel(logging.DEBUG)
    logroot = "/home/<USER>/ISOP/apps/cncert_kj/logs"
    if not os.path.exists(logroot):
        os.mkdir(logroot)
    filehandle = TimedRotatingFileHandler_MP(os.path.normpath(logroot + "/" + logfile + ".log"), 'midnight')
    filehandle.suffix = "%Y-%m-%d"
    filehandle.setLevel(logging.DEBUG)
    consolehandle = logging.StreamHandler()
    consolehandle.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(message)s')
    filehandle.setFormatter(formatter)
    consolehandle.setFormatter(formatter)
    logger.addHandler(filehandle)
    logger.addHandler(consolehandle)
    return logger


mlog = init_logger("monitor")


def print_table(array):
    # 计算每列的最大宽度
    col_widths = [max(len(str(item)) for item in col) for col in zip(*array)]

    # 格式化表格的行
    def format_row(row_):
        return " | ".join("{:{}}".format(item, col_widths[idx]) for idx, item in enumerate(row_))

    # 打印表头
    header = array[0]
    mlog.info(format_row(header))
    mlog.info("-+-".join("-" * width for width in col_widths))
    # 打印数据行
    for row in array[1:]:
        mlog.info(format_row(row))


def get_disk_usage_percentage(path="/"):
    """
    获取本地磁盘根目录使用比例
    """
    try:
        IP = os.environ.get("IP", "***********")
        process = subprocess.Popen(["df", path], stdout=subprocess.PIPE)
        output = process.communicate()[0]
        lines = output.split("\n")
        fields = lines[1].split()
        usage_percentage = fields[4]
        mlog.info("SYSTEM: {}; \t目录: {} \tUsed: {}".format(IP, path, usage_percentage))
    except Exception as _:
        mlog.info("检索磁盘使用信息失败")
        mlog.error("An error occurred: {}".format(traceback.format_exc()))


def get_remote_disk_usage_percentage(hostname, username, password, path):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)

        stdin, stdout, stderr = ssh.exec_command("df " + path)
        output = stdout.read()

        ssh.close()
        lines = output.split("\n")
        fields = lines[1].split()
        usage_percentage = fields[4]
        mlog.info("SYSTEM: {}; \t目录: {} \tUsed: {}".format(hostname, path, usage_percentage))
    except Exception as _:
        mlog.info("{}:检索磁盘使用信息失败".format(hostname))
        mlog.error("An error occurred: {}".format(traceback.format_exc()))


def monitor_disk():
    try:
        path_ = "/"
        # 本地
        get_disk_usage_percentage(path_)

        pg_conf = CommonConf().postgresql_conf()
        hostname_ = pg_conf.get("host")
        username_ = pg_conf.get("username")
        password_ = pg_conf.get("password")
        path__ = "/home/<USER>"
        # 数据库环境
        if all([hostname_, username_, password_]):
            get_remote_disk_usage_percentage(hostname_, username_, password_, path__)
        else:
            mlog.info("没有额外的数据库环境")
    except Exception as e:
        mlog.error(e)


def pg_select(data_base):
    try:
        data = [["DATA_BASE", "MAX_END_TIME", "24H_COUNT"]]
        now_time = int(time.time())
        for item in data_base:
            key = item.keys()[0]
            value = item.values()[0]
            result = json.loads(CFunction.execute(CPgSqlParam(value)))
            if not result:
                max_end_time = "无数据"
            else:
                max_end_time = timestamp2format(result[0][0])

            data_count_sql = data_count.get(key)
            # 最近24小时开始结束时间戳
            begin_time_24h, end_time_24h = now_time - 86400, now_time
            count_24h = ""
            if data_count_sql:
                count_24h = json.loads(
                    CFunction.execute(CPgSqlParam(data_count_sql, params=(begin_time_24h, end_time_24h))))
                if count_24h:
                    count_24h = count_24h[0][0]

            data.append([key, max_end_time, count_24h])
        print_table(data)
    except Exception as e:
        mlog.error(e)


class CheckDockingSystem:
    def __init__(self):
        """
        对接系统巡检，这几个系统最近增强上的事件是什么时候
        """
        self.oauth_util = OAuthUtil()

    def csemp_system(self):
        # 获取规则任务ID
        bwjc_sql = "SELECT rule_id FROM	internal_app_bsa_gjk.bwjc_table WHERE	is_delete = 0	AND rule_id IS NOT NULL ORDER BY end_time DESC LIMIT 1;"
        lcjs_sql = "SELECT task_id FROM internal_app_bsa_gjk.retrieval_task WHERE is_delete = 0 AND task_id != '' AND task_id is not NULL ORDER BY id DESC LIMIT 1; "
        # 报文监测
        msg1 = ""
        try:
            bwjc_res = CFunction.execute(CPgSqlParam(bwjc_sql))
            bwjc_id = json.loads(bwjc_res)[0][0]
            bwjc = self.oauth_util.get_message_detection_state([bwjc_id])
            status = bwjc[0].get("status", "")
            status = BWJC_STATUS.get(status)
            if status:
                bwjc[0]["status"] = status
                msg1 += "CSEMP系统 - 报文监测 -  系统正常 任务状态:{}; ".format(status)
            else:
                msg1 += "CSEMP系统 - 报文监测 -  系统异常 未查询到任务状态; "

        except Exception as e:
            msg1 += "CSEMP系统 - 报文监测 -  系统异常 : {}; ".format(e)
        msg2 = ""
        # 留存检索
        try:
            lcjs_res = CFunction.execute(CPgSqlParam(lcjs_sql))
            lcjs_id = json.loads(lcjs_res)[0][0]
            lcjs = self.oauth_util.get_retrieval_task_status([lcjs_id])
            status = lcjs[0].get("status", "")
            status = LCJS_STATUS.get(status)
            if status:
                lcjs[0]["status"] = status
                msg2 += "CSEMP系统 - 留存检索 -  系统正常 任务状态:{}".format(status)
            else:
                msg2 += "CSEMP系统 - 留存检索 -  系统异常 未查询到任务状态; "

        except Exception as e:
            msg2 += "CSEMP系统 - 留存检索 -  系统异常 :{}".format(e)

        mlog.info(msg1)
        mlog.info(msg2)

    def intelligence_system(self):
        msg = ""
        try:
            res = NtiUtil().search_many(["**************", "**************", "**************"])
            result = json.loads(res)
            if result.get("objects"):
                msg += "情报系统  - 系统正常;"
            else:
                msg += "情报系统  - 系统正常 未查询到情报信息;"
        except Exception as e:
            msg += "情报系统  - 系统异常  {};".format(e)
        # 查询脚本最近一次执行完成的时间
        conf_sql = "SELECT conf_value, conf_key FROM internal_app_bsa_gjk.continuous_events_conf  WHERE conf_key='tclog_threat_nti_timestamp' or conf_key = 'threat_nti_timestamp' "
        event_res = json.loads(CFunction.execute(CPgSqlParam(conf_sql)))
        if event_res:
            for i in event_res:
                conf_value = i[0]
                conf_key = i[1]
                if conf_key == "tclog_threat_nti_timestamp":
                    msg += "{}  ：{}".format(TC_NEW_TIME, timestamp2format(conf_value))
                else:
                    msg += "{} ：{}".format(NETFLOW_NEW_TIME, timestamp2format(conf_value))
        mlog.info(msg)

    def malicious_intelligence_system(self):
        msg = ""
        try:
            ip_list = ["**************", "**************", "**************"]
            url = "http://{}/ipcenter/ip/getTagList".format(MALICIOUS_TAG_IP)
            form_header = {"Content-Type": "application/json", "token": MALICIOUS_TAG_TOKEN}
            data = {
                "ip": ip_list
            }
            res = requests.post(url=url, json=data, headers=form_header, verify=VERIFY)

            if res.status_code == 200:
                msg += "恶意情报系统  - 系统正常;"
            else:
                msg += "恶意情报系统  - 系统异常 code：{};".format(res.status_code)
        except Exception as e:
            msg += "恶意情报系统  - 系统异常  {};".format(e)
        # 查询脚本最近一次执行完成的时间
        conf_sql = "SELECT conf_value, conf_key FROM internal_app_bsa_gjk.continuous_events_conf  WHERE conf_key='tclog_malicious_tag_timestamp' or conf_key='netflow_malicious_tag_timestamp'"
        event_res = json.loads(CFunction.execute(CPgSqlParam(conf_sql)))

        if event_res:
            for i in event_res:
                conf_value = i[0]
                conf_key = i[1]
                if conf_key == "tclog_malicious_tag_timestamp":
                    msg += "{}：{} ".format(TC_NEW_TIME, timestamp2format(conf_value))
                else:
                    msg += " {}：{}".format(NETFLOW_NEW_TIME, timestamp2format(conf_value))

        mlog.info(msg)

    def asset_system(self):
        msg = ""
        try:
            ip_ = "**************"
            with RequestAsset() as req_asset:
                res_data = req_asset.do_asset_url(ip_)
            if res_data:
                code_ = res_data.get("code")
                asset_data = res_data.get("data", {})
                if asset_data:
                    data = res_data.get("data", "")
                    # 有数据，系统正常
                    if data:
                        msg += "资产系统  - 系统正常 code:{}".format(code_)
                else:
                    msg += "资产系统  - 未搜索到资产信息：{}  code:{}".format(json.dumps(res_data, ensure_ascii=False), code_)
            else:
                # 如果没有搜索到数据，说明系统异常，搜索失败
                msg += "资产系统  - 系统异常 资产搜索失败"

        except Exception as e:
            msg += "资产系统  - 系统异常  {}".format(e)
        # 查询脚本最近一次执行完成的时间
        conf_sql = "SELECT conf_value, conf_key FROM internal_app_bsa_gjk.continuous_events_conf  WHERE conf_key='tclog_event_details_asset_timestamp' or conf_key = 'event_details_asset_timestamp' "
        event_res = json.loads(CFunction.execute(CPgSqlParam(conf_sql)))
        if event_res:
            for i in event_res:
                conf_value = i[0]
                conf_key = i[1]
                if conf_key == "tclog_event_details_asset_timestamp":
                    msg += "{}：{}".format(TC_NEW_TIME, timestamp2format(conf_value))
                else:
                    msg += "{}：{}".format(NETFLOW_NEW_TIME, timestamp2format(conf_value))
        mlog.info(msg)

    def record_system(self):
        ip_ = "**************"
        try:
            common_conf = CommonConf()
            visit_record_unit = common_conf.get_visit_record_unit()
            api = visit_record_unit.get("api")
            token = visit_record_unit.get("token")
            sql = "SELECT src_ip FROM internal_app_bsa_gjk.continuous_events ORDER BY end_time DESC LIMIT 1;"
            res = json.loads(CFunction.execute(CPgSqlParam(sql)))
            if res:
                ip_ = res[0][0]
            if not ip_:
                ip_ = "**************"

            params_ = {
                "ipLocation": ip_,
                "token": token
            }
            data = requests.get(api, params=params_)

            if data.status_code == 200:
                mlog.info("备案系统  - 查询IP:{}, 系统正常  系统状态:{}".format(ip_, data.status_code))
            else:
                mlog.info("备案系统  - 查询IP:{} 系统状态:{}".format(ip_, data.status_code))
        except Exception as e:
            mlog.error("备案系统  - 查询IP:{} :{}".format(ip_, e))

    def ddos_system(self):
        msg = "DDOS系统  - "
        try:
            sql = """
                SELECT event_id,src_ip,dst_ip,start_time,end_time,dst_info,judge_info,src_info 
                FROM internal_app_bsa_gjk.continuous_events
                WHERE event_id = 
                (select event_id from "internal_app_bsa_gjk"."continuous_events_tag"  where  "tag_name" = 'ddos' AND "tag_type" = '2' AND "tag_content" LIKE '%DDOS%' ORDER BY "update_time" DESC limit 1) """
            result = CFunction.execute(CPgSqlParam(sql))
            res = json.loads(result)
            if not res:
                msg += "未发现DDOS事件"
                mlog.info(msg)
                return

            headers = {
                # "WebapiAccessToken": token,  # 正式
                # "WebapiAccessToken": "05a13153a1934ca7adad4c4f5b31bb14",      # 测试
                "Content-Type": "application/json"
            }

            url = CommonConf().common_conf.get("ddos_api", '')  # 正式环境
            token = CommonConf().common_conf.get("ddos_api_token", '')  # 正式环境

            headers.update({"AUTHORIZATION": token})
            dst_ip = res[0][2]
            start_time = res[0][3]
            end_time = res[0][4]
            begin_time, end_time = timestamp_to_utc_str(start_time, end_time)
            data = {
                "begin_time": begin_time,
                "end_time": end_time,
                # "src_ip": [i[1]],
                # "src_ip": [],
                "dst_ip": [dst_ip]
            }
            response = requests.post(url, json=data, verify=VERIFY, headers=headers)
            msg += "状态码： {}  ".format(response.status_code)
            if response.status_code == 200:
                text = json.loads(response.text)

                if text and isinstance(text, list):
                    msg += "系统正常"
                else:
                    msg += "返回为空: {}".format(text)
            else:
                msg += "系统异常"
        except Exception as e:
            msg += "系统异常 :{}".format(e)
        mlog.info(msg)

    def run(self):
        self.csemp_system()
        self.record_system()
        self.intelligence_system()
        self.malicious_intelligence_system()
        self.asset_system()
        self.ddos_system()


def get_max_end_time():
    """
    获取每个索引中最大的end_time

    过滤24小时前----2037年1月1日的
    添加时间:2025年6月25日11点21分
    TODO 一个月后删掉这部分逻辑  【过滤24小时前----2037年1月1日的】
    :return:
    """
    try:
        etime = int(time.time())
        stime = etime - 86400
        od_end_time = etime + 7 * 24 * 60 * 60  # 2030年1月1日
        es = es_utils.ESUtil()
        data = {
            "query": {
                "range": {
                    "end_time": {
                        "lt": od_end_time
                    }
                }
            },
            "size": 0,
            "aggregations": {
                "end_time": {
                    "max": {
                        "field": "end_time"
                    }
                }
            }
        }
        index_list = [
            NETFLOW_IP_5MIN,
            NETFLOW_IP_DAY,
            NETFLOW_IP_PAIR_5MIN,
            TLLOG_IP_5MIN,
            TLLOG_IP_DAY,
            TLLOG_IP_PAIR_5MIN
        ]

        count_dsl = {
            "query": {
                "range": {
                    "end_time": {
                        "gte": stime,
                        "lt": etime
                    }
                }
            }
        }
        result = [["INDEX", "MAX_END_TIME", "NOW_TIME", "24H_COUNT"]]
        for index in index_list:
            res = es.search_es(index, data)
            count = es.search_count(index, count_dsl)
            end_time = res.get("aggregations", {}).get("end_time", {}).get("value", 0)
            now_date = datetime.datetime.now().strftime(DATE_TIME_FORMAT)
            result.append([index, timestamp2format(end_time), now_date, count])
        print_table(result)
    except Exception as e:
        mlog.error(e)


# 获取昨天的日期
YESTERDAY = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")


def check_yesterday_file_size():
    """
        检查昨天文件的大小
    """
    try:
        sftp = CertSSHClient()

        mlog.info("昨天日期:{}".format(YESTERDAY))
        csv_file_sizes = {}
        json_file_size = {}
        for i in sftp.province_dic.values():
            province_dir_path = os.path.join(sftp.ssh_conf["ssh_base_path"], i.encode("utf-8"))
            file_list = sftp.get_file_list(province_dir_path)
            for file in file_list:
                file_path = os.path.join(province_dir_path, file)
                attributes = sftp.get_file_stat(file_path)
                if (file.split(".")[0]).endswith(YESTERDAY):
                    file_size = attributes.st_size
                    if file.endswith(".csv"):
                        csv_file_sizes[file_path] = file_size
                    elif file.endswith(".json"):
                        json_file_size[file_path] = file_size
        not_csv_file_sizes(csv_file_sizes)
        not_json_file_size(json_file_size)
    except Exception as e:
        mlog.error(e)


def not_csv_file_sizes(csv_file_sizes):
    if not csv_file_sizes:
        mlog.info("SCRIPT:do_excel_group_by_province.py:\t\t{},未推送事件".format(YESTERDAY))
    else:
        str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},事件推送异常,都为空".format(YESTERDAY)
        for i in csv_file_sizes.values():
            if i != 0:
                str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},事件推送正常".format(YESTERDAY)
                break
        mlog.info(str_)


def not_json_file_size(json_file_size):
    if not json_file_size:
        mlog.info("SCRIPT:do_excel_group_by_province.py:\t\t{},未推送原始日志".format(YESTERDAY))
    else:
        str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},原始日志推送异常,都为空".format(YESTERDAY)
        for i in json_file_size.values():
            if i != 0:
                str_ = "SCRIPT:do_excel_group_by_province.py:\t\t{},原始日志推送正常".format(YESTERDAY)
                break
        mlog.info(str_)


def run_task():
    """
    运行中的任务
    :return:
    """
    try:
        username = 'u_wa_wa1c_crossborderanomaly_nsfocus'
        password = '92d7ddd2a010c59511dc2905b7e14f64'
        url = 'http://{}:8088/ws/v1/cluster/apps'.format(IP)
        params = {'state': 'RUNNING', 'queue': 'root.wa1c.wa_wa1c_crossborderanomaly_realtime', "user.name": username}
        auth = (username, password)
        response = requests.get(url, params=params, auth=auth)
        task_name_list = []
        task_run_count = {}
        application = {}
        result = [["Task_name", "Start Time", "State", "Application ID"]]
        if response.status_code == 200:
            data = response.json()
            # 处理返回的JSON数据
            for app in data.get('apps', {}).get('app', []):
                task_name_list.append(app['name'])
                application[app['name']] = app['id']
                if task_run_count.get(app['name']):
                    task_data = task_run_count.get(app['name'])
                    task_data = task_data + 1
                    task_run_count[app['name']] = task_data
                else:
                    task_run_count[app['name']] = 1
                id_ = app['id']
                start_time = timestamp2format(app['startedTime'] / 1000)
                state = app['state']
                name = app['name']
                result.append([name, start_time, state, id_])
        print_table(result)
    except Exception as e:
        mlog.error(e)


def trace_task():
    """
    溯源系统运行中的任务
    :return:
    """
    try:
        username = 'u_wa_wa1c_ddosdetection_nsfocus'
        password = 'cd9cf295bcf4dc14c24490f8fe4c911'
        url = 'http://{}:8088/ws/v1/cluster/apps'.format(IP)
        params = {'state': 'RUNNING', 'queue': 'root.wa1c.wa_ddosdetection_realtime', "user.name": username}
        auth = (username, password)
        response = requests.get(url, params=params, auth=auth)
        task_name_list = []
        task_run_count = {}
        application = {}
        result = [["Task_name", "Start Time", "State", "Application ID"]]
        if response.status_code == 200:
            data = response.json()
            # 处理返回的JSON数据
            for app in data.get('apps', {}).get('app', []):
                task_name_list.append(app['name'])
                application[app['name']] = app['id']
                if task_run_count.get(app['name']):
                    task_data = task_run_count.get(app['name'])
                    task_data = task_data + 1
                    task_run_count[app['name']] = task_data
                else:
                    task_run_count[app['name']] = 1
                id_ = app['id']
                start_time = timestamp2format(app['startedTime'] / 1000)
                state = app['state']
                name = app['name']
                result.append([name, start_time, state, id_])
        print_table(result)
    except Exception as e:
        mlog.error(e)


def table_monitor():
    try:
        sql = """SELECT table_schema || '.' || table_name AS table_full_name, pg_size_pretty(pg_total_relation_size('"' || table_schema || '"."' || table_name || '"')) AS size FROM information_schema.tables where table_schema  = 'internal_app_bsa_gjk' ORDER BY pg_total_relation_size('"' || table_schema || '"."' || table_name || '"') DESC;"""
        result = json.loads(CFunction.execute(CPgSqlParam(sql)))
        data = [["TABLE_NAME", "SIZE"]]
        for i in result:
            data.append([i[0], i[1]])
        print_table(data)
    except Exception as e:
        mlog.error(e)


if __name__ == '__main__':
    """
    告警和事件结束时间最大的一条数据
    """
    mlog.info("【检测磁盘使用信息】")
    monitor_disk()
    mlog.info("【告警/事件最大结束时间-NOW_TIME:{}】".format(datetime.datetime.now().strftime(DATE_TIME_FORMAT)))
    pg_select(DATA_BASE)
    # 接入系统巡检
    mlog.info("【接入系统巡检】")
    check_yesterday_file_size()
    CheckDockingSystem().run()
    mlog.info("【获取每个索引中最大的结束时间】")
    get_max_end_time()
    mlog.info("【运行中的大数据任务】")
    run_task()
    mlog.info("【溯源系统运行中的大数据任务】")
    trace_task()
    mlog.info("【表占用大小信息】")
    table_monitor()
    mlog.info("\n\n\n\n\n")
