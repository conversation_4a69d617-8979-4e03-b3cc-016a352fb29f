# coding:utf-8
import cStringIO
import codecs
import csv
import datetime
import hashlib
import io
import json
import os
import sys
import time
import traceback
from collections import OrderedDict
from io import BytesIO
from urllib import quote

from audit.audit import Audit
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.http import StreamingHttpResponse
from rest_framework.decorators import list_route

from cncert_kj.lib import chardet
from cncert_kj.lib.docx import Document
from cncert_kj.lib.docx.enum.text import WD_ALIGN_PARAGRAPH
from cncert_kj.lib.docx.shared import Pt
from cncert_kj.lib.docxtpl import DocxTemplate
from cncert_kj.models import cont_event_models, custom_tag_models, netflow_alert_models, tllog_alert_models
from cncert_kj.models.base_model import ContinuousEventsTagModel, ContinuousEvents, IpCountModel, AssetInfoModel
from cncert_kj.models.cont_event_models import get_department_data, get_event_info, get_port_traffic, \
    is_valid_domain, ContinuousEventsModel, count_ones_and_consecutive_ones, convert_seconds_to_dhms, ContEventExport
from cncert_kj.models.static_baseline_model import convert_to_bytes
from cncert_kj.utils import logger, time_trans, bytes_trans, net
from cncert_kj.utils.conf_util import CommonConf, json_tool, CONF_PATH, KEY_DB_SERVICE_MAP, KEY_SERVICE_MAP, \
    JUDGE_STATUS_MAP, STATUS_MAP, ANALYSIS_TECH_MAP, ANALYSIS_TECH_MAP_REVERSE, STATUS_MAP_REVERSE, \
    get_globa_white_list, get_globa_black_list, REPORT_TYPE, TIME_DISTRIBUTION_MAP, event_other_tag_name
from cncert_kj.utils.csv_util import UnicodeWriter
from cncert_kj.utils.flow_log_util import SUFFIX
from cncert_kj.utils.params_validate import is_private_ip
from cncert_kj.utils.request_filing_sys import RequestFilingSys
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT
from cncert_kj.utils.visit_record_unit import VisitAPI
from cncert_kj.utils.word_util import export_word

reload(sys)
sys.setdefaultencoding('utf-8')
auditLog = Audit()
GET_METHOD = "GET"
POST_METHOD = "POST"
DELETE_METHOD = "DELETE"

APPENDIX_LOCAL_PATH = "/home/<USER>/cont_event_appendix"

JUDGE_STATUS_MAP_REVERSE = {v: k for k, v in JUDGE_STATUS_MAP.items()}

mlog = logger.init_logger('cont_event_views')

CONTENT_TYPE = 'text/json'
HOST_ERROR_MSG = 'host error,request failed'


class BaseContEvent:
    dst_port_msg = u"目的端口动态变化"
    dst_port_msg2 = "目的端口分散"
    dst_port_msg3 = "目的端口为{}"
    date_msg = "{}月{}日"
    ddos_template_name = "关于某单位对外发起DDOS事件的报告"
    export_success_msg = "导出持续性事件报告:{} 成功"
    output_filename_msg = "关于{}数据异常出境事件的报告"
    export_error_msg = "download docx report failed"
    id_format_msg = "'{0}'"


def add_event(request):
    try:
        data = request.body
        if not data:
            return HttpResponse("param error,request failed", status=500, )
        json_data = json.loads(data)
        event_list = json_data.get("event_list", [])
        if not event_list:
            return HttpResponse("param error,request failed", status=500, )
        for event_info in event_list:
            cont_event_models.add_event(event_info=event_info)
        auditLog(request, "事件新增成功")
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def parse_csv_file(file):
    original_file_data = file.read()
    file_encode = chardet.detect(original_file_data).get('encoding')
    file_data = original_file_data.decode(file_encode).encode('utf-8')
    rows = csv.reader(io.StringIO(unicode(file_data)), delimiter=',')
    return list(rows)


def build_event_info(row):
    conf_util = CommonConf()
    EVENT_TYPE_MAP = conf_util.get_continuous_event_type_reverse()
    EVENT_TYPE_MAP_REVERSE = conf_util.get_continuous_event_type()

    # 兼容status、judge_status、event_type的中文输入
    status = resolve_field(row[2], STATUS_MAP, STATUS_MAP_REVERSE)
    judge_status = resolve_field(row[3], JUDGE_STATUS_MAP, JUDGE_STATUS_MAP_REVERSE)
    event_type = resolve_field(row[4], EVENT_TYPE_MAP, EVENT_TYPE_MAP_REVERSE)
    analysis_tech = resolve_field(row[5], ANALYSIS_TECH_MAP, ANALYSIS_TECH_MAP_REVERSE)

    event_info = {
        "start_time": time_trans.format_execl_timestamp(row[0]),
        "end_time": time_trans.format_execl_timestamp(row[1]),
        "status": status,
        "judge_status": judge_status,
        "event_type": event_type,
        "analysis_tech": analysis_tech,
        "up_bytesall": row[6][0:-2] if row[5] else 0,
        "down_bytesall": row[7][0:-2] if row[6] else 0,
        "src_ip": row[8],
        "src_region": row[9],
        "src_operator": row[10],
        "src_com": row[11],
        "src_info": row[12],
        "src_threat_mark": row[13],
        "dst_ip": row[14],
        "dst_region": row[15],
        "dst_operator": row[16],
        "dst_com": row[17],
        "dst_info": row[18],
        "dst_threat_mark": row[19],
        "judge_info": row[20]
    }

    return event_info


def resolve_field(field_value, numeric_map, text_map):
    str_value = str(field_value)
    if str_value.isdigit() and str_value in numeric_map:
        return str_value
    return text_map.get(str_value, 0)


def add_csv(request):
    try:
        file = request.FILES.get('file')
        event_info_list = parse_csv_file(file)

        for row in event_info_list[1:]:
            event_info = build_event_info(row)
            cont_event_models.add_event(event_info=event_info)
        auditLog(request, "事件导入成功")
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


# ================= filter辅助函数 =================
def _mark_ip_info(event_info, global_white_list, global_black_list):
    """
    标记源/目的IP是否属于白名单或黑名单，并返回处理后的 src_info, dst_info
    """
    str_src_info = event_info.get("src_info", "").split("&") if event_info.get("src_info", "") else []
    str_dst_info = event_info.get("dst_info", "").split("&") if event_info.get("dst_info", "") else []
    if event_info.get("src_ip") in global_white_list:
        str_src_info.append("源IP属于白名单")
    if event_info.get("dst_ip") in global_white_list:
        str_dst_info.append("目的IP属于白名单")
    if event_info.get("src_ip") in global_black_list:
        str_src_info.append("源IP属于黑名单")
    if event_info.get("dst_ip") in global_black_list:
        str_dst_info.append("目的IP属于黑名单")
    # 去重
    str_src_info = list({i for i in str_src_info if i})
    str_dst_info = list({i for i in str_dst_info if i})
    return str_src_info, str_dst_info


def _parse_app_type(event_info):
    """
    解析应用类型字段，去重
    """
    app_type = event_info.get("app_type", "").split("&") if event_info.get("app_type", "") else []
    return list(set(app_type)) if app_type else []


def _parse_port_str(port_str):
    """
    解析端口字符串，去除首尾中括号和逗号
    """
    if port_str:
        return port_str[1:-1].strip(",")
    return ""


def _get_ip_count(ip, statistics_map):
    """
    获取IP统计数量，默认1
    """
    return statistics_map.get(ip, 1) if ip else 0


def _get_flow_surge_tag(event_info):
    """
    处理流量突增标签
    """
    flow_surge_str = event_info.get("flow_surge", "").split("&")[0] if event_info.get("flow_surge", "") else ""
    total_ones, consecutive_ones = count_ones_and_consecutive_ones(flow_surge_str)
    return "突增{}次，连续突增{}次".format(total_ones, consecutive_ones) if total_ones > 0 else ""


def _get_flow_continue_time_str(event_info):
    """
    处理流量持续时间字符串
    """
    flow_continue_time = event_info.get("flow_continue_time", 0)
    return convert_seconds_to_dhms(flow_continue_time)


def _parse_custom_tags(event_info, custom_tag_map):
    """
    解析自定义标签
    """
    custom_tag_list = []
    if event_info.get("custom_tags"):
        custom_tag_id_list = event_info.get("custom_tags", "").split(',')
        for custom_tag_id in custom_tag_id_list:
            if custom_tag_map.get(custom_tag_id):
                custom_tag_info = {
                    "id": int(custom_tag_id),
                    "name": custom_tag_map.get(custom_tag_id)
                }
                custom_tag_list.append(custom_tag_info)
    return custom_tag_list


def _parse_threat_mark(mark_str):
    """
    解析威胁标签
    """
    return [i for i in mark_str.split(" ") if i] if mark_str else []


def _build_event_res(event_info, event_type_menu, custom_tag_map, src_ip_statistics_map, dst_ip_statistics_map,
                     global_white_list, global_black_list):
    """
    构建单条事件的响应数据
    """
    str_src_info, str_dst_info = _mark_ip_info(event_info, global_white_list, global_black_list)
    app_type = _parse_app_type(event_info)
    src_port_str = _parse_port_str(event_info.get("src_port", ""))
    dst_port_str = _parse_port_str(event_info.get("dst_port", ""))
    up_bytesall = bytes_trans.long2unit(event_info.get("up_bytesall", 0))
    down_bytesall = bytes_trans.long2unit(event_info.get("down_bytesall", 0))
    src_to_dst_ip_count = _get_ip_count(event_info.get("src_ip"), src_ip_statistics_map)
    dst_to_src_ip_count = _get_ip_count(event_info.get("dst_ip"), dst_ip_statistics_map)
    flow_surge_tag = _get_flow_surge_tag(event_info)
    flow_continue_time_str = _get_flow_continue_time_str(event_info)
    custom_tag = _parse_custom_tags(event_info, custom_tag_map)
    src_threat_mark = _parse_threat_mark(event_info.get("src_threat_mark", ""))
    # 组装结果
    event_res = {
        "event_id": event_info.get("event_id"),
        "start_time": time_trans.timestamp2format(event_info.get("start_time", 0)),
        "end_time": time_trans.timestamp2format(event_info.get("end_time", 0)),
        "event_name": event_type_menu.get(str(event_info.get("event_type", 0)), str(event_info.get("event_type", 0))),
        "event_type": int(event_info.get("event_type", 0)),
        "judge_status": int(event_info.get("judge_status", 0)),
        "report_type": int(event_info.get("report_type", 0)),
        "analysis_tech": event_info.get("analysis_tech"),
        "custom_tag": custom_tag,
        "src_ip": event_info.get("src_ip"),
        "src_region": event_info.get("src_region", ""),
        "src_com": event_info.get("src_com", ""),
        "src_info": str_src_info,
        "src_operator": event_info.get("src_operator", ""),
        "src_threat_mark": src_threat_mark,
        "dst_ip": event_info.get("dst_ip"),
        "dst_region": event_info.get("dst_region", ""),
        "dst_com": event_info.get("dst_com", ""),
        "dst_info": str_dst_info,
        "dst_operator": event_info.get("dst_operator", ""),
        "dst_threat_mark": [],
        "bytesall": [
            {"label": "流出", "value": up_bytesall},
            {"label": "流入", "value": down_bytesall}
        ],
        "related_alerts_count": event_info.get("related_alerts_count", 0),
        "status": int(event_info.get("status", 0)),
        "reverse_tag": event_info.get("reverse_tag", ""),
        "key_unit": event_info.get("key_unit") if event_info.get("key_unit") else "",
        "app_type": app_type,
        "ipv6": "是" if event_info.get("ipv6") else "否",
        "judge_info": event_info.get("judge_info", "").replace("\n", "&"),
        "port_distribution": event_info.get("port_distribution", ""),
        "src_port_str": src_port_str,
        "dst_port_str": dst_port_str,
        "src_to_dst_ip_count": src_to_dst_ip_count,
        "dst_to_src_ip_count": dst_to_src_ip_count,
        "work_time_tag": TIME_DISTRIBUTION_MAP.get(event_info.get("work_time_tag", 0), ""),
        "flow_surge": flow_surge_tag,
        "flow_continue_time": flow_continue_time_str,
    }
    return event_res


# ================= filter主接口 =================
def filter(request):
    try:
        cee = ContEventExport()
        # 1. 解析参数
        params_dic, global_white_list, global_black_list = cee.parse_export_params(request)
        # >>
        # cem_obj = ContinuousEventsModel()
        # 查询总数
        # total_event = cem_obj.get_event_filter_count(params_dic)
        # mlog.info("total_event:{}".format(total_event))

        # 添加排序、分页参数
        st = time.time()
        params_dic.update(
            {"order": request.GET.get("order", default=""),
             "order_column": request.GET.get("order_column", default=""),
             "page": int(request.GET.get('page', default="1")),
             "limit": int(request.GET.get('limit', default="10")),
             "is_order": True,
             })

        # event_list = cem_obj.get_event_filter(**params_dic)
        # 2. 查询数据
        event_list, src_ip_statistics_map, dst_ip_statistics_map, total_event = cee.query_event_data(params_dic)
        mlog.info("添加排序与分页后查询事件耗时： {}".format(time.time() - st))

        # 整理数据
        filter_res = {
            "code": 200,
            "msg": "ok",
            "data": {
                "total": 0,
                "alert_list": []
            }
        }
        filter_res["data"]["total"] = total_event
        t2 = time.time()
        event_type_menu = CommonConf().get_continuous_event_type_reverse()
        t3 = time.time()
        mlog.info("获取事件类型枚举event_type_menu耗时:{}秒".format(t3 - t2))
        custom_tag_map = cee.get_custom_tag_map()
        t4 = time.time()
        mlog.info("获取自定义标签custom_tag_map耗时:{}秒".format(t4 - t3))
        t5 = time.time()

        for event_info in event_list:
            event_res = _build_event_res(
                event_info, event_type_menu, custom_tag_map,
                src_ip_statistics_map, dst_ip_statistics_map,
                set(global_white_list), set(global_black_list)
            )
            filter_res["data"]["alert_list"].append(event_res)
        t6 = time.time()
        mlog.info("数据处理耗时:{}秒".format(t6 - t5))
        json_str = json.dumps(filter_res)
        return HttpResponse(json_str, status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def get_ip_info(event_info, global_white_list, global_black_list):
    def process(ip, base_info, label):
        info = base_info.split("&") if base_info else []
        if ip in global_white_list:
            info.append(u"%sIP属于白名单" % label)
        if ip in global_black_list:
            info.append(u"%sIP属于黑名单" % label)
        return list(set(info))

    src_info = process(event_info.get("src_ip"), event_info.get("src_info", ""), u"源")
    dst_info = process(event_info.get("dst_ip"), event_info.get("dst_info", ""), u"目的")
    return src_info, dst_info


def get_traffic_distribution(event_info, src_ip_statistics_map, dst_ip_statistics_map):
    src_ip = event_info.get("src_ip", "")
    dst_ip = event_info.get("dst_ip", "")
    src_count = src_ip_statistics_map.get(src_ip, 1)
    dst_count = dst_ip_statistics_map.get(dst_ip, 1)
    return src_count, dst_count


def get_custom_tags(event_info, custom_tag_map):
    tags = []
    if event_info.get("custom_tags"):
        for tag_id in event_info.get("custom_tags", "").split(","):
            if tag_id in custom_tag_map:
                tags.append(custom_tag_map.get(tag_id))
    return u";".join(tags)


def get_flow_surge_tag(event_info):
    flow_str = event_info.get("flow_surge", "")
    if flow_str:
        base_str = flow_str.split("&")[0]
        total, consecutive = count_ones_and_consecutive_ones(base_str)
        if total > 0:
            return u"突增%d次，连续突增%d次" % (total, consecutive)
    return ""


def generate_event_row(event_info, global_white_list, global_black_list,
                       event_type_map, custom_tag_map,
                       src_ip_statistics_map, dst_ip_statistics_map):
    src_info, dst_info = get_ip_info(event_info, global_white_list, global_black_list)
    tag_str = get_custom_tags(event_info, custom_tag_map)
    flow_surge = get_flow_surge_tag(event_info)
    src_count, dst_count = get_traffic_distribution(event_info, src_ip_statistics_map, dst_ip_statistics_map)

    # 流量持续时间
    flow_continue_time = event_info.get("flow_continue_time", 0)
    flow_continue_time_str = convert_seconds_to_dhms(flow_continue_time)

    return [
        time_trans.timestamp2format(event_info.get("start_time", 0)),
        time_trans.timestamp2format(event_info.get("end_time", 0)),
        STATUS_MAP.get(str(event_info.get("status", 0)), ""),
        JUDGE_STATUS_MAP.get(str(event_info.get("judge_status", 0)), ""),
        REPORT_TYPE.get(str(event_info.get("report_type", 0)), ""),
        event_type_map.get(str(event_info.get("event_type", 0)), ""),
        ANALYSIS_TECH_MAP.get(str(event_info.get("analysis_tech", 0)), ""),
        bytes_trans.long2unit(event_info.get("up_bytesall", 0)),
        bytes_trans.long2unit(event_info.get("down_bytesall", 0)),
        event_info.get("src_ip"),
        event_info.get("src_region", ""),
        '"{}"'.format(event_info.get("src_operator", "")),
        event_info.get("src_com", ""),
        '"{}"'.format('&'.join(src_info)),
        event_info.get("dst_ip"),
        '"{}"'.format(event_info.get("dst_region", "")),
        '"{}"'.format(event_info.get("dst_operator", "")),
        event_info.get("dst_com", ""),
        '"{}"'.format('&'.join(dst_info)),
        event_info.get("judge_info", "").replace("\n", "&"),
        event_info.get("port_distribution", "") if event_info.get("port_distribution", "") else "",
        '"{}"'.format(event_info.get("app_type", "")) if event_info.get("app_type", "") else "",
        '"{}"'.format(event_info.get("src_port", "").strip(',').replace(",", "、")) if event_info.get(
            "src_port", "") else "",
        '"{}"'.format(event_info.get("dst_port", "").strip(',').replace(",", "、")) if event_info.get(
            "dst_port", "") else "",
        '"{}"'.format(src_count),
        '"{}"'.format(dst_count),
        '"{}"'.format(tag_str) if tag_str else "",
        '"{}"'.format(event_info.get("related_alerts_count", 0)),
        '"{}"'.format(TIME_DISTRIBUTION_MAP.get(event_info.get("work_time_tag", 0), "")),
        '"{}"'.format(flow_surge) if flow_surge else "",
        '"{}"'.format(flow_continue_time_str) if flow_continue_time_str else ""
    ]


# 定义一个生成器函数用于流式传输数据
def event_data_generator(event_list, global_white_list, global_black_list, event_type_map, custom_tag_map,
                         src_ip_statistics_map, dst_ip_statistics_map):
    # 先输出表头
    yield codecs.BOM_UTF8
    output = cStringIO.StringIO()
    writer = UnicodeWriter(output)
    # 使用 unicode() 转换字符串（适用于 Python 2）
    writer.writerow(
        [u"开始时间", u"结束时间", u"流量持续状态", u"事件处置状态", u"事件来源", u"事件名称", u"检测手段",
         u"上行/流出总流量",
         u"下行/流入总流量", u"源IP", u"源地理位置", u"源运营商", u"源备案单位", u"源标签", u"目的IP", u"目的地理位置",
         u"目的运营商", u"目的备案单位", u"目的标签", u"备注", u"端口分布", u"应用类型", u"境内端口", u"境外端口",
         u"境内IP通联分布", u"境外IP通联分布",
         u"自定义标签", u"告警次数", u"时间分布", u"流量特征", u"流量持续时间"])

    # 如果event_list为空，返回空数据
    if not event_list:
        return
    batch_size = 1000
    total_count = len(event_list)
    for offset in range(0, total_count, batch_size):
        batch_data = event_list[offset: offset + batch_size]
        for event_info in batch_data:
            row = generate_event_row(event_info, global_white_list, global_black_list,
                                     event_type_map, custom_tag_map,
                                     src_ip_statistics_map, dst_ip_statistics_map)

            writer.writerow(row)
        output.seek(0)
        yield output.read()
        output.truncate(0)  # 清空缓存
        output.seek(0)  # 复位指针


def download_csv_files(request):
    try:
        # >>
        # s = int(time.time())
        # conf_util = CommonConf()
        # EVENT_TYPE_MAP = conf_util.get_continuous_event_type_reverse()
        # start_time = request.GET.get('start_time')
        # end_time = request.GET.get('end_time')
        # if not start_time:
        #     current_time = datetime.datetime.now()
        #     one_year_ago = current_time - datetime.timedelta(days=365)
        #     start_time = one_year_ago.strftime(DATE_TIME_FORMAT)
        # if not end_time:
        #     current_time = datetime.datetime.now()
        #     end_time = current_time.strftime(DATE_TIME_FORMAT)
        # start_time = time_trans.format2timestamp(start_time)
        # end_time = time_trans.format2timestamp(end_time)
        # sip = request.GET.get('sip', default="")
        # sip = '' if sip == 'undefined' else sip
        # dip = request.GET.get('dip', default="")
        # _type = request.GET.get('type', default="")
        # service_type = request.GET.get('service_type', default="")
        #
        # status = request.GET.get('status', default="")
        # judge_status = request.GET.get('judge_status', default="")
        # tag = request.GET.get('tag', default="99")
        # hit_whitelist = request.GET.get('hit_whitelist', default="")
        # hit_blacklist = request.GET.get('hit_blacklist', default="")
        # types = _type.split(",") if _type else []
        # service_types = service_type.split(",") if service_type else []
        #
        # custom_tags_str = request.GET.get('custom_tags', default="")
        # report_types_str = request.GET.get('report_types', default="")
        #
        # src_unit = request.GET.get('src_unit', default="")
        # src_region = request.GET.get("src_region", default="")
        # dst_region = request.GET.get("dst_region", default="")
        #
        # analysis_tech = request.GET.get('analysis_tech', default="")
        # # 在源信息中搜索重点单位
        # src_info_unit = request.GET.get('src_info_unit', default="")
        # # 行业类型
        # departments = request.GET.get('departments', default="")
        # # 源信息
        # src_info = request.GET.get('src_info', default="")
        # # 目的信息
        # dst_info = request.GET.get('dst_info', default="")
        # # 境内标签类别
        # src_tag_name = request.GET.get('src_tag_name', default="")
        # # 境外标签类别
        # dst_tag_name = request.GET.get('dst_tag_name', default="")
        #
        # # 应用类型
        # app_type = request.GET.get('app_type', default="")
        # # 是否存在应用类型
        # is_app_type = request.GET.get('is_app_type', default="")
        #
        # # 排序方式
        # order = request.GET.get('order', default="")
        # # 排序字段
        # order_column = request.GET.get('order_column', default="")
        #
        # # 源备案单位
        # src_com = request.GET.get('src_com', default="")
        # # 目的备案单位
        # dst_com = request.GET.get('dst_com', default="")
        #
        # # 源运营商
        # src_operator = request.GET.get('src_operator', default="")
        # # 目的运营商
        # dst_operator = request.GET.get('dst_operator', default="")
        #
        # # ipv6
        # ipv6 = request.GET.get('ipv6', default="")
        #
        # # 事件id
        # event_id = request.GET.get('event_id', default="")
        #
        # # 端口分布
        # post_distribution = request.GET.get('post_distribution', default="")
        # # 境内端口
        # src_port = request.GET.get('src_port', default="")
        # # 境外端口
        # dst_port = request.GET.get('dst_port', default="")
        # # ip类别
        # category_name = request.GET.get('category_name', default="")
        #
        # # 获取全局黑白名单
        # global_white_list = get_globa_white_list()
        # global_black_list = get_globa_black_list()
        #
        # white_list_str = ""
        #
        # black_list_str = ""
        #
        # min_up_bytesall = request.GET.get("min_up_bytesall")
        # max_up_bytesall = request.GET.get("max_up_bytesall")
        # min_up_bytesall_unit = request.GET.get("min_up_bytesall_unit")
        # max_up_bytesall_unit = request.GET.get("max_up_bytesall_unit")
        # # ip通联分布
        # ip_tl_distribution = request.GET.get('ip_tl_distribution')
        # # 工作时间标记：1为工作时间，2为非工作时间，3为不是工作时间也不是非工作时间
        # work_time_tag = int(request.GET.get('work_time_tag', 0))
        #
        # # 流量特征（流量突增）：
        # is_flow_surge = request.GET.get('is_flow_surge', default="")
        #
        # # 流量持续时间
        # flow_continue_time_min = request.GET.get('flow_continue_time_min')
        # flow_continue_time_max = request.GET.get('flow_continue_time_max')
        # flow_continue_time_tuple = (flow_continue_time_min, flow_continue_time_max)
        # # 用于数据库异常场景筛选境内数据库，境外数据库，1是境内，2是境外
        #
        # asset_region = request.GET.get('asset_region', default="")
        #
        # # 用于木马感染异常场景筛选命中资产：（关键词模糊匹配）
        # asset_type = request.GET.get('asset_type', default="")
        #
        # # 境内不包含端口
        # not_src_port = request.GET.get('not_src_port', default="")
        # # 境外不包含端口
        # not_dst_port = request.GET.get('not_dst_port', default="")
        # # 用于DDoS异常场景筛选境内是被控还是反射，1是被控，2是反射
        # attack_type = request.GET.get('attack_type', default="")
        #
        # data = request.body
        # json_data = {}
        # if data:
        #     json_data = json.loads(data)
        # event_ids = json_data.get("event_ids", [])
        # # event_ids_str = ','.join(BaseContEvent.id_format_msg.format(x) for x in event_ids)
        # params_dic = {
        #     "is_order": True,
        #     "start_time": start_time,
        #     "end_time": end_time,
        #     "sip": sip,
        #     "dip": dip,
        #     "status": status,
        #     "judge_status": judge_status,
        #     "tag": tag,
        #     "src_unit": src_unit,
        #     "src_region": src_region,
        #     "dst_region": dst_region,
        #     "analysis_tech": analysis_tech,
        #     "whitelist": white_list_str,
        #     "blacklist": black_list_str,
        #     "types": types,
        #     "service_types": service_types,
        #     "custom_tags": custom_tags_str,
        #     "report_types": report_types_str,
        #     "src_info_unit": src_info_unit,
        #     "depart": departments,
        #     "src_info": src_info,
        #     "dst_info": dst_info,
        #     "app_type": app_type,
        #     "src_com": src_com,
        #     "dst_com": dst_com,
        #     "src_operator": src_operator,
        #     "dst_operator": dst_operator,
        #     "ipv6": ipv6,
        #     "event_ids": event_ids,
        #     "event_id": event_id,
        #     "post_distribution": post_distribution,
        #     "src_port": src_port,
        #     "dst_port": dst_port,
        #     "ip_tl_distribution": ip_tl_distribution,
        #     "work_time_tag": work_time_tag,
        #     "hit_whitelist": hit_whitelist,
        #     "hit_blacklist": hit_blacklist,
        #     "black_category_name": category_name,
        #     "is_app_type": is_app_type,  # 是否有应用类型
        #     "src_tag_name": src_tag_name,  # 境内标签类别
        #     "dst_tag_name": dst_tag_name,  # 境外标签类别
        #     "is_flow_surge": is_flow_surge,  # 是否为流量突增
        #     "flow_continue_time_tuple": flow_continue_time_tuple,  # 流量持续时间
        #     "asset_region": asset_region,
        #     "asset_type": asset_type,
        #     "not_src_port": not_src_port,
        #     "not_dst_port": not_dst_port,
        #     "attack_type": attack_type,
        # }
        #
        # if min_up_bytesall is not None:
        #     min_up_bytesall = convert_to_bytes(int(min_up_bytesall), min_up_bytesall_unit)
        #     params_dic.update({"min_up_bytesall": min_up_bytesall})
        # if max_up_bytesall is not None:
        #     max_up_bytesall = convert_to_bytes(int(max_up_bytesall), max_up_bytesall_unit)
        #     params_dic.update({"max_up_bytesall": max_up_bytesall})
        #
        # # 查询数据--添加排序与分页
        # params_dic.update(
        #     {"order": order,
        #      "order_column": order_column,
        #      # "page": int(page),
        #      # "limit": int(limit),
        #      "is_order": True,
        #      })
        # cem_obj = ContinuousEventsModel()
        #
        # event_list = cem_obj.get_event_filter(**params_dic)
        # s2 = int(time.time())
        # mlog.info("事件导出查询时间---{}".format(s2 - s))
        #
        # custom_tag_map = custom_tag_models.get_custom_tag_map()
        # mlog.info("事件导出标签查询时间---{}".format(int(time.time()) - s2))
        #
        # s3 = int(time.time())
        # src_ip_set = {i.get("src_ip") for i in event_list}
        # dst_ip_set = {i.get("dst_ip") for i in event_list}
        # mlog.info("ip一对多境内外ip获取时间---{}".format(int(time.time()) - s3))
        # s33 = int(time.time())
        #
        # # 使用 values_list() 只获取需要的字段，然后转换为字典
        # src_ip_statistics_map = dict(
        #     IpCountModel.objects.filter(ip__in=list(src_ip_set), ip_type=1)
        #     .values_list('ip', 'ip_count')
        # )
        # mlog.info("ip一对多境内ip获取时间---{}".format(int(time.time()) - s33))
        # s333 = int(time.time())
        # dst_ip_statistics_map = dict(
        #     IpCountModel.objects.filter(ip__in=list(dst_ip_set), ip_type=2)
        #     .values_list('ip', 'ip_count')
        # )
        # mlog.info("ip一对多境外ip获取时间---{}".format(int(time.time()) - s333))

        csv_name = '持续大流量事件_{}'.format(datetime.datetime.now().strftime(DATE_TIME_FORMAT))
        csv_name = quote(csv_name.encode("utf-8"))
        cee = ContEventExport()
        # 1. 解析参数
        params_dic, global_white_list, global_black_list = cee.parse_export_params(request)
        # 查询数据--添加排序与分页
        params_dic.update(
            {"order": request.GET.get("order"),
             "order_column": request.GET.get("order_column"),
             # "page": int(page),
             # "limit": int(limit),
             "is_order": True,
             })
        # 2. 查询数据
        event_list, src_ip_statistics_map, dst_ip_statistics_map, _ = cee.query_event_data(params_dic, is_export=True)
        custom_tag_map = cee.get_custom_tag_map()
        # 3. 获取事件类型映射
        event_type_map = CommonConf().get_continuous_event_type_reverse()
        # 使用StreamingHttpResponse并指定content_type为text/csv
        response = StreamingHttpResponse(
            event_data_generator(event_list, global_white_list, global_black_list, event_type_map, custom_tag_map,
                                 src_ip_statistics_map, dst_ip_statistics_map),
            content_type='text/csv')
        response['Content-Disposition'] = "attachment;filename=%s.csv" % (csv_name)
        return response
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def _download_csv_files(request):
    """
    事件导出Excel接口（优化版），返回xlsx文件
    """
    try:
        cee = ContEventExport()
        # 1. 解析参数
        params_dic, global_white_list, global_black_list = cee.parse_export_params(request)
        # 查询数据--添加排序与分页
        params_dic.update(
            {"order": request.GET.get("order"),
             "order_column": request.GET.get("order_column"),
             # "page": int(page),
             # "limit": int(limit),
             "is_order": True,
             })
        # 2. 查询数据
        event_list, src_ip_statistics_map, dst_ip_statistics_map = cee.query_event_data(params_dic)
        custom_tag_map = cee.get_custom_tag_map()
        # 3. 获取事件类型映射
        event_type_map = CommonConf().get_continuous_event_type_reverse()
        # 4. 生成Excel
        output = cee.event_data_to_excel(event_list, global_white_list, global_black_list, event_type_map,
                                         custom_tag_map, src_ip_statistics_map, dst_ip_statistics_map)
        # 5. 文件名
        excel_name = u'持续性告警_{}.xlsx'.format(datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
        excel_name = quote(excel_name.encode("utf-8"))
        # 6. 返回响应
        response = StreamingHttpResponse(output,
                                         content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = "attachment;filename=%s" % (excel_name)
        return response
    except Exception as _:
        mlog.error(u"导出Excel失败: %s" % traceback.format_exc())
        return HttpResponse(u"导出Excel失败", status=500)


def get_judge(event_id):
    try:
        record_res = {
            "code": 200,
            "msg": "ok",
            "data": {
                "judge_status": 0,
                "words": [],
                "appendixs": []
            }
        }
        get_judge_res = cont_event_models.get_judge_info(event_id)
        if len(get_judge_res):
            record_res["data"]["judge_status"] = get_judge_res[0][0] if get_judge_res[0][0] else 0
            if get_judge_res[0][1]:
                record_res["data"]["words"].append(get_judge_res[0][1])
            if get_judge_res[0][2]:
                file_name_list = get_judge_res[0][2].split(',')
                for file_name in file_name_list:
                    record_res["data"]["appendixs"].append({"name": file_name})
        json_str = json.dumps(record_res)
        return HttpResponse(json_str, status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def post_judge(event_id, request):
    try:
        data = request.body
        if not data:
            return HttpResponse("没有data数据", status=500, )
        json_data = json.loads(data)
        judge_status = json_data.get("judge_status", 0)
        judge_info = json_data.get("words", [])[0] if json_data.get("words", []) else ""

        if not len(cont_event_models.get_judge_info(event_id)):
            return HttpResponse("该事件未查到研判信息", status=500, )

        cont_event_models.update_judge_info(event_id=event_id, judge_status=judge_status, judge_info=judge_info)
        auditLog(request, "持续性事件:{} 研判成功, 状态:{}".format(event_id, judge_status))
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception:
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def judge(request):
    try:
        event_id = request.GET.get('event_id', default='')
        if not event_id:
            return HttpResponse("没有参数event_id", status=500, )

        if request.method == GET_METHOD:
            return get_judge(event_id=event_id)
        if request.method == POST_METHOD:
            return post_judge(event_id=event_id, request=request)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def create_appendixs_tmpdir():
    try:
        if not os.path.exists(APPENDIX_LOCAL_PATH):
            cmd = "mkdir -p {PATH}".format(PATH=APPENDIX_LOCAL_PATH)
            os.system(cmd)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())


def get_appendixs(event_id, file_name):
    try:
        # 通过事件id获取处置信息
        get_judge_res = cont_event_models.get_judge_info(event_id)
        if not len(get_judge_res):
            return HttpResponse("未查到处置信息", status=500, )

        # 生成存储的文件名：MD5（原文件名）.MD5(事件id)
        full_file_name = "{}.{}".format(hashlib.md5(file_name).hexdigest(), hashlib.md5(event_id).hexdigest())

        # 读取文件，返回文件流
        file_path = os.path.join(APPENDIX_LOCAL_PATH, full_file_name)
        if os.path.exists(file_path):
            # with open("{}/{}".format(APPENDIX_LOCAL_PATH, full_file_name)) as f:
            with open(file_path) as f:
                response = HttpResponse(f, content_type='application/octet-stream')
                response['Content-Disposition'] = 'attachment; filename={}'.format(file_name)
        else:
            response = HttpResponse("no such file", status=500, )

        return response
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def upload_appendixs(request, event_id, file_list):
    try:
        get_judge_res = cont_event_models.get_judge_info(event_id)
        if not len(get_judge_res):
            return HttpResponse("研判信息为空", status=500, )

        exist_file_name_list = get_judge_res[0][2].split(',') if get_judge_res[0][2] else []

        # 文件名称不能重复
        for file in file_list:
            if file.name in exist_file_name_list:
                return HttpResponse("file exist", status=500, )
            exist_file_name_list.append(file.name)

        # 上传文件至本地目录下/home/<USER>/ISOP/cont_event_appendix
        for file in file_list:
            file_name = "{}.{}".format(hashlib.md5(file.name).hexdigest(), hashlib.md5(event_id).hexdigest())
            # 写入文件
            f = open(os.path.join(os.path.join(APPENDIX_LOCAL_PATH), file_name), 'wb')
            for line in file.chunks():
                f.write(line)
            f.close()

        # multiprocessing.Process(target=hdfs_appendixs.upload_appendixs_to_HDFS,
        #                         args=(event_id, APPENDIX_TMP_PATH, APPENDIX_HDFS_PATH, file_list)).start()

        judge_file = ",".join(exist_file_name_list)
        cont_event_models.update_judge_file(event_id=event_id, judge_file=judge_file)
        auditLog(request, "持续性事件:{} 上传附件:{}成功".format(event_id, judge_file))
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def delete_appendixs(request, event_id, old_file_name):
    try:
        get_judge_res = cont_event_models.get_judge_info(event_id)
        if not len(get_judge_res):
            return HttpResponse("该事件研判信息为空", status=500, )

        exist_file_name_list = get_judge_res[0][2].split(',') if get_judge_res[0][2] else []
        if old_file_name not in exist_file_name_list:
            return HttpResponse("文件名不存在", status=500, )

        file_name = "{}.{}".format(hashlib.md5(old_file_name).hexdigest(), hashlib.md5(event_id).hexdigest())

        # hdfs_appendixs.delete_appendixs_from_HDFS(APPENDIX_TMP_PATH, APPENDIX_HDFS_PATH, file_name)
        cmd = "rm '{}/{}'".format(APPENDIX_LOCAL_PATH, file_name)
        os.system(cmd)
        exist_file_name_list.remove(old_file_name)
        judge_file = ",".join(exist_file_name_list)
        cont_event_models.update_judge_file(event_id=event_id, judge_file=judge_file)
        auditLog(request, "持续性事件:{} 删除附件:{}成功".format(event_id, old_file_name))
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def appendixs(request, event_id):
    """
        接口：事件处置--上传附件
    """
    try:
        if not event_id:
            return HttpResponse("事件ID参数未获取", status=500, )

        # 创建系统下存放文件的目录，需优化为初始化程序时创建。
        create_appendixs_tmpdir()
        # 上传附件
        if request.method == POST_METHOD:
            file_list = request.FILES.getlist('file')
            return upload_appendixs(request, event_id=event_id, file_list=file_list)
        return HttpResponse(HOST_ERROR_MSG, status=500, )

    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def single_appendixs(request, event_id, file_name):
    """
        接口：事件处置--下载附件、删除附件
    """
    try:
        create_appendixs_tmpdir()
        if not event_id:
            return HttpResponse("event_id未获取", status=500, )
        if not file_name:
            return HttpResponse("文件名未获取", status=500, )

        # 下载附件
        if request.method == GET_METHOD:
            return get_appendixs(event_id=event_id, file_name=file_name)
        # 删除附件
        if request.method == DELETE_METHOD:
            return delete_appendixs(request, event_id=event_id, old_file_name=file_name)
        return HttpResponse(HOST_ERROR_MSG, status=500, )

    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def custom_tags(request, event_id):
    """
    传tag_id时，是从已有的标签中选择
    tag_id为空时，需要新建标签，且将标签与事件关联
    """
    try:
        if not event_id:
            return HttpResponse("没有参数event_id", status=500, )

        # 添加/修改/删除
        if request.method != POST_METHOD:
            return HttpResponse("Method error", status=500, )
        data = request.body
        json_data = json.loads(data)

        # 自定义标签id
        new_custom_tag_id_list = json_data.get("custom_tag_id_list", [])
        # 自定义标签名称
        custom_tag_name = json_data.get("custom_tag_name", "")

        # 校验新的tag_id是否都存在
        if new_custom_tag_id_list and not custom_tag_models.is_new_custom_tags_exist(new_custom_tag_id_list):
            return HttpResponse("tag_id全部存在！", status=500, )

        # 新增自定义标签名称
        if custom_tag_name:
            if custom_tag_models.get_custom_tag_by_name(custom_tag_name):
                return HttpResponse("custom_tag_name already exists", status=500, )
            # 新建标签
            custom_tag_models.add_custom_tag(tag_name=custom_tag_name, tag_notes="")
            id_list = [i[0] for i in custom_tag_models.get_custom_tag_by_name(custom_tag_name) if i]
            new_custom_tag_id_list.extend(id_list)
        # 删除原有的关联关系
        cont_event_models.delete_custom_tags(event_id)
        # 将新的标签与事件关联
        cont_event_models.add_custom_tags(event_id, new_custom_tag_id_list)
        auditLog(request, "持续性事件:{}, 配置自定义标签成功".format(event_id))
        return HttpResponse(json.dumps({"code": 200, "msg": "ok"}), status=200, content_type=CONTENT_TYPE)
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
        return HttpResponse(HOST_ERROR_MSG, status=500, )


def field_conf(request):
    """
        页面字段配置接口
    """
    try:
        file_path = os.path.join(CONF_PATH, "web_field_conf.json")
        field_conf = json_tool.read_file(file_path)
        # 查询
        if request.method == GET_METHOD:
            type_ = request.GET.get("type")
            return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": field_conf[type_]})

        # 修改
        elif request.method == POST_METHOD:
            req_data = json.loads(request.body)
            type_ = req_data.get("type")
            field_data = req_data.get("data")

            field_conf[type_] = field_data
            with open(file_path, "w") as f:
                f.write(json.dumps(field_conf))

            return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": ""})
        else:
            return JsonResponse(status=200, data={"code": 400, "msg": "error", "data": "request method error"})
    except Exception as e:
        mlog.exception("字段配置出错")
        return JsonResponse(status=200, data={"code": 400, "msg": "error", "data": str(e)})


def get_key_service_types_map(_request):
    """
        重要服务枚举值接口
    """
    data = []
    for k, v in KEY_SERVICE_MAP.items():
        dic_ = {}
        dic_["key"] = k
        dic_["value"] = v
        data.append(dic_)

    data = sorted(data, key=lambda x: int(x["key"]), reverse=False)
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


def get_key_bd_service_types_map(_request):
    """
        重要数据库服务枚举值接口
    """
    data = []
    for k, v in KEY_DB_SERVICE_MAP.items():
        dic_ = {}
        dic_["key"] = k
        dic_["value"] = v
        data.append(dic_)
    data = sorted(data, key=lambda x: int(x["key"]), reverse=False)
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


def get_judge_status_map(_request):
    """
        处置类型枚举值接口
    """
    data = []
    for k, v in JUDGE_STATUS_MAP.items():
        dic_ = {}
        dic_["key"] = k
        dic_["value"] = v
        data.append(dic_)
    data = sorted(data, key=lambda x: int(x["key"]), reverse=False)
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


def get_department_type(_request):
    """
    行业类型枚举接口
    """
    data = []
    depart_list = get_department_data()
    for item in depart_list:
        dic_ = {"id": item[0], "name": item[1]}
        data.append(dic_)
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


@list_route(methods=['GET'])
def get_tag_name_map(request):
    """
    获取事件标签名称枚举接口
    """
    tag_name = [
        "物联网", "服务类型", "疑似DDOS攻击", "威胁情报", "恶意情报", "非常用端口", "云平台IP", "FTP",
        "物联网资产", "防火墙", "Web邮箱", "远程登录", "视频监控", "OA系统", "数据库", "VPN", "路由器", "网络附属存储(NAS)",
        "云管理平台"]

    data = []
    for i in tag_name:
        dic_ = {}
        dic_["key"] = event_other_tag_name[i]
        dic_["value"] = i
        data.append(dic_)
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


def get_event_info_view(request):
    """
        事件详细信息接口
    """
    try:
        data = {}
        if request.method == GET_METHOD:
            event_id = request.GET.get("event_id")
            data = get_event_info(event_id)
        return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
    return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})


def event_port_traffic(request):
    """
    事件端口流量分布
    """
    try:
        data = {}
        if request.method == GET_METHOD:
            event_id = request.GET.get("event_id")
            report_type = request.GET.get("report_type")
            related_alerts_list = cont_event_models.get_related_alerts(event_id)
            if not related_alerts_list:
                return HttpResponse("没有告警id", status=500, )
            if not related_alerts_list[0][0]:
                related_alerts_list[0][0] = "0"
            alert_id_list = related_alerts_list[0][0].split(";")
            if len(alert_id_list) == 1:
                alert_id_list *= 2

            data = get_port_traffic(alert_id_list, report_type)
        return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
    return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})


# {    "1": "大流量事件",
#     "2": "重要数据服务事件",
#     "3": "物联网设备事件",
#     "4": "重点单位事件",
#     "5": "流量倍数事件",
#     "6": "数据库事件",
#     "8": "重点监控目标事件"
# }

ALAET_JUDGE_STATUS_MAP = {
    "0": "未处置",
    "1": "误判",
    "2": "确认事件"
}


def check_param(start_time, end_time):
    try:
        if not start_time or not end_time:
            return None, None, True
        start_time = time_trans.format2timestamp(start_time)
        end_time = time_trans.format2timestamp(end_time)
        if start_time >= end_time:
            return None, None, True
        return start_time, end_time, False
    except Exception:
        return None, None, True


def get_event_group_by(request):
    "http://127.0.0.1:8000/cncert_kj/v2/continuous_events/get_event_group_by?start_time=2024-03-15 09:32:04&&end_time=2024-04-23 10:32:04&&group_by=event_type"
    start_time = request.GET.get("start_time")
    end_time = request.GET.get("end_time")
    start_time, end_time, flag = check_param(start_time, end_time)
    group_by = request.GET.get("group_by")
    alert_type = request.GET.get('alert_type')

    if flag or group_by not in ["event_type", "judge_status", "type", "record_type"] or alert_type not in ["netflow",
                                                                                                           "tllog_alert",
                                                                                                           "event"]:
        return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})

    is_show_whitelist = request.GET.get('is_show_whitelist', default="")
    white_list_str = ''
    if is_show_whitelist:
        global_white_list = get_globa_white_list()
        white_list_str = net.ips_to_str(global_white_list)

    if alert_type == "event":
        report_types_str = request.GET.get('report_types', default="")
        data_list = cont_event_models.get_event_group_by_type(start_time, end_time, group_by, report_types_str)
    elif alert_type == "netflow":
        data_list = netflow_alert_models.get_netflow_alert_group_by(start_time, end_time, group_by, white_list_str)
    else:
        data_list = tllog_alert_models.get_tllog_alert_group_by(start_time, end_time, group_by, white_list_str)

    if alert_type == "event":
        event_type_menu = CommonConf().get_continuous_event_type_reverse()
    else:
        event_type_menu = CommonConf().get_tllog_alert_type()
        event_type_menu.update(CommonConf().get_netflow_alert_type())
        event_type_menu = {value: key for key, value in event_type_menu.items()}

    result = []
    for data in data_list:
        format_data = {}
        format_data["count"] = data[0]
        if group_by == "event_type" or group_by == "type":
            format_data["event_name"] = event_type_menu.get(str(data[1]), str(data[0]))
            format_data["event_type"] = str(data[1])
        elif group_by == "judge_status":
            format_data["judge_status_name"] = JUDGE_STATUS_MAP[str(data[1])]
            format_data["judge_status"] = str(data[1])
        else:
            format_data["judge_status_name"] = ALAET_JUDGE_STATUS_MAP[str(data[1])]
            format_data["judge_status"] = str(data[1])
        result.append(format_data)

    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": result})


def _validate_and_parse_params(request):
    """
    校验并解析请求参数，返回标准化后的参数和错误标志。
    """
    start_time = request.GET.get("start_time")
    end_time = request.GET.get("end_time")
    start_time, end_time, flag = check_param(start_time, end_time)
    alert_type = request.GET.get('alert_type')
    if flag or alert_type not in ["netflow", "tllog_alert", "event"]:
        return None, None, None, None, True
    return start_time, end_time, alert_type, request, False


def _get_white_list_str(request):
    """
    判断是否展示白名单，返回白名单字符串。
    """
    is_show_whitelist = request.GET.get('is_show_whitelist', "")
    if is_show_whitelist:
        global_white_list = get_globa_white_list()
        return net.ips_to_str(global_white_list)
    return ''


def _get_time_grouping(start_time, end_time):
    """
    根据时间跨度确定分组粒度和格式。
    """
    if end_time - start_time <= 60 * 60 * 24:
        return 60 * 60, "%Y-%m-%d %H", 'yyyy-mm-dd hh24'
    else:
        return 60 * 60 * 24, "%Y-%m-%d", 'yyyy-mm-dd'


def _get_data_list(alert_type, start_time, end_time, sql_format_time, request, white_list_str):
    """
    根据 alert_type 获取对应的数据列表。
    """
    if alert_type == "event":
        report_types_str = request.GET.get('report_types', "")
        return cont_event_models.get_event_type_tendency(start_time, end_time, sql_format_time, report_types_str)
    elif alert_type == "netflow":
        return netflow_alert_models.get_netflow_alert_type_tendency(start_time, end_time, white_list_str,
                                                                    sql_format_time)
    else:
        return tllog_alert_models.get_tllog_alert_type_tendency(start_time, end_time, white_list_str,
                                                                sql_format_time)


def _get_event_type_menu(alert_type):
    """
    获取事件类型菜单，返回映射字典。
    """
    conf = CommonConf()
    if alert_type == "event":
        return conf.get_continuous_event_type_reverse()
    else:
        menu = conf.get_tllog_alert_type()
        menu.update(conf.get_netflow_alert_type())
        return {value: key for key, value in menu.items()}


def _format_result(data_list, time_list, event_type_menu):
    """
    格式化结果数据，组装前端需要的结构。
    """
    if not data_list:
        return JsonResponse(status=200, data={"code": 400, "msg": "无数据", "data": {}})
    # 先收集所有事件类型和时间点
    event_type_set = set()
    time_str_set = {t["start_time_str"] for t in time_list}
    # 预处理数据为 {(event_type, time): count}
    count_map = {}
    for data in data_list:
        event_type = event_type_menu.get(str(data[0]), str(data[0]))
        time_str = str(data[1])
        event_type_set.add(event_type)
        if time_str in time_str_set:
            count_map[(event_type, time_str)] = data[2]
    # 组装结果
    result = {}
    x_axis_time = [t["start_time_str"] for t in time_list]
    for event_type in event_type_set:
        result[event_type] = [count_map.get((event_type, t), 0) for t in x_axis_time]
    result["x_axis_time"] = x_axis_time
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": result})


def get_event_type_tendency(request):
    # >>
    # # http://127.0.0.1:8000/cncert_kj/v2/continuous_events/get_event_type_tendency?start_time=2024-03-15 09:32:04&&end_time=2024-04-23 10:32:04
    # start_time = request.GET.get("start_time")
    # end_time = request.GET.get("end_time")
    # start_time, end_time, flag = check_param(start_time, end_time)
    # alert_type = request.GET.get('alert_type')
    # if flag or alert_type not in ["netflow", "tllog_alert", "event"]:
    #     return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})
    #
    # is_show_whitelist = request.GET.get('is_show_whitelist', default="")
    # white_list_str = ''
    # if is_show_whitelist:
    #     global_white_list = get_globa_white_list()
    #     white_list_str = net.ips_to_str(global_white_list)
    #
    # if end_time - start_time <= 60 * 60 * 24:
    #     interval = 60 * 60
    #     format_time = "%Y-%m-%d %H"
    #     sql_format_time = 'yyyy-mm-dd hh24'
    # else:
    #     interval = 60 * 60 * 24
    #     format_time = "%Y-%m-%d"
    #     sql_format_time = 'yyyy-mm-dd'
    #
    # if alert_type == "event":
    #     report_types_str = request.GET.get('report_types', default="")
    #     data_list = cont_event_models.get_event_type_tendency(start_time, end_time, sql_format_time, report_types_str)
    # elif alert_type == "netflow":
    #     data_list = netflow_alert_models.get_netflow_alert_type_tendency(start_time, end_time, white_list_str,
    #                                                                      sql_format_time)
    # else:
    #     data_list = tllog_alert_models.get_tllog_alert_type_tendency(start_time, end_time, white_list_str,
    #                                                                  sql_format_time)
    #
    # time_list = time_trans.get_time_list(start_time, end_time, interval, format_time)
    # if alert_type == "event":
    #     event_type_menu = CommonConf().get_continuous_event_type_reverse()
    # else:
    #     event_type_menu = CommonConf().get_tllog_alert_type()
    #     event_type_menu.update(CommonConf().get_netflow_alert_type())
    #     event_type_menu = {value: key for key, value in event_type_menu.items()}
    #
    # result = {}
    # event_type_set = set()
    # format_data = {}
    # if data_list is None or len(data_list) == 0:
    #     return JsonResponse(status=200, data={"code": 400, "msg": "无数据", "data": {}})
    # for time_dict in time_list:
    #     start_time_str = time_dict["start_time_str"]
    #     for data in data_list:
    #         event_type = event_type_menu.get(str(data[0]), str(data[0]))
    #         _time = str(data[1])
    #         event_type_set.add(event_type)
    #         if start_time_str == _time:
    #             count = data[2]
    #             format_data[event_type + "_" + start_time_str] = count
    # x_axis_time = []
    # for time_dict in time_list:
    #     start_time_str = time_dict["start_time_str"]
    #     x_axis_time.append(start_time_str)
    #     for event_type in event_type_set:
    #         count = format_data.get(event_type + "_" + start_time_str, 0)
    #         li = result.get(event_type, [])
    #         li.append(count)
    #         result[event_type] = li
    # result["x_axis_time"] = x_axis_time
    # return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": result})

    # 参数校验与解析
    start_time, end_time, alert_type, request, flag = _validate_and_parse_params(request)
    if flag:
        return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})
    # 白名单处理
    white_list_str = _get_white_list_str(request)
    # 分组粒度
    interval, format_time, sql_format_time = _get_time_grouping(start_time, end_time)
    # 数据获取
    data_list = _get_data_list(alert_type, start_time, end_time, sql_format_time, request, white_list_str)
    # 时间区间列表
    time_list = time_trans.get_time_list(start_time, end_time, interval, format_time)
    # 事件类型菜单
    event_type_menu = _get_event_type_menu(alert_type)
    # 结果组装
    return _format_result(data_list, time_list, event_type_menu)


def get_event_summary_overview(request):
    # >> http://127.0.0.1:8000/cncert_kj/v2/continuous_events/get_event_summary_overview?start_time=2024-03-15 09:32:04&&end_time=2024-04-23 10:32:04
    start_time = request.GET.get("start_time")
    end_time = request.GET.get("end_time")
    start_time, end_time, flag = check_param(start_time, end_time)
    if flag:
        return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})
    report_types_str = request.GET.get('report_types', default="")
    data_list = cont_event_models.get_event_group_by_type(start_time, end_time, "event_type", report_types_str)
    event_type_menu = CommonConf().get_continuous_event_type()
    event_type_menu = {value: key for key, value in event_type_menu.items()}
    result = {}
    event_big_type_dict = CommonConf().get_alert_category(is_event=True)
    for data in data_list:
        format_data = {}
        event_type = str(data[1])
        event_big_type = event_big_type_dict.get(event_type, event_type)
        li = result.get(event_big_type, [])
        event_name = event_type_menu.get(event_type)
        if not event_name:
            continue
        format_data["count"] = data[0]
        format_data["event_name"] = event_name
        format_data["event_type"] = event_type
        event_type_menu.pop(event_type)
        li.append(format_data)
        result[event_big_type] = li

    for key, value in event_type_menu.items():
        format_data = {}
        event_type = key
        event_big_type = event_big_type_dict.get(event_type, event_type)
        li = result.get(event_big_type, [])
        format_data["count"] = 0
        format_data["event_name"] = value
        format_data["event_type"] = event_type

        li.append(format_data)
        result[event_big_type] = li
    for key, events in result.items():
        events.sort(key=lambda x: (-x['count'], x['event_type']))

    # 根据每个大类的总数进行排序（大类总和为 count 值的总和）
    sorted_data = OrderedDict(
        sorted(result.items(), key=lambda x: sum(event['count'] for event in x[1]), reverse=True))
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": sorted_data})


def _parse_location(src_region):
    """解析并去重地理位置字符串"""
    return list(OrderedDict.fromkeys(loc.strip() for loc in src_region.split()))


def _remove_consecutive_repeats(s):
    """去除连续重复的词（词长度 >= 2）"""
    n = len(s)
    result = []
    i = 0
    while i < n:
        max_k = (n - i) // 2
        found = False
        for k in range(max_k, 1, -1):
            if i + 2 * k > n:
                continue
            if s[i:i + k] == s[i + k:i + 2 * k]:
                result.append(s[i:i + k])
                i += 2 * k
                found = True
                break
        if not found:
            result.append(s[i])
            i += 1
    return ''.join(result)


def _process_location(src_region, src_unit):
    if not src_unit:
        return u"未知"
    if not src_region:
        return src_unit
    # 分割地理位置并去除空白字符
    locations = _parse_location(src_region)
    if not locations:
        return src_unit

    # 获取省份和城市信息
    if len(locations) == 2:
        province, city = locations
    elif len(locations) == 3:
        _, province, city = locations
    else:
        province, city = "", locations[-1]
    province = "" if province in ["中国", u"中国"] else province
    city = "" if city in ["中国", u"中国"] else city
    s = "".join([province, city, src_unit])
    result = _remove_consecutive_repeats(s)
    return result


def handle_video(event_id, request, file_path, template_name):
    """
    处理视频类型的模板
    """
    try:

        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        dst_region = data["dst_region"]
        report_type = data["report_type"]
        dport = data["dport"]
        sport = data["sport"]
        sport_list = data["sport_list"]
        unit_ = data["unit_"]
        e_timestamp = data["e_timestamp"]
        s_timestamp = data["s_timestamp"]
        event_type = data["event_type"]

        # 获取资产类型
        service_type = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=1,
                                                               tag_name="service").first()
        asset_type = service_type.tag_content if service_type else "未知"
        protocol = u"TCP"  # 协议类型

        # 开始获取总的流入流出流量，近一个月的流量
        top_10_dict = cont_event_models.get_video_flow_info_of_es(report_type, s_timestamp, e_timestamp, src_ip,
                                                                  sports=sport_list)
        if len(top_10_dict["data"]) > 1:
            # 如果有多个目的ip，目的地理位置 需要改变
            dst_region = u"境外多个"
            dst_ip = u""
        table_list = top_10_dict["data"][:10]  # 排序并获取前 10

        total_out_bytes = bytes_trans.long2unit(top_10_dict["total_out_bytes"])
        start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
        end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
        # 将字符串转换为 datetime 对象
        dt = datetime.datetime.strptime(start_time, DATE_TIME_FORMAT)
        event_analysis_start_time = BaseContEvent.date_msg.format(dt.month, dt.day)
        dt2 = datetime.datetime.strptime(end_time, DATE_TIME_FORMAT)
        event_analysis_end_time = BaseContEvent.date_msg.format(dt2.month, dt2.day)
        # 判断事件是主动还是被动，上传为主动，下载为被动
        if event_type in [11, 13, 21, 23, 31, 33, 41, 43, 61, 63, 71, 211]:
            initiative_or_passive = u"主动"
        elif event_type in [12, 14, 22, 24, 32, 34, 42, 44, 62, 64, 72, 212]:
            initiative_or_passive = u"被动"
        else:
            initiative_or_passive = u"未知"

        filling_map = {
            "src_ip": src_ip, "src_com": unit_, "dst_ip": dst_ip, "dst_region": dst_region, "start_time": start_time,
            "end_time": end_time, "up_bytesall": total_out_bytes, "asset_type": asset_type,
            "sport": sport, "dport": dport, "protocol": protocol, "create_time": create_time,
            "initiative_or_passive": initiative_or_passive, "e_start_time": event_analysis_start_time,
            "e_end_time": event_analysis_end_time
        }
        port_distribution_tag = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=0,
                                                                        tag_name="port_distribution").first()
        if port_distribution_tag:
            port_distribution = port_distribution_tag.tag_content
            if BaseContEvent.dst_port_msg2 in port_distribution:
                dport = BaseContEvent.dst_port_msg
            else:
                dport = BaseContEvent.dst_port_msg3.format(dport)
        filling_map["dport"] = dport
        filling_map["table_list"] = table_list
        output_filename = BaseContEvent.output_filename_msg.format(unit_)

        # 保存文件到服务器的uploads文件夹，文件名使用secure_filename保证安全
        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        # 打开渲染后的文档
        doc = Document(rendered_docx)
        # 获取模板中的第一个表格
        if doc.tables:
            table = doc.tables[0]
            # 删除示例数据行，只保留表头
            table._element.remove(table.rows[1]._element)
            for row_data in table_list:
                # 填充数据行
                new_row = table.add_row()
                new_row.cells[0].text = row_data["num"]
                new_row.cells[1].text = row_data["asset_ip"]
                new_row.cells[2].text = unit_
                new_row.cells[3].text = row_data["peer_ip"]
                new_row.cells[4].text = row_data["region"]
                new_row.cells[5].text = row_data["out_bytes"]
                new_row.cells[6].text = row_data["in_bytes"]
        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def get_top_apt_data(base_unit, src_com, data):
    if base_unit:
        return cont_event_models.get_apt_flow_info_of_es(
            base_unit, data["report_type"], data["s_timestamp"], data["e_timestamp"], data["dport_list"])
    else:
        q_ports = Q()
        for port in data["dport_list"]:
            q_ports |= Q(dst_port__contains="," + port + ",")
        src_ip_list = ContinuousEvents.objects.filter(
            start_time__gte=data["s_timestamp"],
            end_time__lte=data["e_timestamp"],
            src_com=src_com
        ).filter(q_ports).values_list('src_ip', flat=True).distinct()
        return cont_event_models.get_apt_flow_info_by_src_ip(
            src_ip_list[:50000], data["report_type"], data["s_timestamp"], data["e_timestamp"], data["dport_list"])


def build_filling_map(data, top_10_dict):
    start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
    end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
    start_stamp = datetime.datetime.fromtimestamp(top_10_dict["min_start_time"])
    end_stamp = datetime.datetime.fromtimestamp(top_10_dict["max_end_time"])

    return {
        "src_com": data["unit_"],
        "create_time": data["create_time"],
        "start_time": start_time,
        "end_time": end_time,
        "start_date": time_trans.output_datetime(start_stamp),
        "end_date": time_trans.output_datetime(end_stamp),
        "src_ip": "、".join(top_10_dict["src_ip"]),
        "dst_ip": "、".join(top_10_dict["dst_ip"]),
        "ip_count": len(top_10_dict["src_ip"]),
        "max_ip": top_10_dict["data"][0]["asset_ip"] if top_10_dict["data"] else "",
        "max_up_bytes": top_10_dict["data"][0]["out_bytes"] if top_10_dict["data"] else "",
        "dport": data["dport"],
        "protocol": "、".join(top_10_dict["protocol"]) if top_10_dict["protocol"] else "未知",
        "table_list": top_10_dict["data"][:10]
    }


def fill_table_with_apt_data(table, table_list, unit_, dport):
    for row_data in table_list:
        new_row = table.add_row()
        new_row.cells[0].text = unit_
        new_row.cells[1].text = u"{}".format(row_data["asset_ip"])
        new_row.cells[2].text = u"{}".format(row_data["peer_ip"])
        new_row.cells[3].text = u"{}".format(dport)
        new_row.cells[4].text = u"{}".format(row_data["protocol"])
        new_row.cells[5].text = u"{}".format(row_data["out_bytes"])
        new_row.cells[6].text = u"{}".format(row_data["in_bytes"])
        new_row.cells[7].text = u"{}".format(row_data["start_time"])
        new_row.cells[8].text = u"{}".format(row_data["end_time"])


def merge_and_style_table(table):
    # 获取表格的行数和列数
    num_rows = len(table.rows)
    # 定义需要合并的列索引（从0开始）
    merge_columns = [0, 2, 3, 4]
    # 遍历需要合并的列
    for col_idx in merge_columns:
        start_row = None  # 记录相同内容开始的行索引
        prev_value = None
        for row_idx in range(1, num_rows):  # 从第二行开始遍历
            current_cell = table.cell(row_idx, col_idx)
            current_text = current_cell.text.strip()
            if start_row and current_text == prev_value:
                table.cell(start_row, col_idx).text = ""
                table.cell(row_idx, col_idx).merge(table.cell(start_row, col_idx))
            else:
                start_row = row_idx  # 更新开始行
            prev_value = current_text
        # 合并最后一段相同内容的单元格
        if start_row is not None and num_rows - start_row > 1:
            table.cell(start_row, col_idx).merge(table.cell(num_rows - 1, col_idx))
    # 设置所有单元格的上下左右居中
    for row in table.rows:
        for cell in row.cells:
            # 获取单元格的第一个段落
            paragraph = cell.paragraphs[0]

            # 设置段落的水平居中
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 设置字体大小为12磅
            run = paragraph.add_run()
            run.font.size = Pt(12)

            # 设置单元格内容的垂直居中
            # python-docx 默认垂直居中，但可以显式设置
            cell.vertical_alignment = WD_ALIGN_PARAGRAPH.CENTER


def handle_apt(event_id, request, file_path, template_name):
    """处理APT类型的模板"""
    try:
        data = basic_parameters(event_id)
        unit_ = data["unit_"]
        src_com = data["src_com"]
        base_unit = data["base_unit"]

        if not base_unit and not src_com:
            return JsonResponse(status=200, data={"code": 400, "msg": "备案单位为空"})

        top_10_dict = get_top_apt_data(base_unit, src_com, data)
        filling_map = build_filling_map(data, top_10_dict)

        output_filename = template_name.replace("某单位", unit_)
        # 保存文件到服务器的uploads文件夹，文件名使用secure_filename保证安全
        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        # 打开渲染后的文档
        doc = Document(rendered_docx)
        # 获取模板中的第一个表格
        if doc.tables:
            table = doc.tables[0]
            fill_table_with_apt_data(table, filling_map["table_list"], unit_, filling_map["dport"])
            merge_and_style_table(table)

        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def get_date(start_time, end_time):
    dt = datetime.datetime.strptime(start_time, DATE_TIME_FORMAT)
    start_date = time_trans.output_datetime(dt)
    dt2 = datetime.datetime.strptime(end_time, DATE_TIME_FORMAT)
    end_date = time_trans.output_datetime(dt2)
    return start_date, end_date


def basic_parameters(event_id):
    event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
    if not event_obj:
        return {}
    src_com = event_obj.src_com
    src_region = event_obj.src_region

    dport_list = event_obj.dst_port[1:-1].split(",") if event_obj.dst_port else []
    dport = "、".join(dport_list) if dport_list else u"未知"

    sport_list = event_obj.src_port[1:-1].split(",") if event_obj.src_port else []
    sport = "、".join(sport_list) if sport_list else u"未知"
    # 创建时间
    now_time = datetime.datetime.now()
    create_time = time_trans.output_datetime(now_time)

    # 查询ES时间
    e_timestamp = int(time.time())
    s_timestamp = e_timestamp - 3600 * 24 * 30

    # 单位名称

    obj = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=1, tag_name="unit").first()
    src_unit = obj.tag_content if obj else ""
    if src_unit and src_unit.endswith("大学"):
        unit_ = src_unit
    else:
        _src_unit = src_unit if src_unit else src_com
        # 处理地理位置和单位名称
        unit_ = u'{}'.format(_process_location(src_region, _src_unit))

    data = {
        "src_ip": event_obj.src_ip,
        "dst_ip": event_obj.dst_ip,
        "src_com": src_com,
        "unit_": unit_,
        "base_unit": src_unit if src_unit else "",
        "src_region": src_region,
        "dst_region": event_obj.dst_region.replace(" ", "") if event_obj.dst_region else "",
        "create_time": create_time,
        "s_timestamp": s_timestamp,
        "e_timestamp": e_timestamp,
        "sport": sport,
        "sport_list": sport_list,
        "dport": dport,
        "dport_list": dport_list,
        "report_type": event_obj.report_type,
        "event_type": event_obj.event_type,
    }
    return data


def get_asset_type(event_id):
    obj = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=1, tag_name="service").first()
    return obj.tag_content if obj else "未知"


def analyze_destination_info(data, dst_ip):
    if len(data) > 1:
        return u"多个", u""
    return u"", dst_ip


def adjust_port_display(event_id, dport, sport):
    tag = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=0, tag_name="port_distribution").first()
    if tag:
        port_distribution = tag.tag_content

        dport = BaseContEvent.dst_port_msg if BaseContEvent.dst_port_msg2 in port_distribution else BaseContEvent.dst_port_msg3.format(
            dport)
        sport = u"动态变化" if "源端口分散" in port_distribution else "{}".format(sport)
    return dport, sport


def get_table2_data(report_type, s_timestamp, e_timestamp, first_ip):
    if not first_ip:
        return u"未知IP", u"未知时间", u"未知时间", []

    top_10_dict2 = cont_event_models.get_outbound_dst_ip_flow_info_of_es(report_type, s_timestamp, e_timestamp,
                                                                         first_ip, threshold=104857600)
    table_start_time, table_end_time = get_date(
        time_trans.timestamp2format(top_10_dict2["min_start_time"]),
        time_trans.timestamp2format(top_10_dict2["max_end_time"]))

    data = top_10_dict2.get("data", [])
    if not data:
        region = u"未知IP"
    elif len(data) == 1:
        region = u"境内IP{}".format(data[0]["asset_ip"])
    else:
        region = u"境内多个IP"
    return region, table_start_time, table_end_time, data[:10]


def handle_outbound(event_id, request, file_path, template_name):
    """处理异常出境类型的模板"""
    try:
        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        report_type = data["report_type"]
        dport = data["dport"]
        sport = data["sport"]
        sport_list = data["sport_list"]
        unit_ = data["unit_"]
        e_timestamp = data["e_timestamp"]
        s_timestamp = data["s_timestamp"]

        asset_type = get_asset_type(event_id)

        """
        以当前事件的境内IP和境内端口为查询条件，去es里查询流出流量大于100MB的记录，以这些记录的最小开始时间为开始时间，最大结束时间为结束时间
        """
        top_10_dict = cont_event_models.get_outbound_flow_info_of_es(
            report_type, s_timestamp, e_timestamp, src_ip, sports=sport_list, threshold=104857600)

        dst_region, dst_ip = analyze_destination_info(top_10_dict["data"], dst_ip)

        first_region, first_ip = (top_10_dict["data"][0]["region"], top_10_dict["data"][0]["peer_ip"]) if top_10_dict[
            "data"] else ("", "")
        total_out_bytes = bytes_trans.long2unit(top_10_dict["total_out_bytes"])

        dport, sport = adjust_port_display(event_id, dport, sport)

        table_list = top_10_dict["data"][:10]  # 排序并获取前 10
        start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
        end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
        event_analysis_start_time, event_analysis_end_time = get_date(start_time, end_time)

        # 第二张表格es查询

        table_region, table_start_time, table_end_time, table_list2 = get_table2_data(report_type, s_timestamp,
                                                                                      e_timestamp, first_ip)

        filling_map = {"src_ip": src_ip, "src_com": unit_, "dst_ip": dst_ip, "dst_region": dst_region,
                       "start_time": start_time, "end_time": end_time, "up_bytesall": total_out_bytes, "dport": dport,
                       "asset_type": asset_type, "sport": sport, "create_time": create_time, "first_ip": first_ip,
                       "first_region": first_region, "e_start_time": event_analysis_start_time,
                       "e_end_time": event_analysis_end_time, "table_list": table_list, "table_region": table_region,
                       "table_start_time": table_start_time, "table_end_time": table_end_time}
        output_filename = BaseContEvent.output_filename_msg.format(unit_)
        # 保存文件到服务器的uploads文件夹，文件名使用secure_filename保证安全
        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        # 打开渲染后的文档
        doc = Document(rendered_docx)
        # 获取模板中的第一个表格
        if doc.tables:
            table = doc.tables[0]
            # 删除示例数据行，只保留表头
            table._element.remove(table.rows[1]._element)
            for row_data in table_list:
                # 填充数据行
                new_row = table.add_row()
                new_row.cells[0].text = row_data["num"]
                new_row.cells[1].text = row_data["asset_ip"]
                new_row.cells[2].text = row_data["asset_name"] if row_data["asset_name"] else unit_
                new_row.cells[3].text = row_data["peer_ip"]
                new_row.cells[4].text = row_data["region"]
                new_row.cells[5].text = row_data["out_bytes"]
                new_row.cells[6].text = row_data["in_bytes"]
            table2 = doc.tables[1]
            # 删除示例数据行，只保留表头
            table2._element.remove(table2.rows[1]._element)
            # 批量查询备案系统
            ip_list = list({i["asset_ip"] for i in table_list2})
            with RequestFilingSys() as request_filing_obj:
                ip_details = request_filing_obj.query_icp_api(ip_list)
            for row_data2 in table_list2:
                new_row2 = table2.add_row()
                new_row2.cells[0].text = row_data2["num"]
                new_row2.cells[1].text = row_data2["asset_ip"]
                name_ = row_data2["asset_name"]
                if name_:
                    new_row2.cells[2].text = name_
                else:
                    unit_name_ = ip_details.get(row_data2["asset_ip"], {}).get("user", "")
                    mlog.info("{}--获取备案单位:{}".format(row_data2["asset_ip"], unit_name_))
                    new_row2.cells[2].text = unit_name_
                new_row2.cells[3].text = row_data2["peer_ip"]
                new_row2.cells[4].text = row_data2["region"]
                new_row2.cells[5].text = row_data2["out_bytes"]
                new_row2.cells[6].text = row_data2["in_bytes"]
                new_row2.cells[7].text = u"{}".format(row_data2["port"])
                new_row2.cells[8].text = ""
                new_row2.cells[9].text = row_data2["active_passive"]
        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def handle_agent(event_id, request, file_path, template_name):
    """处理代理类型的模板"""
    try:
        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        report_type = data["report_type"]
        dport = data["dport"]
        sport = data["sport"]
        sport_list = data["sport_list"]
        unit_ = data["unit_"]
        e_timestamp = data["e_timestamp"]
        s_timestamp = data["s_timestamp"]

        # 获取资产类型
        service_type = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=1,
                                                               tag_name="service").first()
        asset_type = service_type.tag_content if service_type else "未知"
        """
        以当前事件的境内IP和境内端口为查询条件，去es里查询流出流量大于100MB的记录，以这些记录的最小开始时间为开始时间，最大结束时间为结束时间
        """

        top_10_dict = cont_event_models.get_outbound_flow_info_of_es(
            report_type, s_timestamp, e_timestamp, src_ip, sports=sport_list, threshold=104857600)
        dst_region = u""
        if len(top_10_dict["data"]) > 1:
            # 如果有多个目的ip，目的地理位置 需要改变
            dst_region = u"多个"
            dst_ip = u""
        total_out_bytes = bytes_trans.long2unit(top_10_dict["total_out_bytes"])

        port_distribution_tag = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_type=0,
                                                                        tag_name="port_distribution").first()
        if port_distribution_tag:
            port_distribution = port_distribution_tag.tag_content
            dport = BaseContEvent.dst_port_msg if BaseContEvent.dst_port_msg2 in port_distribution else BaseContEvent.dst_port_msg3.format(
                dport)
            sport = u"动态变化" if "源端口分散" in port_distribution else "{}".format(sport)

        table_list = top_10_dict["data"][:10]  # 排序并获取前 10
        start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
        end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
        event_analysis_start_time, event_analysis_end_time = get_date(start_time, end_time)

        filling_map = {"src_ip": src_ip, "src_com": unit_, "dst_ip": dst_ip, "dst_region": dst_region,
                       "start_time": start_time, "end_time": end_time, "up_bytesall": total_out_bytes, "dport": dport,
                       "asset_type": asset_type, "sport": sport, "create_time": create_time,
                       "e_start_time": event_analysis_start_time,
                       "e_end_time": event_analysis_end_time, "table_list": table_list}
        output_filename = template_name.replace("某单位", unit_)
        # 保存文件到服务器的uploads文件夹，文件名使用secure_filename保证安全
        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        # 打开渲染后的文档
        doc = Document(rendered_docx)
        # 获取模板中的第一个表格
        if doc.tables:
            table = doc.tables[0]
            # 删除示例数据行，只保留表头
            table._element.remove(table.rows[1]._element)
            for row_data in table_list:
                # 填充数据行
                new_row = table.add_row()
                new_row.cells[0].text = row_data["num"]
                new_row.cells[1].text = row_data["asset_ip"]
                new_row.cells[2].text = row_data["asset_name"] if row_data["asset_name"] else unit_
                new_row.cells[3].text = row_data["peer_ip"]
                new_row.cells[4].text = row_data["region"]
                new_row.cells[5].text = row_data["out_bytes"]
                new_row.cells[6].text = row_data["in_bytes"]
        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def handle_trojan(event_id, request, file_path, template_name):
    """处理感染木马类型的模板"""
    try:
        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        dst_region = data["dst_region"]
        report_type = data["report_type"]
        dport = data["dport"]
        dport_list = data["dport_list"]
        unit_ = data["unit_"]
        e_timestamp = data["e_timestamp"]
        s_timestamp = data["s_timestamp"]

        """
        以当前事件的IP对和境外端口为查询条件，去es里查询，以这些记录的最小开始时间为开始时间，最大结束时间为结束时间
        """
        top_10_dict = cont_event_models.get_trojan_flow_info_of_es(report_type, s_timestamp, e_timestamp, src_ip,
                                                                   dst_ip, dport_list)
        total_out_bytes = bytes_trans.long2unit(top_10_dict["total_out_bytes"])
        total_in_bytes = bytes_trans.long2unit(top_10_dict["total_in_bytes"])

        start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
        end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
        event_analysis_start_time, event_analysis_end_time = get_date(start_time, end_time)

        filling_map = {
            "src_ip": src_ip,
            "src_com": unit_,
            "dst_ip": dst_ip,
            "dst_region": dst_region,
            "start_time": start_time,
            "end_time": end_time,
            "up_bytesall": total_out_bytes,
            "dst_port": dport,
            "create_time": create_time,
            "e_start_time": event_analysis_start_time,
            "e_end_time": event_analysis_end_time}

        output_filename = BaseContEvent.output_filename_msg.format(unit_)

        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        doc = Document(rendered_docx)
        if doc.tables:
            table = doc.tables[0]
            new_row = table.add_row()
            new_row.cells[0].text = src_ip
            new_row.cells[1].text = unit_
            new_row.cells[2].text = dst_ip
            new_row.cells[3].text = dport
            new_row.cells[4].text = dst_region
            new_row.cells[5].text = total_out_bytes
            new_row.cells[6].text = total_in_bytes

        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def handle_specific_ip(event_id, request, file_path, template_name):
    """处理关于某单位向境外特定IP传输数据异常出境事件的报告"""
    mlog.info("开始处理：{}".format(template_name))
    try:
        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        dst_region = data["dst_region"]
        report_type = data["report_type"]
        sport = data["sport"]
        dport = data["dport"]
        sport_list = data["sport_list"]
        unit_ = data["unit_"]
        e_timestamp = data["e_timestamp"]
        s_timestamp = data["s_timestamp"]

        """
        以当前事件的IP对和境内端口为查询条件，去es里查询，以这些记录的最小开始时间为开始时间，最大结束时间为结束时间
        """
        top_10_dict = cont_event_models.get_specific_ip_flow_info_of_es(report_type, s_timestamp, e_timestamp, src_ip,
                                                                        dst_ip, sport_list)
        total_out_bytes = bytes_trans.long2unit(top_10_dict["total_out_bytes"])
        total_in_bytes = bytes_trans.long2unit(top_10_dict["total_in_bytes"])

        start_time = time_trans.timestamp2format(top_10_dict["min_start_time"])
        end_time = time_trans.timestamp2format(top_10_dict["max_end_time"])
        event_analysis_start_time, event_analysis_end_time = get_date(start_time, end_time)
        # 获取 资产类型
        product_list = set(AssetInfoModel.objects.filter(asset_ip=src_ip, asset_port__in=sport_list).values_list(
            "product", flat=True))

        service_type = "、".join(product_list) if product_list else u"未知"
        filling_map = {
            "src_ip": src_ip,
            "src_com": unit_,
            "dst_ip": dst_ip,
            "dst_region": dst_region,
            "start_time": start_time,
            "end_time": end_time,
            "up_bytesall": total_out_bytes,
            "in_bytesall": total_in_bytes,
            "sport": sport,
            "dport": dport,
            "service_type": service_type,
            "create_time": create_time,
            "e_start_time": event_analysis_start_time,
            "e_end_time": event_analysis_end_time}

        output_filename = BaseContEvent.output_filename_msg.format(unit_)

        doc = DocxTemplate(file_path)
        doc.render(filling_map)

        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


# 定义模板名称与处理函数的映射关系
TEMPLATE_MAP = {
    u"关于某单位数据异常出境事件的报告": handle_outbound,
    u"关于某单位视频设备异常出境事件报告": handle_video,
    u"关于某单位主机遭境外APT组织攻击窃密的报告": handle_apt,
    u"关于某单位代理服务数据异常出境事件的报告": handle_agent,
    u"关于某单位感染木马数据异常出境事件的报告": handle_trojan,
    u"关于某单位向境外特定IP传输数据异常出境事件的报告": handle_specific_ip,
}


def validate_and_get_template(event_id, request):
    current_path = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    event_obj = ContinuousEvents.objects.filter(event_id=event_id).first()
    if not event_obj:
        return JsonResponse(status=200, data={"code": 400, "msg": "No data available"})
    if event_obj.report_type == 0:
        return JsonResponse(status=200, data={"code": 201, "msg": "手动录入事件不支持导出docx"})
    template_name = request.GET.get('template_name')
    if not template_name:
        return JsonResponse(status=200, data={"code": 400, "msg": "no template_name field"})
    file_path = os.path.join(current_path, "template", template_name + ".docx")
    if not os.path.exists(file_path):
        return JsonResponse(status=200, data={"code": 400, "msg": "no template"})
    return event_obj, file_path, template_name


def join_or_default(items):
    return "、".join(set(items)) if items else "未知"


def judge_initiative(event_type):
    if event_type in [11, 13, 21, 23, 31, 33, 41, 43, 61, 63, 71]:
        return "主动"
    elif event_type in [12, 14, 22, 24, 32, 34, 42, 44, 62, 64, 72]:
        return "被动"
    return "未知"


def dynamic_port_label(port, port_list, default_value):
    return default_value if port and len(port_list) > 1 else port


def extract_sql_type(app_type_str):
    return "、".join([i.split(",")[1] for i in app_type_str.split("&")]) if app_type_str else "未知"


def generate_table_list(event_obj, template_name, src_ip):
    dst_ip = event_obj.dst_ip
    dst_ips = []
    table_list = []
    related_alert_uuids = []
    e_timestamp = int(time.time())
    es_start_timestamp = e_timestamp - 3600 * 24 * 365
    pg_start_timestamp = e_timestamp - 3600 * 24 * 7

    if template_name == BaseContEvent.ddos_template_name:
        total_out_bytes, total_in_bytes, start_time, end_time = cont_event_models.get_flow_info_of_es(
            event_obj.report_type, es_start_timestamp, e_timestamp, src_ip)

        ddos_data_list = cont_event_models.get_event_info_by_src_ip(pg_start_timestamp, e_timestamp, src_ip)
        for ddos in ddos_data_list:
            ddos_dst_ip = ddos[3]
            if ddos_dst_ip:
                dst_ips.append(ddos_dst_ip)
            if not is_private_ip(ddos_dst_ip):
                table_list.append([ddos[0], ddos[1], ddos_dst_ip, ddos[4], bytes_trans.long2unit(int(ddos[8])),
                                   bytes_trans.long2unit(int(ddos[9]))])
                related_alert_uuids.extend(ddos[10].split(";"))
    else:
        total_out_bytes, total_in_bytes, start_time, end_time = cont_event_models.get_flow_info_of_es(
            event_obj.report_type, es_start_timestamp, e_timestamp, src_ip, dst_ip)
        if not is_private_ip(dst_ip):
            table_list.append([src_ip, dst_ip, total_out_bytes, total_in_bytes, start_time, end_time])
    return table_list, related_alert_uuids, total_out_bytes, total_in_bytes, start_time, end_time, dst_ips


def get_alerts_by_type(report_type, alert_ids):
    if str(report_type) == "1":
        return tllog_alert_models.get_alert_filter_by_uuids(alert_id_list=alert_ids)
    elif str(report_type) == "2":
        return netflow_alert_models.get_alert_filter_by_uuids(alert_id_list=alert_ids)
    return []


def add_if_valid(value, target_list):
    if value:
        target_list.append(value)


def add_if_valid_hostr(hostr, target_list):
    if hostr and hostr not in ["NULL", u"NULL"] and is_valid_domain(hostr):
        target_list.append(hostr)


def fetch_alert_data(alert_id_list_str, report_type):
    def parse_flow_logs(alerts):
        protocol_list, hostr_list, app_list = [], [], []
        for alert in alerts:
            logs = alert[6]
            if not logs or logs.endswith(SUFFIX):
                continue
            for log in logs.split('|'):
                try:
                    flow = json.loads(log)
                    add_if_valid(flow.get("protocol"), protocol_list)
                    add_if_valid_hostr(flow.get("hostr"), hostr_list)
                    add_if_valid(flow.get("app"), app_list)
                except Exception:
                    mlog.error("Invalid flow log: {}".format(log))
        return protocol_list, hostr_list, app_list

    result = {"protocol": "未知", "hostr": "未知", "app_type": "未知", "host_service": "未知"}
    if not alert_id_list_str:
        return result

    alerts = get_alerts_by_type(report_type, alert_id_list_str)
    if not alerts:
        return result
    protocols, hostrs, apps = parse_flow_logs(alerts)
    result.update({
        "protocol": join_or_default(protocols),
        "hostr": join_or_default(hostrs),
        "app_type": join_or_default(apps),
        "host_service": join_or_default(apps)
    })
    return result


def get_uuid_str(related_alert_uuids, related_alerts):
    if related_alert_uuids:
        alert_id_list_str = ','.join(BaseContEvent.id_format_msg.format(x) for x in related_alert_uuids)
    else:
        alert_id_list_str = ','.join(BaseContEvent.id_format_msg.format(x) for x in related_alerts.split(";"))
    return alert_id_list_str


def download_docx_report(request, event_id):
    """
    根据选择的模板导出相关报告
    1、除了DDOS 模板以外，其余的模板事件分析这块的列表最多只会有一条数据，因为查的是持续性事件表，不再是查的告警表了
    2、DDOS模板查一周内源ip为当前ip的所有事件，然后 按照 ip对归并 ，最后按流出流量降序 取前10
    3、涉及协议，域名等相关信息的还是得查告警表
    """

    # 基础校验和获取模板路径
    event_obj, file_path, template_name = validate_and_get_template(event_id, request)
    if isinstance(event_obj, JsonResponse):
        return event_obj

    # 根据模板名称获取对应的处理函数
    handler = TEMPLATE_MAP.get(template_name)
    # 如果找到了对应的处理函数，则调用它
    if handler:
        return handler(event_id, request, file_path, template_name)

    try:
        data = basic_parameters(event_id)
        create_time = data["create_time"]
        src_ip = data["src_ip"]
        dst_ip = data["dst_ip"]
        dst_region = data["dst_region"]
        event_type = data["event_type"]
        report_type = data["report_type"]
        sport = data["sport"]
        sport_list = data["sport_list"]
        dport = data["dport"]
        dport_list = data["dport_list"]
        unit_ = data["unit_"]
        related_alerts = event_obj.related_alerts  # 关联告警uuid
        app_type_str = event_obj.app_type  # 应用
        sql_type = extract_sql_type(app_type_str)
        sport = dynamic_port_label(sport, sport_list, "源端口动态变化")
        dport = dynamic_port_label(dport, dport_list, BaseContEvent.dst_port_msg)

        table_list, related_alert_uuids, total_out_bytes, _, start_time, end_time, dst_ips = generate_table_list(
            event_obj, template_name, src_ip)

        n_start_time = start_time.replace("-", "/")
        n_end_time = end_time.replace("-", "/")

        # 将字符串转换为 datetime 对象
        dt = datetime.datetime.strptime(start_time, DATE_TIME_FORMAT)
        event_analysis_start_time = BaseContEvent.date_msg.format(dt.month, dt.day)
        dt2 = datetime.datetime.strptime(end_time, DATE_TIME_FORMAT)
        event_analysis_end_time = BaseContEvent.date_msg.format(dt2.month, dt2.day)

        if len(list(set(dst_ips))) > 1:
            # 如果有多个目的ip，目的地理位置 需要改变
            dst_region = u"境外多个"
            dst_ip = u"地址"

        alert_id_list_str = get_uuid_str(related_alert_uuids, related_alerts)

        # 判断事件是主动还是被动，上传为主动，下载为被动

        initiative_or_passive = judge_initiative(event_type)

        filling_map = {
            "src_ip": src_ip, "src_com": unit_, "dst_ip": dst_ip, "dst_region": dst_region, "start_time": start_time,
            "end_time": end_time, "up_bytesall": total_out_bytes, "sport": sport, "dport": dport,
            "create_time": create_time, "initiative_or_passive": initiative_or_passive,
            "sql_type": sql_type, "e_start_time": event_analysis_start_time,
            "e_end_time": event_analysis_end_time, "n_start_time": n_start_time, "n_end_time": n_end_time
        }
        filling_map["table_list"] = table_list
        alert_data = fetch_alert_data(alert_id_list_str, report_type)
        filling_map.update(alert_data)

        output_filename = template_name.replace("某单位", unit_)
        # 保存文件到服务器的uploads文件夹，文件名使用secure_filename保证安全
        doc = DocxTemplate(file_path)
        doc.render(filling_map)
        rendered_docx = BytesIO()
        doc.save(rendered_docx)
        rendered_docx.seek(0)
        # 打开渲染后的文档
        doc = Document(rendered_docx)
        # 获取模板中的第一个表格
        if doc.tables:
            table = doc.tables[0]
            # 删除示例数据行，只保留表头
            table._element.remove(table.rows[1]._element)
            if template_name == BaseContEvent.ddos_template_name:
                for index, row_data in enumerate(table_list, start=1):
                    # 填充数据行
                    new_row = table.add_row()
                    # 如果是某单位对外发起DDOS事件的报告 第一列是序号
                    new_row.cells[0].text = str(index)
                    new_row.cells[1].text = row_data[0]
                    new_row.cells[2].text = row_data[1]
                    new_row.cells[3].text = row_data[2]
                    new_row.cells[4].text = row_data[3]
                    new_row.cells[5].text = row_data[4]
                    new_row.cells[6].text = row_data[5]
            else:
                for row_data in table_list:
                    # 填充数据行
                    new_row = table.add_row()
                    new_row.cells[0].text = row_data[0]
                    new_row.cells[1].text = row_data[1]
                    new_row.cells[2].text = row_data[2]
                    new_row.cells[3].text = row_data[3]
                    new_row.cells[4].text = row_data[4]
                    new_row.cells[5].text = row_data[5]
        response, docx_file_name = export_word(doc, output_filename)
        auditLog(request, BaseContEvent.export_success_msg.format(docx_file_name))
        return response
    except Exception:
        mlog.error(traceback.format_exc())
        return HttpResponse(BaseContEvent.export_error_msg, status=500, )


def get_docx_template_nams(request):
    """
    获取所有模板的名字
    """
    if request.method != GET_METHOD:
        return HttpResponse("405 Method Not Allowed", status=405)

    data = []
    # 模板路径
    current_path = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(os.path.dirname(current_path), "template")
    for index, file_name in enumerate(os.listdir(template_dir), start=1):
        # 获取文件名（不包括后缀）
        if file_name.startswith("KJ告警模型梳理与统计"):
            continue
        data.append(os.path.splitext(file_name)[0])
    return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})


# 批量研判接口
def event_batch_judge(request):
    try:
        if request.method == "POST":
            data = json.loads(request.body)
            event_id_list = data.get("event_id_list")
            judge_status = data.get("judge_status")
            judge_info = data.get("judge_info", "")
            if not event_id_list:
                return JsonResponse(
                    status=200,
                    data={"code": 500, "msg": "event_id_list is empty"}
                )
            event_res = cont_event_models.get_event_info_by_event_id_list(event_id_list)
            for i in event_res:
                event_id = i[0]
                old_judge_info = i[2]
                if judge_info:
                    if old_judge_info:
                        new_judge_info = old_judge_info + "\n" + judge_info
                    else:
                        new_judge_info = judge_info
                else:
                    new_judge_info = old_judge_info

                cont_event_models.update_judge_info(event_id, int(judge_status), new_judge_info)
            auditLog(request, "持续性事件批量研判成功")
            return JsonResponse(status=200, data={"code": 200, "msg": "success"})

    except Exception as e:
        mlog.exception("批量研判接口出错：{}".format(e))
        return JsonResponse(status=200, data={"code": 500, "msg": "batch judge error"})


def get_tc_time_distribution(request):
    """
        事件详情-通联时间分布接口
    """
    try:
        data = {}
        if request.method == GET_METHOD:
            event_id = request.GET.get("event_id")
            if not event_id:
                return JsonResponse(status=200, data={"code": 500, "msg": "event_id is empty"})
            event_info = cont_event_models.get_event_info_by_event_id(event_id)
            if not event_info:
                return JsonResponse(status=200, data={"code": 500, "msg": "event_id is not exist"})
            related_alerts = event_info[0][10]
            report_type = event_info[0][12]
            reverse_tag = event_info[0][14]
            if not related_alerts:
                return JsonResponse(status=200, data={"code": 500, "msg": "no related alerts", "data": data})
            alert_id_list = related_alerts.split(";")
            (work_up_bytes_all, work_down_bytes_all, work_up_flow_ratio, non_work_up_bytes_all,
             non_work_down_bytes_all, non_work_up_flow_ratio) = cont_event_models.search_alert_working_day(
                alert_id_list, report_type, reverse_tag)
            data = {
                "work_up_bytes_all": bytes_trans.long2unit(work_up_bytes_all),
                "work_down_bytes_all": bytes_trans.long2unit(work_down_bytes_all),
                "work_up_flow_ratio": work_up_flow_ratio,
                "non_work_up_bytes_all": bytes_trans.long2unit(non_work_up_bytes_all),
                "non_work_down_bytes_all": bytes_trans.long2unit(non_work_down_bytes_all),
                "non_work_up_flow_ratio": non_work_up_flow_ratio
            }
        return JsonResponse(status=200, data={"code": 200, "msg": "success", "data": data})
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
    return JsonResponse(status=400, data={"code": 400, "msg": "error", "data": {}})
