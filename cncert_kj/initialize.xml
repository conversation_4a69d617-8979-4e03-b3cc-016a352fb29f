<?xml version="1.0" encoding="utf-8"?>
<!--节点顺序不允许变更，关系到入配置表的顺序-->
<!--请自行保证xml的正确性，由于xml配置、xml格式错误等引起的错误BSA概不负责-->
<root>
    <!--安全日志，定义表结构；如果表使用已存在的安全日志建立，此节点也可以为空-->
    <safelogs log_version="new">
        <!--填入的安全日志如果不存在，则会新建安全日志；如果存在，则会报错阻止安装-->
        <!--name表示安全日志名-->
        <!--reservelog表示是否保留原始日志，1表示保留，0表示不保留；主要影响es中的是否保留原始日志字段-->
        <safelog  name="GJK_T_MERGE" reservelog="0" ignore="true" id="1022" issubpartition="true" noSysCol="true">
            <!--name表示字段名称-->
            <!--type表示字段类型，值仅能为int,bigint,string,float,double,array<string>,map<string,string>-->
            <!--isindex为是否建立索引，默认为建立索引-->
            <!--isparticiple为是否分词，默认为不分词;只有type=string/array<string>/map<string,string>时才有可能需要分词，是具体需求而定-->
            <!--coltag为0表示是普通字段，coltag=1表示是IP字段-->
            <col name="sip" type="string" isindex="0" isparticiple="0" coltag="1"/>
            <col name="dip" type="string" isindex="0" isparticiple="0" coltag="1"/>
            <col name="proto_type" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="tx_packetsall" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="rx_packetsall" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="start_time" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="end_time" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="window_type" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="timestamp" type="int" isindex="0" isparticiple="0" coltag="0"/>
        </safelog>

        <safelog  name="GJK_T_CONNLOG" reservelog="0" ignore="true" id="1023" issubpartition="true" noSysCol="true">
            <col name="c_netnum" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_flowid" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_ipv4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_ipv6" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_tunnel_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_tunnel_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_ipv4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_ipv6" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_tunnel_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_tunnel_port" type="int" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_packet_group" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_proto_type" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_connect_status" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_direct" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_server_dir" type="int" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_up_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_up_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_down_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_down_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_c2s_packet_jitter" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s2c_packet_jitter" type="int" isindex="0" isparticiple="0" coltag="0"/>

            <col name="timestamp" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_app_type" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_stream_time" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_hostr" type="string" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_s_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_region" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_city" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_district" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_operators" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_owner" type="string" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_d_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_region" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_city" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_district" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_operators" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_owner" type="string" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_s_mark1" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark2" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark3" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark5" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark1" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark2" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark3" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark5" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
        </safelog>

        <safelog  name="GJK_T_CONNLOG_V2" reservelog="0" ignore="true" id="1024" issubpartition="true" noSysCol="true">
            <col name="c_netnum" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_flowid" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_ipv4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_ipv6" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_src_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_tunnel_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_tunnel_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_ipv4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_ipv6" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_dest_port" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_tunnel_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_tunnel_port" type="int" isindex="0" isparticiple="0" coltag="0"/>

            <col name="c_packet_group" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_proto_type" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_proto_type_name" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_connect_status" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_direct" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_server_dir" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_up_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_up_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_down_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_down_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_c2s_packet_jitter" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s2c_packet_jitter" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="timestamp" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_app_type" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_stream_time" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_hostr" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_region" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_region_name" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_city" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_district" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_operators" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_operators_name" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_owner" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_region" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_region_name" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_city" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_district" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_operators" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_operators_name" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_owner" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark1" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark2" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark3" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_mark5" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark1" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark2" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark3" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark4" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_mark5" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
        </safelog>

        <safelog  name="GJK_T_CONNLOG_V3" reservelog="0" ignore="true" id="1025" issubpartition="true" noSysCol="true">

            <col name="flow_id" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="sport" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_ip" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dport" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="protocol" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="conn_status" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="up_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="up_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="down_packets" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="down_bytes" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="timestamp" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="app" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="stream_time" type="int" isindex="0" isparticiple="0" coltag="0"/>
            <col name="hostr" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_s_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="c_d_boundary" type="bigint" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_country" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_province" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_city" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_oper" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_country" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_province" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_city" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_oper" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_mark1" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_mark2" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="src_mark3" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_mark1" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_mark2" type="string" isindex="0" isparticiple="0" coltag="0"/>
            <col name="dst_mark3" type="string" isindex="0" isparticiple="0" coltag="0"/>

        </safelog>

        <safelog  name="GJK_T_WHITELIST" reservelog="0" ignore="true" id="1026" issubpartition="true" noSysCol="true">
            <!--新增白名单对应安全日志-->
            <col name="white_ip" type="string" isindex="0" isparticiple="0" coltag="1"/>
        </safelog>


    </safelogs>
    <!--name表示数据库的名称-->
    <!--如果库存在，则保留库，在里面添加表；否则，新建库-->
    <database name="internal_app_bsa_gjk">
        <!--填入的表如果不存在，则会新建表；如果存在，则在安装APP时直接提示该表已存在，阻止安装-->
        <!--name表示表名-->
        <!--safelogname为安全日志名称，可以是库中已存在的安全日志，或XML中新增的安全日志名称-->
        <!--告警源IP流量表;event_id+src_ip-->
        <table name="t_merge" safelogname="GJK_T_MERGE" ignore="true"/>
        <table name="t_connlog" safelogname="GJK_T_CONNLOG" ignore="true"/>
        <table name="t_connlog_v2" safelogname="GJK_T_CONNLOG_V2" ignore="true"/>
        <table name="t_connlog_v3" safelogname="GJK_T_CONNLOG_V3" ignore="true"/>
        <!--新增白名单对应表-->
        <table name="t_whitelist" safelogname="GJK_T_WHITELIST" ignore="true"/>
    </database>
    <!--解析器，可填入多个；如果存在同名解析器，则表中不予插入，但会将jar包覆盖；对应plugins下的jar文件-->
    <analyzers>
        <analyzer name="bsa_csvflow_parser" format_key="json"/>
    </analyzers>
    <!--规则，在进行规则导入后，会先删除app_rule中该app的所有规则，再进行规则的插入-->
    <rules>
        <rule>
            <dbname>internal_app_bsa_gjk</dbname>
            <tablename>t_merge</tablename>
            <analyzer>bsa_csvflow_parser</analyzer>
            <recongnize_rule><![CDATA[*]]></recongnize_rule>
            <format_rule><![CDATA[]]></format_rule>
        </rule>
        <rule>
            <dbname>internal_app_bsa_gjk</dbname>
            <tablename>t_connlog</tablename>
            <analyzer>bsa_csvflow_parser</analyzer>
            <recongnize_rule><![CDATA[*]]></recongnize_rule>
            <format_rule><![CDATA[]]></format_rule>
        </rule>
        <rule>
            <dbname>internal_app_bsa_gjk</dbname>
            <tablename>t_connlog_v2</tablename>
            <analyzer>bsa_csvflow_parser</analyzer>
            <recongnize_rule><![CDATA[*]]></recongnize_rule>
            <format_rule><![CDATA[]]></format_rule>
        </rule>
        <rule>
            <dbname>internal_app_bsa_gjk</dbname>
            <tablename>t_connlog_v3</tablename>
            <analyzer>bsa_csvflow_parser</analyzer>
            <recongnize_rule><![CDATA[*]]></recongnize_rule>
            <format_rule><![CDATA[]]></format_rule>
        </rule>
       <rule>
           <!--新增白名单对应安全rule-->
            <dbname>internal_app_bsa_gjk</dbname>
            <tablename>t_whitelist</tablename>
            <analyzer>bsa_csvflow_parser</analyzer>
            <recongnize_rule><![CDATA[*]]></recongnize_rule>
            <format_rule><![CDATA[]]></format_rule>
        </rule>
    </rules>
</root>