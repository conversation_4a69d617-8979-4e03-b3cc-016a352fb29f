#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
单位阈值告警模型
每10分钟执行1次
检测当天单位的流量是否超过设置的阈值，超过则会产生告警，IP为单位的随机一个IP
"""
import datetime
import json
import os
import time
import traceback
from uuid import uuid4

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.static_baseline_model import get_dynamic_baseline_status
from cncert_kj.models.tllog_alert_models import push_alert_data, send_rule
from cncert_kj.script.alert_day_model.day_filter_rule import get_filter_rule
from cncert_kj.script.event_asset_info import GetAssetInfo
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.script.update_cont_event import update_tllog_sql, update_netflow_sql
from cncert_kj.utils import net, params_validate, logger, conf_util
from cncert_kj.utils.es_utils import ESUtil
from cncert_kj.utils.redis_utils import cache
from cncert_kj.utils.request_filing_sys import RequestFilingSys

mlog = logger.init_logger('day_model_unit_threshold_alarm')
TCLOG_INDEX = conf_util.TLLOG_IP_DAY
NETFLOW_INDEX = conf_util.NETFLOW_IP_DAY


class ThresholdAlarmModel:
    """
    单位阈值告警脚本
        1、查询境内单位和境外单位  get_units
        2、根据单位计算流出和流入流量  search_bytes
        3、判断超过阈值的单位
        4、超过阈值生成告警，下发阻断，时间范围当天
        5、记录生成告警和下发阻断的单位，  不管通联还是netflow， 单位的IP是一样的，同时都有需要生成两个告警，但只需要下发一个阻断规则
        6、下次查询排除这些单位
    """

    def __init__(self):
        """
        """
        self.expire = 60 * 60 * 24 * 30  # 记录留存30天
        self.type = 81
        self.analysis_tech = 2
        self.neflow_fields = ','.join((
            "uuid",
            "name",
            "type",
            "start_time",
            "end_time",
            "sip",
            "src_region",
            "src_operator",
            "src_iot",
            "src_service",
            "src_unit",
            "src_com",
            "sport",
            "dip",
            "dst_region",
            "dst_operator",
            "dst_iot",
            "dst_service",
            "dst_unit",
            "dst_com",
            "dport",
            "bytes_all",
            "bytes_all_down",
            "packets_all",
            "packets_all_down",
            "analysis_tech",
            "flow_logs",
            "ipv6",
            "port_distribution"
        ))
        self.tllog_fields = ','.join((
            "uuid",
            "name",
            "type",
            "start_time",
            "end_time",
            "sip",
            "src_region",
            "src_operator",
            "src_iot",
            "src_service",
            "src_unit",
            "src_com",
            "sport",
            "dip",
            "dst_region",
            "dst_operator",
            "dst_iot",
            "dst_service",
            "dst_unit",
            "dst_com",
            "dport",
            "up_bytes_all",
            "down_bytes_all",
            "up_packets_all",
            "down_packets_all",
            "flow_list",
            "analysis_tech",
            "flow_logs",
            "ipv6",
            "port_distribution"
        ))
        self.tllog_insert_sql_template = """
        INSERT INTO internal_app_bsa_gjk.traffic_alert ({fields}) VALUES ({params})
        """
        self.netflow_insert_sql_template = """
        INSERT INTO internal_app_bsa_gjk.netflow_alert_5min ({fields}) VALUES ({params})
        """

    def get_units(self):
        """
        区分通联和Netflow
        """
        unit_sql = "SELECT id, name, ip_range, threshold,tclog_key_monitor, netflow_key_monitor, tclog_dynamic_baseline, boundary FROM internal_app_bsa_gjk.key_unit WHERE tclog_dynamic_baseline=true"

        exist_id_list = list(cache.execute("smembers", self.generate_key()))
        mlog.info("exist_id_list--------------------:{}".format(exist_id_list))
        if exist_id_list:
            # 排除已经产生告警的单位
            unit_sql += " AND id NOT IN %s"
            unit_res = json.loads(CFunction.execute(CPgSqlParam(unit_sql, params=(tuple(exist_id_list),))))
        else:
            unit_res = json.loads(CFunction.execute(CPgSqlParam(unit_sql)))
        # 境内单位
        in_tl_key_unit_data = {}
        in_netflow_key_unit_data = {}
        # 境外单位
        out_tl_key_unit_data = {}
        out_netflow_key_unit_data = {}
        if unit_res:
            return self.handle_unit(unit_res)
        return in_tl_key_unit_data, in_netflow_key_unit_data, out_tl_key_unit_data, out_netflow_key_unit_data

    def ips_to_ip_list(self, asset_ips, name):
        ip_list = []
        for ip_ in asset_ips:
            ip_ = ip_.strip()
            if not ip_:
                continue
            ip_check = params_validate.validate_ipv4(ip_)
            if ip_check:
                ip_list.append(ip_)
            else:
                mlog.error(u"错误IP：{} ， 单位：{}".format(ip_, name))
        return ip_list

    def handle_unit(self, unit_res):
        in_tl_key_unit_data = in_netflow_key_unit_data = out_tl_key_unit_data = out_netflow_key_unit_data = {}
        for item in unit_res:
            id_ = item[0]
            name = item[1]
            ip_range = item[2]
            threshold = item[3]  # 单位GB 是否需要转换为  byte
            if not threshold:
                mlog.error("===============================================:{}   {}".format(name, threshold))
                continue
            threshold = threshold * 1024 * 1024
            tclog_key_monitor = item[4]
            netflow_key_monitor = item[5]
            boundary = item[7]
            asset_ips = net.ips_to_list(ip_range.split(","))
            ip_list = self.ips_to_ip_list(asset_ips, name)
            data = {
                "id": id_,
                "name": name,
                "ip_range": ip_list,
                "threshold": threshold,
            }
            if boundary == 1:
                if tclog_key_monitor:
                    out_tl_key_unit_data[name] = data
                if netflow_key_monitor:
                    out_netflow_key_unit_data[name] = data
            elif boundary == 2:
                if tclog_key_monitor:
                    in_tl_key_unit_data[name] = data
                if netflow_key_monitor:
                    in_netflow_key_unit_data[name] = data
        return in_tl_key_unit_data, in_netflow_key_unit_data, out_tl_key_unit_data, out_netflow_key_unit_data

    def search_bytes(self, index, key_units, flow):
        """
        不同索引查询 流量
        并判断是否超过阈值
        返回超过阈值的数据
        """
        s = int(time.time())
        start_time, end_time = self.get_now_timestamp()
        es = ESUtil()
        query_dsl = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "asset_name": key_units.keys()
                            }
                        },
                        {
                            "range": {
                                "end_time": {
                                    "gte": start_time,
                                    "lte": end_time
                                }
                            }
                        }
                    ]
                }
            },
            "size": 0,
            "aggregations": {
                "asset_name": {
                    "terms": {
                        "field": "asset_name",
                        "size": 5000
                    },
                    "aggregations": {
                        "out_bytes_sum": {
                            "sum": {
                                "field": "out_bytes"
                            }
                        },
                        "in_bytes_sum": {
                            "sum": {
                                "field": "in_bytes"
                            }
                        },
                        "dport_hits": {
                            "top_hits": {
                                "size": 1,
                                "_source": {
                                    "includes": [
                                        "dport"
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }

        result = es.search_es(index, query_dsl)
        if not result:
            result = {}
        buckets = result.get("aggregations", {}).get("asset_name", {}).get("buckets", [])
        data = []
        mlog.info("-------------------------{}".format(len(buckets)))
        for bucket in buckets:
            key_unit = bucket.get("key")
            out_bytes = bucket.get("out_bytes_sum", {}).get("value", 0)
            in_bytes = bucket.get("in_bytes_sum", {}).get("value", 0)
            threshold = key_units.get(key_unit, {}).get("threshold")
            _source = bucket['dport_hits']["hits"]["hits"][0]["_source"]
            dport = _source.get("dport", None) if _source else None
            # 境外算流入，  境内算流出
            if flow == "out_bytes" and out_bytes > threshold:
                data.append(
                    {
                        "asset_name": key_unit,
                        "key_unit_id": key_units.get(key_unit, {}).get("id"),
                        "asset_ip": key_units.get(key_unit, {}).get("ip_range"),
                        "in_bytes": in_bytes,
                        "out_bytes": out_bytes,
                        "start_time": start_time,
                        "end_time": int(time.time()),
                        "dport": dport
                    }
                )
            if flow == "in_bytes" and in_bytes > threshold:
                data.append(
                    {
                        "asset_name": key_unit,
                        "key_unit_id": key_units.get(key_unit, {}).get("id"),
                        "asset_ip": key_units.get(key_unit, {}).get("ip_range"),
                        "in_bytes": in_bytes,
                        "out_bytes": out_bytes,
                        "start_time": start_time,
                        "end_time": int(time.time()),
                        "dport": dport
                    }
                )
        e = int(time.time())
        mlog.info("本次查询时长：{}".format(e - s))
        return data

    def generate_alarm(self, data, typ):
        """
            生成告警数据
        """
        tllog_alert_params = []
        netflow_alert_params = []
        tc_uuid_list = []
        netflow_uuid_list = []
        ip_lst = []
        key_unit_id_list = []
        ip_range_data = {}
        for item in data:
            uuid_ = str(uuid4())
            asset_name = item.get("asset_name")
            ip_range = item.get("asset_ip")
            asset_ip = ip_range[0]
            ip_range_data[asset_ip] = ip_range
            in_bytes = int(item.get("in_bytes"))
            out_bytes = int(item.get("out_bytes"))
            start_time = item.get("start_time")
            end_time = item.get("end_time")
            dport = item.get("dport")
            key_unit_id = item.get("key_unit_id")
            name = "重点监控目标流量超过阈值告警"
            ipv6 = False
            # >> IP都是IPV4的
            # if is_ipv4_correct(asset_ip):
            #     ipv6 = False
            # else:
            #     ipv6 = True
            if typ == "tclog_in":

                tllog_alert_params.append([uuid_, name, self.type, start_time, end_time, asset_ip,
                                           "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", dport,
                                           out_bytes, in_bytes, 0, 0, "", self.analysis_tech, [], ipv6, 3, key_unit_id])
                tc_uuid_list.append(uuid_)
            elif typ == "tclog_out":
                tc_uuid_list.append(uuid_)
                tllog_alert_params.append(
                    [
                        uuid_, name, self.type, start_time, end_time, asset_ip,
                        "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", dport,
                        out_bytes, in_bytes, 0, 0, "", self.analysis_tech, [], ipv6, 3, key_unit_id
                    ]
                )
            elif typ == "netflow_in":

                netflow_alert_params.append(
                    [
                        uuid_, name, self.type, start_time, end_time, asset_ip,
                        "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", dport,
                        out_bytes, in_bytes, 0, 0, self.analysis_tech, [], ipv6, 3, key_unit_id
                    ]
                )
                netflow_uuid_list.append(uuid_)
            elif typ == "netflow_out":
                netflow_uuid_list.append(uuid_)
                netflow_alert_params.append([uuid_, name, self.type, start_time, end_time, asset_ip,
                                             "", "", "", "", asset_name, "", -1, "", "", "", "", "", "", "", dport,
                                             out_bytes, in_bytes, 0, 0, self.analysis_tech, [], ipv6, 3, key_unit_id])

            ip_lst.append(asset_ip)
        with RequestFilingSys() as request_filing_obj:
            ip_info = request_filing_obj.query_icp_api(ip_lst)
        rules = get_filter_rule()
        if not rules:
            rules = {}
        src_com_filter = set(rules.get("2", []))
        if tllog_alert_params:
            mlog.info("待推送告警数量一：{}".format(len(netflow_alert_params)))
            n, new_tllog_alert_params = self.tc_push_and_insert(tllog_alert_params, ip_range_data, ip_info,
                                                                src_com_filter, key_unit_id_list)
            mlog.info("重点监控单位的IP数量：{}".format(n))
            mlog.info("生成通联告警数量：{}".format(len(new_tllog_alert_params)))
        if netflow_alert_params:
            mlog.info("待推送告警数量二：{}".format(len(netflow_alert_params)))
            n, new_netflow_alert_params = self.nt_push_and_insert(netflow_alert_params, ip_range_data, ip_info,
                                                                  src_com_filter, key_unit_id_list)

            mlog.info("重点监控单位的IP数量：{}".format(n))

            mlog.info("生成netflow告警数量：{}".format(len(new_netflow_alert_params)))

        if tc_uuid_list:
            if len(tc_uuid_list) == 1:
                tc_filter_condition = "uuid = '{}'".format(tc_uuid_list[0])
            else:
                tc_filter_condition = "uuid in {}".format(tuple(tc_uuid_list))
            update_tllog_sql(tc_filter_condition)
        if netflow_uuid_list:
            if len(netflow_uuid_list) == 1:
                filter_condition = "uuid = '{}'".format(netflow_uuid_list[0])
            else:
                filter_condition = "uuid in {}".format(tuple(netflow_uuid_list))
            update_netflow_sql(filter_condition)

        if key_unit_id_list:
            mlog.info("生成告警的单位ID： {}".format(key_unit_id_list))
            cache.execute("sadd", self.generate_key(), *key_unit_id_list)
            cache.execute("expire", self.generate_key(), self.expire)

    def tc_push_and_insert(self, tllog_alert_params, ip_range_data, ip_info, src_com_filter, key_unit_id_list):
        n = 0
        new_tllog_alert_params = []
        for i in tllog_alert_params:
            unit_id = i.pop()
            asset_ip = i[5]
            alert_id = i[0]
            ip_range = ip_range_data[asset_ip]
            ip_detail = ip_info.get(asset_ip, {})
            src_country = ip_detail.get("country", "")
            src_province = ip_detail.get("province", "")
            src_operator = ip_detail.get("operator", "")
            src_com = ip_detail.get("user", "")
            src_region = " ".join([src_country, src_province])
            i[6], i[7], i[11] = src_region, src_operator, src_com
            if src_com in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(src_com))
                continue
            try:
                data = self.format_data(i, 1)
                # 推送告警
                push_res = push_alert_data(data)
                mlog.info("推送结果为：{} \n 需要下发阻断的IP个数：{}".format(push_res, len(ip_range)))
                # 下发阻断
                self.blocking_up(ip_range, alert_id, alert_type=1, unit_id=unit_id)
                new_tllog_alert_params.append(tuple(i))
                val = ",".join(len(i) * ["%s"])
                CFunction.execute(
                    CPgSqlParam(
                        self.tllog_insert_sql_template.format(fields=self.tllog_fields, params=val),
                        params=tuple(i))
                )
                # 推送下发成功记录id
                key_unit_id_list.append(unit_id)
                n += len(ip_range)
            except Exception as e:
                mlog.error("推送-阻断失败：{}， 单位：{}".format(e, unit_id))
                mlog.error(traceback.format_exc())
        return n, new_tllog_alert_params

    def nt_push_and_insert(self, netflow_alert_params, ip_range_data, ip_info, src_com_filter, key_unit_id_list):
        n = 0
        new_netflow_alert_params = []
        for i in netflow_alert_params:
            unit_id = i.pop()
            asset_ip = i[5]
            alert_id = i[0]
            ip_range = ip_range_data[asset_ip]
            ip_detail = ip_info.get(asset_ip, {})
            src_region = ip_detail.get("src_region", "")
            src_operator = ip_detail.get("operator", '')
            src_com = ip_detail.get("user", "")
            i[6], i[7], i[11] = src_region, src_operator, src_com
            if src_com in src_com_filter:
                mlog.info("备案单位：{}， 已下发为白名单事件过滤规则".format(src_com))
                continue
            try:
                data = self.format_data(i, 2)
                # 推送告警
                push_res = push_alert_data(data)
                mlog.info("推送结果为：{} \n 需要下发阻断的IP个数：{}".format(push_res, len(ip_range)))
                # 下发阻断
                self.blocking_up(ip_range, alert_id, alert_type=2, unit_id=unit_id)
                new_netflow_alert_params.append(tuple(i))
                val = ",".join(len(i) * ["%s"])
                CFunction.execute(
                    CPgSqlParam(
                        self.netflow_insert_sql_template.format(fields=self.neflow_fields, params=val),
                        params=tuple(i))
                )
                # 推送下发成功记录id
                key_unit_id_list.append(unit_id)
                n += len(ip_range)
            except Exception as e:
                mlog.error("推送-阻断失败：{}, 单位：{}".format(e, unit_id))
                mlog.error(traceback.format_exc())
        return n, new_netflow_alert_params

    def blocking_up(self, ip_range, alert_id, alert_type, unit_id):
        """阻断下发"""
        if not cache.execute("sismember", self.generate_key(), unit_id):
            """
            当ID不存在与redis中时，才下发阻断规则，避免重复下发
            """
            start_time, end_time = self.get_now_timestamp()
            for ip_ in ip_range:
                rule = {
                    "alert_id": alert_id,
                    "sip": ip_,
                    "sport": "",
                    "dip": "",
                    "dport": "",
                    "start_time": start_time,
                    "end_time": end_time,
                    "push_time": int(time.time())
                }
                send_rule(rule=rule, alert_id=alert_id, alert_type=alert_type, is_two_way=1)
        else:
            mlog.info("该单位已下发规则，无需重复下发， 单位ID：{}".format(unit_id))

    def format_data(self, params, flow_type):
        if flow_type == 1:
            log_type = "tllog"
        else:
            log_type = "netflow"
        data = {
            "alert_id": params[0],
            "name": params[1],
            "src_unit": params[10],
            "sip": params[5],
            "sport": params[12],
            "src_com": params[11],
            "src_region": params[6],
            "src_operator": params[7],
            "start_time": params[3],
            "end_time": params[4],
            "custom_tags": "",
            "type": params[2],
            "dip": params[13],
            "dport": params[20],
            "dst_com": params[19],
            "dst_region": params[14],
            "dst_operator": params[15],
            "up_bytes_all": params[21] if params[21] else 0,
            "up_packets_all": params[23] if params[23] else 0,
            "down_bytes_all": params[22] if params[22] else 0,
            "down_packets_all": params[24] if params[24] else 0,
            "alarm_time": "",  # 不确定,暂留空
            "push_time": int(time.time()),
            "log_type": log_type,
        }
        return data

    @staticmethod
    def get_now_timestamp():
        """
        获取当天的凌晨与次日凌晨的时间戳为开始/结束时间
        """
        start_time = int(time.mktime(datetime.date.today().timetuple()))
        end_time = start_time + 60 * 60 * 24
        return start_time, end_time

    @staticmethod
    def generate_key():
        """Redis Key"""
        date = datetime.datetime.now().strftime("%Y%m%d")
        key = "unit_threshold_alarm_key_{}".format(date)
        return key

    def run(self):
        begin_time = int(time.time())
        try:
            tc_status = get_dynamic_baseline_status(self.type, 2)
            netflow_status = get_dynamic_baseline_status(self.type, 1)
            if tc_status or netflow_status:
                in_tl_unit_data, in_netflow_unit_data, out_tl_unit_data, out_netflow_unit_data = self.get_units()
                if tc_status:
                    mlog.info("通联重点监控目标流量阈值告警已开启")
                    # 境外算流入，  境内算流出
                    tc_in_data = self.search_bytes(TCLOG_INDEX, in_tl_unit_data, "out_bytes")
                    tc_out_data = self.search_bytes(TCLOG_INDEX, out_tl_unit_data, "in_bytes")
                    self.generate_alarm(tc_in_data, "tclog_in")
                    self.generate_alarm(tc_out_data, "tclog_out")
                    mlog.info("境内通联单位数量-------------------------------:{}".format(len(in_tl_unit_data)))
                    mlog.info("境外通联单位数量-------------------------------:{}".format(len(out_tl_unit_data)))
                    mlog.info("tc_in_data---------------------------------:{}".format(len(tc_in_data)))
                    mlog.info("tc_out_data--------------------------------:{}".format(len(tc_out_data)))
                else:
                    mlog.info("通联重点监控目标流量阈值告警未开启")
                mlog.info("------------------------------------------------------------------------------------------")
                if netflow_status:
                    mlog.info("netflow重点监控目标流量阈值告警已开启")
                    # 境外算流入，  境内算流出
                    netflow_in_data = self.search_bytes(NETFLOW_INDEX, in_netflow_unit_data, "out_bytes")
                    netflow_out_data = self.search_bytes(NETFLOW_INDEX, out_netflow_unit_data, "in_bytes")
                    self.generate_alarm(netflow_in_data, "netflow_in")
                    self.generate_alarm(netflow_out_data, "netflow_out")
                    mlog.info("境内netflow单位数量---------------------------:{}".format(len(in_netflow_unit_data)))
                    mlog.info("境外netflow单位数量---------------------------:{}".format(len(out_netflow_unit_data)))
                    mlog.info("netflow_in_data----------------------------:{}".format(len(netflow_in_data)))
                    mlog.info("netflow_out_data---------------------------:{}".format(len(netflow_out_data)))
                else:
                    mlog.info("netflow重点监控目标流量阈值告警未开启")
            else:
                mlog.info("未开启重点监控目标流量阈值告警模型")
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())
        stop_time = int(time.time())
        mlog.info("任务执行总时长：{} 秒".format(stop_time - begin_time))


class Lock(object):

    def __init__(self):
        self.LOCK_PATH = "/tmp/THRESHOLD_ALARM.LOCK"

    def is_locked(self):
        return True if os.path.exists(self.LOCK_PATH) else False

    def get_lock(self):
        f = open(self.LOCK_PATH, "w")
        f.write("locked")
        f.flush()
        f.close()

    def release_lock(self):
        if self.is_locked():
            os.remove(self.LOCK_PATH)


if __name__ == '__main__':
    try:
        ta = ThresholdAlarmModel()
        ta.run()
        today = datetime.date.today()
        stime = int(time.mktime(today.timetuple())) - 3600
        etime = int(time.time())
        analysis_tech = 2
        mlog.info("开始增强资产信息")
        GetAssetInfo(analysis_tech=analysis_tech, start_time=stime, end_time=etime,
                     event_type=[81]).enhance_event_asset_info()
        mlog.info("开始增强恶意标签信息")
        EventMaliciousTag().run(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[81])
        mlog.info("开始增强情报信息")
        enhance_event_intelligence_info(analysis_tech=analysis_tech, start_time=stime, end_time=etime, event_type=[81])
    except Exception as e:
        mlog.error("运行失败")
        mlog.error(traceback.format_exc())
