#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    远程登录事件研判脚本
    每5分钟运行一次
"""
import datetime
import time
import os
import django
# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from cncert_kj.script.cont_event_judge import EventJudgeUtil
from cncert_kj.utils import logger, lock_util

mlog = logger.init_logger('remote_login_judge')


class RemoteLoginJudge(EventJudgeUtil):
    def __init__(self, event_type_name, record_key):
        super(RemoteLoginJudge, self).__init__(event_type_name, record_key)
        self.post_list = []

    def condition(self, protocol_list, product_list, args=[]):
        """
            是否为真实服务的判断逻辑,其他服务可重写此处
        """
        protocol_list = " ".join(protocol_list).lower()
        product_list = " ".join(product_list).lower()
        if any([
            "ssh" in protocol_list,
            "ssh" in product_list,
            "rdp" in protocol_list,
            "rdp" in product_list,
        ]):
            return True
        return False

    def run(self):
        """
            二次研判探测任务下发未返回数据的事件
        """
        t1 = datetime.datetime.now()
        mlog.info("========开始{}类型事件研判：{}".format(self.event_type_name, t1))

        # 获取上次事件研判时间
        st = self.aj.get_conf_value(self.record_key) - 3600 * 24 * 3
        et = int(time.time()) + 3600 * 2
        # 查询持续性事件
        filter_res = self.aj.filter_cont_event(st, et, self.types, self.filter_judge_status)
        mlog.info("持续性事件数量：{}".format(len(filter_res)))

        record_id = 0
        record_end_time = st
        try:
            if len(filter_res) == 0:
                t = datetime.datetime.now()
                mlog.info("=======无事件，研判结束：{}秒".format((t - t1).total_seconds()))
                return

            for i in filter_res:
                if i:
                    event_id = i.get("event_id")
                    record_id = event_id
                    # mlog.info("当前事件id:{}".format(event_id))
                    event_type = i.get("event_type")
                    end_time = i.get("end_time")

                    src_ip = i.get("src_ip")
                    src_info = i.get("src_info")
                    #>> src_service_list = [k for k in i.get("src_service") if k]
                    # src_service = src_service_list[0] if src_service_list else ""

                    dst_ip = i.get("dst_ip")
                    dst_info = i.get("dst_info")
                    #>> dst_service_list = [k for k in i.get("dst_service") if k]
                    # dst_service = dst_service_list[0] if dst_service_list else ""

                    judge_info = i.get("judge_info")

                    if end_time > record_end_time:
                        record_end_time = end_time

                    # 区分主被动类型
                    upload_types, download_type = self.aj.up_or_down_types(self.types)

                    # 主动类型的查目的ip，目的信息
                    if event_type in upload_types:
                        ip_ = dst_ip
                        #>> sql_service = dst_service

                    # 被动类型的查源ip，源信息
                    elif event_type in download_type:
                        ip_ = src_ip
                        #>> sql_service = src_service

                    else:
                        mlog.info("id:{},事件类型错误:{}，跳过".format(event_id, event_type))
                        continue
                    #>> sql_service = sql_service.encode("utf-8")
                    # post_list = self.get_post_list(sql_service)
                    protocol_list, product_list = self.aj.search_asset_url(ip_)
                    mlog.info("----id:{},ip:{},资产结果：{}, {}".format(event_id, ip_, protocol_list, product_list))

                    # 其他判断条件放args中
                    is_true = self.condition(protocol_list, product_list)

                    if is_true:
                        mlog.info("ip:{},是{}".format(ip_, self.real_ch))
                        judge_status = 7  # 自动研判，未通报
                        # 是真实ftp，但是，没有弱口令，不改变处置状态
                        if judge_info:
                            judge_info += "\n" + "是否是{}：【是】".format(self.real_ch)
                        else:
                            judge_info = "是否是{}：【是】".format(self.real_ch)
                    else:
                        judge_status = 6  # 误报
                        mlog.info("ip:{},研判结果为：误报".format(ip_))
                        # 误报，直接修改处置状态为 6，换下一个事件
                        self.aj.update_judge_info(event_id, judge_status)
                        continue

                    # 主动类型的查目的ip，目的信息
                    if event_type in upload_types:
                        dst_info += '&{}'.format(self.real_ch)
                        self.aj.update_judge_info(event_id, judge_status, judge_info=judge_info, dst_info=dst_info)
                        # 同步到事件扩展表中
                        self.cet_obj.filter_and_insert(event_id, 2, self.real_en, self.real_ch)
                    # 被动类型的查源ip，源信息
                    else:
                        src_info += '&{}'.format(self.real_ch)
                        self.aj.update_judge_info(event_id, judge_status, judge_info=judge_info, src_info=src_info)
                        # 同步到事件扩展表中
                        self.cet_obj.filter_and_insert(event_id, 1, self.real_en, self.real_ch)

            self.aj.update_conf_value(self.record_key, record_end_time)
            t2 = datetime.datetime.now()
            mlog.info("=======研判结束：{},耗时：{}秒".format(t2, (t2 - t1).total_seconds()))
        except Exception as e:
            mlog.exception("研判失败，记录当前id:{}，失败原因：{}".format(record_id, e))


if __name__ == '__main__':
    # 脚本运行
    RemoteLoginJudge("远程登录默认端口", "remote_login_judge_timestamp").run()
    mlog.info("=================结束任务=================")
