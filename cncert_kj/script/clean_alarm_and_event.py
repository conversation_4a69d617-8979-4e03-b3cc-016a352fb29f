#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
事件/告警清理脚本
每天凌晨1点执行一次
用于清理 未研判和误报的事件，及其关联告警数据
"""
import ConfigParser
import argparse
import datetime
import json
import os
import time
import traceback

from cncert_kj.conf.constant import PATH_APP
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.base_model import ContinuousEventsTagModel
from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import PORT_DISTRIBUTION_MAP
from cncert_kj.utils.time_trans import timestamp2format

mlog = logger.init_logger("clean_alarm_and_event")


class CleanAlarmAndEvent:
    """
        自动清理持续性事件和告警数据
        1、每天执行定时任务，清理半年前的数据
        2、条件为未研判、误报的数据
        3、同时清理关联的告警数据、以及告警关联的数据
    """

    def __init__(self, event_type, report_type, analysis_tech, is_delete, retention_days, port_distribution=None):
        self.event_type = event_type
        self.report_type = report_type
        self.analysis_tech = analysis_tech
        self.retention_days = retention_days
        self.port_distribution = port_distribution
        # 是否执行删除操作
        self.is_delete = is_delete

        self.tc_type = 1
        self.netflow_type = 2
        self.step = 1000
        self.tc_days, self.netflow_days = self.get_conf()

        # 告警/事件表
        self.tc_alarm_table_name = "traffic_alert"
        self.netflow_alarm_table_name = "netflow_alert_5min"
        self.event_table_name = "continuous_events"

        # 事件详情表
        self.event_details_table_name = "event_details"

        # 告警研判记录表
        self.tc_record_table_name = "tllog_alert_record"
        self.netflow_record_table_name = "netflow_alert_5min_record"

        # 自定义标签关联表
        self.tc_relate_tag_table_name = "traffic_alert_relate_tag"
        self.netflow_relate_tag_table_name = "netflow_alert_5min_relate_tag"
        self.event_relate_tag_table_name = "continuous_event_relate_tag"

    def get_conf(self):
        """"""
        config = ConfigParser.ConfigParser()
        conf_path = os.path.join(PATH_APP, "conf/clean.conf")
        config.read(conf_path)
        return int(config.get("clean", "tc_days")), int(config.get("clean", "netflow_days"))  # 保留文件天数

    def add_month_ago(self, sql, params, month_ago, not_judge):
        """
            一个月前误报都删除,  一个月前未研判的并且流出流量小于100MB的
        """
        if month_ago and not_judge:
            month_ago_time = int(
                time.mktime((datetime.datetime.now() - datetime.timedelta(days=month_ago)).timetuple()))
            # 一个月前误报都删除
            sql += " OR (  judge_status = %s  and end_time < %s) "
            params.extend([6, month_ago_time])
            # 一个月前未研判的并且流出流量小于100MB的
            sql += " OR (judge_status = %s AND up_bytesall < %s  and end_time < %s) "
            params.extend([1, not_judge, month_ago_time])
        return sql, params

    def get_events(self, report_type, month_ago=None, not_judge=None):
        """
        查询半年前的未研判和误报的事件
        查询半年前的事件数据 where judge_status in (1, 6) AND end_time < 半年前的时间戳
        report_type:
        """
        # 半年前时间戳   当前时间戳-180天
        # timestamp = int(time.mktime((datetime.datetime.now() - datetime.timedelta(days=180)).timetuple()))
        if report_type == self.tc_type:
            mlog.info("查询通联日志未研判和误报的事件")
            days = self.tc_days
        else:
            mlog.info("查询NetFlow未研判和误报的事件")
            days = self.netflow_days
        # 1、指定清理数据，保留天数
        if self.retention_days:
            days = self.retention_days
        mlog.info("保留天数{}天".format(days))
        timestamp = int(time.mktime((datetime.datetime.now() - datetime.timedelta(days=days)).timetuple()))

        sql = """SELECT event_id, related_alerts, report_type FROM internal_app_bsa_gjk.{table_name}  where (judge_status in (1, 6) AND end_time < %s AND report_type=%s""".format(
            table_name=self.event_table_name)
        params = [timestamp, report_type]
        # 2、指定清理事件类型
        if self.event_type:
            event_type = self.event_type.split(",")
            if len(event_type) == 1:
                event_type *= 2
            event_type = tuple([int(i) for i in event_type])
            sql += " AND event_type in %s"
            params.append(event_type)
        # 3、指定事件是 静态还是动态
        if self.analysis_tech:
            sql += " AND analysis_tech=%s"
            params.append(self.analysis_tech)
        sql += " ) "
        # 这个拼接的sql放在最后面
        sql, params = self.add_month_ago(sql, params, month_ago, not_judge)
        mlog.info("查询未研判和误报的事件SQL: {}".format(sql))
        mlog.info("时间戳: {} ， {}".format(timestamp, timestamp2format(timestamp)))
        mlog.info("params: {}".format(params))
        event_list = json.loads(CFunction.execute(CPgSqlParam(sql, params=tuple(params))))
        event_id_list = []
        alarm_id_list = []
        if event_list and event_list[0]:
            event_ids = [item[0] for item in event_list]
            if self.port_distribution:
                # 如果存在 端口分布参数，需要过滤对应数据
                mlog.info("接收到端口分布参数：{}".format(self.port_distribution))
                tag_content = PORT_DISTRIBUTION_MAP.get(self.port_distribution, "")
                mlog.info("端口分布：{}".format(tag_content))
                if tag_content:
                    # 开始过滤对应端口分布的事件
                    event_ids = self.batch_query_event_ids(event_ids, tag_content=tag_content)
                    mlog.info("该端口分布：{} 的事件ID数量：{}".format(tag_content, len(event_ids)))
            event_ids = set(event_ids)
            for item in event_list:
                event_id = item[0]
                if event_id in event_ids:
                    related_alerts = item[1]
                    event_id_list.append(event_id)
                    alarm_id_list.extend(related_alerts.split(";"))
        return event_id_list, alarm_id_list

    def clean_alert_record(self, alarm_ids, alarm_type):
        """
        删除告警的研判记录
        return: record_id
        """
        if alarm_type == self.tc_type:
            table_name = self.tc_record_table_name
        else:
            table_name = self.netflow_record_table_name
        sql = """DELETE FROM internal_app_bsa_gjk.{table_name} WHERE alert_id in %s""".format(table_name=table_name)
        mlog.info("删除告警的研判记录SQL: {}".format(sql))
        mlog.info("告警ID数量 ：{}".format(len(alarm_ids)))
        n = 0
        for i in range(0, len(alarm_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批告警的研判记录， 数量：{} 条".format(n, len(alarm_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(alarm_ids[i: i + self.step]),)))
            mlog.info("第{}批告警的研判记录删除成功！".format(n))
        mlog.info("----------------------------{}告警的研判记录删除完成----------------------------\n\n\n\n".format(table_name))

    def clean_alert_relate_tag(self, alarm_ids, alarm_type):
        """
        删除告警的自定义标签
        """
        if alarm_type == self.tc_type:
            table_name = self.tc_relate_tag_table_name
        else:
            table_name = self.netflow_relate_tag_table_name
        sql = """DELETE FROM internal_app_bsa_gjk.{table_name} WHERE alert_id in %s""".format(table_name=table_name)
        mlog.info("删除告警的自定义标签SQL: {}".format(sql))
        mlog.info("告警ID数量：{}".format(len(alarm_ids)))
        n = 0
        for i in range(0, len(alarm_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批告警的自定义标签， 数量：{} 条".format(n, len(alarm_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(alarm_ids[i: i + self.step]),)))
            mlog.info("第{}批告警的自定义标签删除成功！".format(n))
        mlog.info("----------------------------{}告警的自定义标签删除完成----------------------------\n\n\n\n".format(table_name))

    def clean_alarms(self, alarm_ids, alarm_type):
        """
        删除告警数据
        """
        if alarm_type == self.tc_type:
            table_name = self.tc_alarm_table_name
        else:
            table_name = self.netflow_alarm_table_name
        sql = """DELETE FROM internal_app_bsa_gjk.{table_name} WHERE uuid in %s""".format(table_name=table_name)
        mlog.info("删除告警数据SQL: {}".format(sql))
        mlog.info("告警ID数量：{}".format(len(alarm_ids)))
        n = 0
        for i in range(0, len(alarm_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批告警， 数量：{} 条".format(n, len(alarm_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(alarm_ids[i: i + self.step]),)))
            mlog.info("第{}批告警删除成功！".format(n))
        mlog.info("----------------------------{}告警数据删除完成----------------------------\n\n\n\n".format(table_name))

    def clean_event_relate_tag(self, event_ids):
        """
        删除事件的自定义标签
        """
        sql = """ DELETE FROM internal_app_bsa_gjk.{table_name} WHERE event_id in %s""".format(
            table_name=self.event_relate_tag_table_name)
        mlog.info("删除事件的自定义标签SQL: {}".format(sql))
        mlog.info("事件ID数量：{} ".format(len(event_ids)))
        n = 0
        for i in range(0, len(event_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批事件的自定义标签， 数量：{} 条".format(n, len(event_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_ids[i: i + self.step]),)))
            mlog.info("第{}批事件的自定义标签删除成功！".format(n))
        mlog.info("----------------------------事件的自定义标签删除完成----------------------------\n\n\n\n")

    def clean_events(self, event_ids):
        """
        删除事件
        """
        sql = """DELETE FROM internal_app_bsa_gjk.{table_name} WHERE event_id in %s""".format(
            table_name=self.event_table_name)
        mlog.info("删除事件SQL: {}".format(sql))
        mlog.info("事件ID数量 ： {}".format(len(event_ids)))
        n = 0
        for i in range(0, len(event_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批事件， 数量：{} 条".format(n, len(event_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_ids[i: i + self.step]),)))
            mlog.info("第{}批事件删除成功！".format(n))
        mlog.info("---------------------------事件删除完成----------------------------\n\n\n\n")

    def clean_event_details(self, event_ids):
        """
        删除事件详情
        """
        sql = """DELETE FROM internal_app_bsa_gjk.{table_name} WHERE event_id in %s""".format(
            table_name=self.event_details_table_name)
        mlog.info("删除事件详情SQL: {}".format(sql))
        mlog.info("事件 ID 数量：{}".format(len(event_ids)))
        n = 0
        for i in range(0, len(event_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批事件详情， 数量：{} 条".format(n, len(event_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_ids[i: i + self.step]),)))
            mlog.info("第{}批事件的自定义标签删除成功！".format(n))
        mlog.info("----------------------------事件详情删除完成----------------------------\n\n\n\n")

    def clean_event_tags(self, event_ids):
        """
        删除事件标签
        """
        sql = """DELETE FROM internal_app_bsa_gjk.continuous_events_tag WHERE event_id in %s"""
        mlog.info("删除事件标签SQL: {}".format(sql))
        mlog.info("事件ID数量：{}".format(len(event_ids)))
        n = 0
        for i in range(0, len(event_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批事件标签， 数量：{} 条".format(n, len(event_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_ids[i: i + self.step]),)))
            mlog.info("第{}批事件标签删除成功！".format(n))
        mlog.info("---------------------------事件标签删除完成----------------------------\n\n\n\n")

    def clean_event_ports(self, event_ids):
        """
        删除事件标签
        """
        sql = """DELETE FROM internal_app_bsa_gjk.continuous_events_port WHERE event_id in %s"""
        mlog.info("删除事件端口SQL: {}".format(sql))
        mlog.info("事件ID数量：{}".format(len(event_ids)))
        n = 0
        for i in range(0, len(event_ids), self.step):
            n += 1
            mlog.info("正在删除第：{} 批事件标签， 数量：{} 条".format(n, len(event_ids[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_ids[i: i + self.step]),)))
            mlog.info("第{}批事件端口删除成功！".format(n))
        mlog.info("---------------------------事件端口删除完成----------------------------\n\n\n\n")

    def delete_event_details(self):
        """删除未与事件关联的事件详情脏数据"""
        event_id_sql = """select cep.event_id from internal_app_bsa_gjk.event_details cep left join internal_app_bsa_gjk.continuous_events ce on cep.event_id = ce.event_id WHERE ce.event_id is NULL;"""
        res = json.loads(CFunction.execute(CPgSqlParam(event_id_sql)))
        event_id_list = [i[0] for i in res]
        mlog.info("未与事件关联的事件详情数量：{}".format(len(event_id_list)))
        sql = """DELETE FROM internal_app_bsa_gjk.event_details WHERE event_id in %s"""
        n = 0
        for i in range(0, len(event_id_list), self.step):
            n += 1
            mlog.info("正在删除第：{} 批未与事件关联的事件详情， 数量：{} 条".format(n, len(event_id_list[i: i + self.step])))
            CFunction.execute(CPgSqlParam(sql, params=(tuple(event_id_list[i: i + self.step]),)))
            mlog.info("第{}批未与事件关联的事件详情删除成功！".format(n))

        mlog.info("---------------------------未与事件关联的事件详情 删除完成----------------------------\n\n\n\n")

    @staticmethod
    def batch_query_event_ids(event_ids, tag_content, batch_size=5000):
        result_set = set()
        for i in range(0, len(event_ids), batch_size):
            sub_ids = event_ids[i:i + batch_size]
            sub_result = ContinuousEventsTagModel.objects.filter(
                event_id__in=sub_ids,
                tag_name='port_distribution',
                tag_content=tag_content
            ).values_list('event_id', flat=True)
            result_set.update(sub_result)
        return result_set

    def clean(self, event_id_list, tc_log_alarm_id_list, netflow_alarm_id_list):
        # 通联日志
        if tc_log_alarm_id_list:
            self.clean_alert_record(tc_log_alarm_id_list, self.tc_type)
            self.clean_alert_relate_tag(alarm_ids=tc_log_alarm_id_list, alarm_type=self.tc_type)
            self.clean_alarms(alarm_ids=tc_log_alarm_id_list, alarm_type=self.tc_type)

        # NetFlow
        if netflow_alarm_id_list:
            self.clean_alert_record(netflow_alarm_id_list, self.netflow_type)
            self.clean_alert_relate_tag(alarm_ids=netflow_alarm_id_list, alarm_type=self.netflow_type)
            self.clean_alarms(alarm_ids=netflow_alarm_id_list, alarm_type=self.netflow_type)

        # 持续性事件
        if event_id_list:
            self.clean_event_tags(event_ids=event_id_list)
            self.clean_event_ports(event_ids=event_id_list)
            self.clean_event_relate_tag(event_ids=event_id_list)
            self.clean_event_details(event_ids=event_id_list)
            self.clean_events(event_ids=event_id_list)

    def run(self):
        try:
            event_id = []
            tc_log_alarm_id_list = []
            netflow_alarm_id_list = []
            # 4、指定查询通联还剩NetFlow
            if self.report_type:
                if self.report_type == self.tc_type:
                    event_id_list, tc_log_alarm_id_list = self.get_events(report_type=self.tc_type)
                    event_id.extend(event_id_list)
                    mlog.info("\n\n\n\n")
                if self.report_type == self.netflow_type:
                    event_id_list, netflow_alarm_id_list = self.get_events(report_type=self.netflow_type)
                    event_id.extend(event_id_list)
                    event_id = list(set(event_id))
            else:
                # 一个月前误报都删除，未研判的并且流出流量小于100MB的
                # 增加两个参数控制， 只有正常运行的时候才会清理这些数据
                # 一个月前误报都删除：month_ago      未研判的并且流出流量小于100MB的：not_judge
                month_ago = 30  # 30天
                not_judge = 100 * 1024 * 1024  # 100MB
                event_id_list, tc_log_alarm_id_list = self.get_events(report_type=self.tc_type, month_ago=month_ago,
                                                                      not_judge=not_judge)
                event_id.extend(event_id_list)
                mlog.info("\n\n\n\n")
                event_id_list, netflow_alarm_id_list = self.get_events(report_type=self.netflow_type,
                                                                       month_ago=month_ago, not_judge=not_judge)
                event_id.extend(event_id_list)
                event_id = list(set(event_id))
            mlog.info("事件ID数量: {}".format(len(event_id)))
            mlog.info("通联日志告警ID数量: {}".format(len(tc_log_alarm_id_list)))
            mlog.info("NetFlow告警ID数量: {}".format(len(netflow_alarm_id_list)))
            # 指定是否执行删除操作，默认执行删除
            if self.is_delete:
                self.clean(event_id, tc_log_alarm_id_list, netflow_alarm_id_list)
                self.delete_event_details()
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())


if __name__ == '__main__':
    """
    添加定时任务
    vi /home/<USER>/ISOP/bin/cron/crontabConf
    插入内容, 每天凌晨1点跑一次
    * 0 1 * * * python /home/<USER>/ISOP/apps/cncert_kj/script/clean_alarm_and_event.py
    """
    mlog.info("python clean_alarm_and_event.py -h 查看传参使用方式及参数说明")
    parser = argparse.ArgumentParser(description="告警事件删除任务")
    parser.add_argument("--event_type", default=None, help="事件类型，逗号分隔")
    parser.add_argument("--report_type", default=None, help="流量类型，1:通联，2:NetFlow", type=int)
    parser.add_argument("--analysis_tech", default=None, help="动静态基线，1:静态，2:动态", type=int)
    parser.add_argument("--retention_days", default=None, help="清理数据保留最近天数", type=int)
    parser.add_argument("--port_distribution", default=None,
                        help="端口分布：    1: '源端口集中 目的端口集中', 2: '源端口集中 目的端口分散', 3: '源端口分散 目的端口集中', 4: '源端口分散 目的端口分散'",
                        type=int)
    parser.add_argument("--is_delete", default=1, help="是否执行删除操作，1:执行，0:不执行; 默认为执行", type=int)
    args = parser.parse_args()
    mlog.info("接收参数：{}".format(args))
    event_type_ = args.event_type
    report_type_ = args.report_type
    analysis_tech_ = args.analysis_tech
    retention_days_ = args.retention_days
    port_distribution_ = args.port_distribution
    is_delete_ = args.is_delete
    CleanAlarmAndEvent(event_type_, report_type_, analysis_tech_, is_delete_, retention_days_, port_distribution_).run()
