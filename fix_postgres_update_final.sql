DO
$$
    DECLARE
        batch_size INT := 10000;
        row_count  INT;
    BEGIN
        LOOP
            -- 使用 CTE 获取一批需要更新的行
            WITH cte AS (
                SELECT ctid
                FROM internal_app_bsa_gjk.netflow_alert_5min
                WHERE id IS NULL
                LIMIT batch_size
            )
            UPDATE internal_app_bsa_gjk.netflow_alert_5min
            SET id = nextval('netflow_alert_5min_id_seq')
            FROM cte
            WHERE internal_app_bsa_gjk.netflow_alert_5min.ctid = cte.ctid;

            GET DIAGNOSTICS row_count = ROW_COUNT;
            EXIT WHEN row_count = 0;
            RAISE NOTICE '已更新 % 行', row_count;
            -- 移除了 COMMIT（由外部事务自动提交）
        END LOOP;
    END
$$;