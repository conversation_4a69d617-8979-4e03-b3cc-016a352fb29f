# -*- coding: UTF-8 -*-
import sys
import traceback

from django.conf.urls import url
from rest_framework import routers

from cncert_kj.conf.constant import PATH_LIB
from cncert_kj.utils import logger

sys.path.append(PATH_LIB)
mlog = logger.init_logger('ALL')

try:
    from cncert_kj.views import tllog_flow, netflow_flow, tllog_alert, black_white, cont_event, custom_tag, \
        netflow_alert, tllog_key_unit_overview, tllog_key_unit, tllog_key_unit_detail, netflow_key_unit_overview, \
        netflow_key_unit, netflow_key_unit_detail, retrieval_task_view, top_1w_view, config_view, static_baseline, \
        netflow_flow_filter_view, trend_chart_view, operation_intel_view, ip_one_to_more_tag_view, reported_events_view, \
        rule_detail_view, login_view, asset_manage_view, event_alert_daily_stats_view, whitelist_event_view, \
        greylist_event_view, exception_event_view

    urlpatterns = [
        url('v2/tllog_flow/overview', tllog_flow.overview),
        url('v2/netflow_flow/overview', netflow_flow.overview),

        url('v2/tllog_alert/overview/_count', tllog_alert.overview_count),
        url('v2/tllog_alert/overview/sip/topn', tllog_alert.topn_sip),
        url('v2/tllog_alert/overview/dip/topn', tllog_alert.topn_dip),
        url('v2/tllog_alert/_filter', tllog_alert.fitler),
        url('v2/tllog_alert/([\s\S]+)/tllog_detail', tllog_alert.tllog_detail),
        url('v2/tllog_alert/([\s\S]+)/download_tllog_detail', tllog_alert.download_tllog_detail),
        url('v2/tllog_alert/([\s\S]+)/port_traffic_distr', tllog_alert.port_traffic_distr),
        url('v2/tllog_alert/csv', tllog_alert.download_cvs_files),
        url('v2/tllog_alert/([\s\S]+)/record', tllog_alert.record),
        url('v2/tllog_alert/([\s\S]+)/appendixs$', tllog_alert.appendixs),
        url('v2/tllog_alert/([\s\S]+)/appendixs/([\s\S]+)', tllog_alert.single_appendixs),
        url('v2/tllog_alert/([\s\S]+)/custom_tags$', tllog_alert.custom_tags),
        url('v2/tllog_alert/traffic_portrait', tllog_alert.traffic_portrait),

        url('v2/netflow_alert/overview/_count', netflow_alert.overview_count),
        url('v2/netflow_alert/overview/sip/topn', netflow_alert.topn_sip),
        url('v2/netflow_alert/overview/dip/topn', netflow_alert.topn_dip),
        url('v2/netflow_alert/_filter', netflow_alert.fitler),
        url('v2/netflow_alert/([\s\S]+)/netflow_detail', netflow_alert.netflow_detail),
        url('v2/netflow_alert/([\s\S]+)/download_netflow_detail', netflow_alert.download_netflow_detail),
        url('v2/netflow_alert/([\s\S]+)/port_traffic_distr', netflow_alert.port_traffic_distr),
        url('v2/netflow_alert/csv', netflow_alert.download_cvs_files),
        url('v2/netflow_alert/([\s\S]+)/record', netflow_alert.record),
        url('v2/netflow_alert/([\s\S]+)/appendixs$', netflow_alert.appendixs),
        url('v2/netflow_alert/([\s\S]+)/appendixs/([\s\S]+)', netflow_alert.single_appendixs),
        url('v2/netflow_alert/([\s\S]+)/custom_tags$', netflow_alert.custom_tags),
        url('v2/netflow_alert/traffic_portrait', netflow_alert.traffic_portrait),

        url('v2/continuous_events/_add', cont_event.add_event),
        url('v2/continuous_events/csv/_add', cont_event.add_csv),
        url('v2/continuous_events/_filter', cont_event.filter),
        url('v2/continuous_events/detail', cont_event.get_event_info_view),
        url('v2/continuous_events/port_traffic', cont_event.event_port_traffic),
        url('v2/continuous_events/csv', cont_event.download_csv_files),
        url('v2/continuous_events/judge', cont_event.judge),
        url('v2/continuous_events/([0-9]+)/appendixs$', cont_event.appendixs),
        url('v2/continuous_events/([0-9]+)/appendixs/([\s\S]+)', cont_event.single_appendixs),
        url('v2/continuous_events/([0-9]+)/custom_tags$', cont_event.custom_tags),
        url('v2/continuous_events/get_event_group_by', cont_event.get_event_group_by),
        url('v2/continuous_events/get_event_type_tendency', cont_event.get_event_type_tendency),
        url('v2/continuous_events/get_event_summary_overview', cont_event.get_event_summary_overview),
        url('v2/continuous_events/([0-9]+)', cont_event.download_docx_report),
        url('v2/continuous_events/template', cont_event.get_docx_template_nams),  # 获取所有模板名
        url('v2/continuous_events/tc_time_distribution', cont_event.get_tc_time_distribution),  # 获取通联时间分布信息

        url('v2/whitelists', black_white.get_whitelists),
        url('v2/whitelist$', black_white.get_whitelist),
        # 黑名单增删改查
        url('v2/blacklist/add', black_white.add_blacklist),
        url('v2/blacklist/del', black_white.delete_black_list),
        url('v2/blacklist/put', black_white.update_blacklist),
        url('v2/blacklist/detail', black_white.get_black_ip_detail),
        url('v2/blacklist_batch_edit', black_white.blacklist_batch_edit),
        url('v2/blacklist$', black_white.get_blacklists),
        url('v2/synchronize_blacklist_to_intel', black_white.synchronize_blacklist_to_operation_intel),

        # 黑白名单ip分类增删改查
        url('v2/ip_category/add', black_white.add_ip_category),
        url('v2/ip_category/del', black_white.del_ip_category),
        url('v2/ip_category/put', black_white.update_ip_category),
        url('v2/ip_category/execute_switch', black_white.execute_switch),
        url('v2/ip_category', black_white.get_ip_category),

        url('v2/custom_tag$', custom_tag.add_custom_tag),
        url('v2/custom_tag/([0-9]+)', custom_tag.update_custom_tag),
        url('v2/custom_tags$', custom_tag.custom_tags),

        url('v2/tllog_key_unit/overview/update_address_type', tllog_key_unit_overview.update_address_type),
        url('v2/tllog_key_unit/overview/execute_refresh_cache', tllog_key_unit_overview.execute_refresh_cache),
        url('v2/tllog_key_unit/overview/max_key_unit_outflow', tllog_key_unit_overview.get_max_key_unit_outflow),
        url('v2/tllog_key_unit/overview/max_ip_outflow', tllog_key_unit_overview.get_max_ip_outflow),
        url('v2/tllog_key_unit/overview/key_unit_detail', tllog_key_unit_overview.get_key_unit_detail),
        url('v2/tllog_key_unit/overview/department_detail', tllog_key_unit_overview.get_department_detail),
        url('v2/tllog_key_unit/overview/ip_detail', tllog_key_unit_overview.get_ip_detail),
        url('v2/tllog_key_unit/overview/key_unit_flow_trend', tllog_key_unit_overview.get_key_unit_flow_trend),
        url('v2/tllog_key_unit/overview/key_department_trend', tllog_key_unit_overview.get_department_flow_trend),
        url('v2/tllog_key_unit/overview/ip_flow_trend', tllog_key_unit_overview.get_ip_flow_trend),
        url('v2/tllog_key_unit/overview/total_flow', tllog_key_unit_overview.total_flow),
        url('v2/tllog_key_unit/overview/flow_trend_total', tllog_key_unit_overview.total_flow_trend),

        url('v2/tllog_key_unit/_add', tllog_key_unit.add_key_unit),
        url('v2/units_manager/_add', tllog_key_unit.add_units_manager),
        url('v2/tllog_industry/_add', tllog_key_unit.add_industry),
        url('v2/tllog_key_unit/([0-9]+)/_update', tllog_key_unit.update_key_unit),
        url('v2/units_manager/([0-9]+)/_update', tllog_key_unit.update_units_manager),
        url('v2/units_manager/([0-9]+)/_details', tllog_key_unit.details_units_manager),
        url('v2/units_manager/distribute_config', tllog_key_unit.distribute_config),
        url('v2/units_configuration/([0-9]+)/_update', tllog_key_unit.update_field),
        url('v2/units_configuration/batch_update', tllog_key_unit.batch_update_field),
        url('v2/tllog_industry/([0-9]+)/_update', tllog_key_unit.update_industry),
        url('v2/tllog_key_unit/([0-9]+)/_delete', tllog_key_unit.delete_key_unit),
        url('v2/units_manager/([0-9]+)/_delete', tllog_key_unit.delete_units_manager),
        url('v2/tllog_industry/([0-9]+)/_delete', tllog_key_unit.delete_industry),
        url('v2/tllog_key_unit/key_units', tllog_key_unit.get_key_units),
        url('v2/tllog_key_unit/departments', tllog_key_unit.get_departments),
        url('v2/tllog_key_unit/total_unit_ip_num', tllog_key_unit.total_unit_ip_num),
        url('v2/tllog_key_unit/department_statistics', tllog_key_unit.department_statistics),
        url('v2/tllog_key_unit/department_key_unit', tllog_key_unit.get_department_key_unit),
        # url('v2/tllog_key_unit/xls/batch_data_check', tllog_key_unit.batch_data_check),
        url('v2/tllog_key_unit/xls/_add', tllog_key_unit.add_xls),
        url('v2/tllog_key_unit/xls/_export', tllog_key_unit.export_xls),
        url('v2/tllog_key_unit/xls', tllog_key_unit.download_xls_files),
        url('v2/tllog_key_unit/industry', tllog_key_unit.get_industry),
        url('v2/tllog_key_unit/select_key_unit', tllog_key_unit.select_key_unit),
        url('v2/tllog_key_unit/list_industry', tllog_key_unit.get_industry_list),
        # >> 重点监控详情的接口
        # url('v2/tllog_key_unit/([0-9]+)/detail', tllog_key_unit.get_detail),
        url('v2/tllog_key_unit/details/([0-9]+)/detail', tllog_key_unit.get_detail),
        url('v2/tllog_key_unit/([\s\S]+)/log_detail', tllog_key_unit.tllog_detail),
        url('v2/tllog_key_unit/now_flow_statistics', tllog_key_unit.now_flow_statistics),
        url('v2/tllog_key_unit/n_flow_statistics', tllog_key_unit.n_flow_statistics),

        url('v2/tllog_key_unit/detail/ip_flow_statistics', tllog_key_unit_detail.ip_flow_statistics),
        url('v2/tllog_key_unit/detail/ip_pair_flow_monitor', tllog_key_unit_detail.ip_pair_flow_monitor),
        url('v2/tllog_key_unit/detail/([0-9]+)/flow_trend', tllog_key_unit_detail.get_key_unit_flow_trend),
        url('v2/tllog_key_unit/detail/([0-9]+)/([\s\S]+)/ip_flow_trend', tllog_key_unit_detail.get_ip_flow_trend),
        # ip对通信详情导出
        url('v2/tllog_key_unit/detail_export', tllog_key_unit.detail_export),
        # 通联日志流量查询 全部导出
        url('v2/tllog_key_unit/export_all', tllog_key_unit.detail_export_all),

        url('v2/netflow_key_unit/overview/max_key_unit_outflow', netflow_key_unit_overview.get_max_key_unit_outflow),
        url('v2/netflow_key_unit/overview/max_ip_outflow', netflow_key_unit_overview.get_max_ip_outflow),
        url('v2/netflow_key_unit/overview/key_unit_detail', netflow_key_unit_overview.get_key_unit_detail),
        url('v2/netflow_key_unit/overview/department_detail', netflow_key_unit_overview.get_department_detail),
        url('v2/netflow_key_unit/overview/ip_detail', netflow_key_unit_overview.get_ip_detail),
        url('v2/netflow_key_unit/overview/key_unit_flow_trend', netflow_key_unit_overview.get_key_unit_flow_trend),
        url('v2/netflow_key_unit/overview/key_department_trend', netflow_key_unit_overview.get_department_flow_trend),
        url('v2/netflow_key_unit/overview/ip_flow_trend', netflow_key_unit_overview.get_ip_flow_trend),
        url('v2/netflow_key_unit/overview/total_flow', netflow_key_unit_overview.total_flow),
        url('v2/netflow_key_unit/overview/flow_trend_total', netflow_key_unit_overview.total_flow_trend),

        url('v2/netflow_key_unit/_add', netflow_key_unit.add_key_unit),
        url('v2/netflow_key_unit/([0-9]+)/_update', netflow_key_unit.update_key_unit),
        url('v2/netflow_key_unit/([0-9]+)/_delete', netflow_key_unit.delete_key_unit),
        url('v2/netflow_key_unit/key_units', netflow_key_unit.get_key_units),
        url('v2/netflow_key_unit/csv', netflow_key_unit.download_csv_files),

        url('v2/netflow_key_unit/([0-9]+)/detail', netflow_key_unit.get_detail),
        url('v2/netflow_key_unit/([\s\S]+)/log_detail', netflow_key_unit.netflow_detail),
        url('v2/netflow_key_unit/now_flow_statistics', netflow_key_unit.now_flow_statistics),
        url('v2/netflow_key_unit/n_flow_statistics', netflow_key_unit.n_flow_statistics),

        url('v2/netflow_key_unit/detail/ip_flow_statistics', netflow_key_unit_detail.ip_flow_statistics),
        url('v2/netflow_key_unit/detail/ip_pair_flow_monitor', netflow_key_unit_detail.ip_pair_flow_monitor),
        url('v2/netflow_key_unit/detail/([0-9]+)/flow_trend', netflow_key_unit_detail.get_key_unit_flow_trend),
        url('v2/netflow_key_unit/detail/([0-9]+)/([\s\S]+)/ip_flow_trend', netflow_key_unit_detail.get_ip_flow_trend),
        # ip对通信详情导出
        url('v2/netflow_key_unit/detail_export', netflow_key_unit.detail_export),
        # netflow 全部导出
        url('v2/netflow_key_unit/export_all', netflow_key_unit.detail_export_all),

        url('v2/retrieval_task/create', retrieval_task_view.create_retrieval_task),
        url('v2/retrieval_task/delete', retrieval_task_view.delete_retrieval_task),
        url('v2/retrieval_task/clear', retrieval_task_view.clear_retrieval_task),
        url('v2/retrieval_task/download_file', retrieval_task_view.download_file),
        url('v2/retrieval_task/get_retrieval_task', retrieval_task_view.get_retrieval_task),
        url('v2/retrieval_task/retry_task', retrieval_task_view.retry_task_view),
        url('v2/retrieval_task/get_lcjs_task_list', retrieval_task_view.get_lcjs_task_list),
        url('v2/retrieval_task/get_search_task_list', retrieval_task_view.get_search_task_list),

        # 大流量事件
        url('v2/top_1w/big_flow_event', top_1w_view.big_flow_event),
        url('v2/top_1w/traffic_portrait', top_1w_view.traffic_portrait),
        url('v2/top_1w/tclog_traffic_portrait', top_1w_view.tclog_traffic_portrait),
        url('v2/top_1w/netflow_traffic_portrait', top_1w_view.netflow_traffic_portrait),
        # 告警规则配置
        url('v2/alert_rule/_add', top_1w_view.add_alert_rule),
        url('v2/alert_rule/_delete', top_1w_view.del_alert_rule),
        url('v2/alert_rule/_update', top_1w_view.edit_alert_rule),
        url('v2/alert_rule/_filter', top_1w_view.alert_rule_filter),
        url('v2/alert_rule/_retrieve', top_1w_view.get_alert_rule),

        url('v2/top_1w/judge', top_1w_view.judge),
        url('v2/top_1w/([0-9]+)/appendixs$', top_1w_view.appendixs),
        url('v2/top_1w/([0-9]+)/appendixs/([\s\S]+)', top_1w_view.single_appendixs),
        # 历史事件标定
        url('v2/top_1w/history_judge', top_1w_view.history_judge),
        # 启动基线统计
        url('v2/top_1w/start_baseline_task', top_1w_view.start_baseline_task),
        # 计算流量均值
        url('v2/top_1w/agg_avg_flow', top_1w_view.agg_avg_flow),
        # 导出
        url('v2/top_1w/big_flow_download', top_1w_view.big_flow_download),
        # 应用配置
        url('v2/admin/config', config_view.config),
        url("v2/alert/ports", tllog_alert.get_alert_ports),  # 告警端口
        url("v2/alert/push_issue", tllog_alert.alert_push_issue),  # 告警推送处置
        url("v2/type/get_type", tllog_alert.get_types),
        url("v2/event/ports", tllog_alert.get_event_ports),  # 事件端口
        url("v2/event/push_issue", tllog_alert.event_push_issue),  # 事件推送处置

        # 字段配置
        url("v2/field_conf", cont_event.field_conf),
        # 重要服务枚举值接口
        url("v2/get_key_service_types_map", cont_event.get_key_service_types_map),
        # 重要数据库服务枚举值接口
        url("v2/get_key_bd_service_types_map", cont_event.get_key_bd_service_types_map),
        # 事件处置状态枚举接口
        url("v2/get_judge_status_map", cont_event.get_judge_status_map),
        # 行业类型枚举接口
        url("v2/get_department_type", cont_event.get_department_type),
        # 事件标签名称枚举接口
        url("v2/get_tag_name_map", cont_event.get_tag_name_map),
        # 静态基线阈值配置
        url('v2/static_baseline_config/static_baselines', static_baseline.get_static_baselines),
        url('v2/static_baseline_config/_add', static_baseline.add_static_baseline),
        url('v2/static_baseline_config/_delete', static_baseline.delete_static_baseline),
        url('v2/static_baseline_config/([0-9]+)/_update', static_baseline.update_static_baseline),
        url('v2/static_baseline_config/([0-9]+)/_details', static_baseline.details_static_baseline),
        url('v2/static_baseline_config/distribute_config', static_baseline.distribute_config),
        url('v2/static_baseline_config/category_select', static_baseline.category_select),
        url('v2/static_baseline_config/execute_switch', static_baseline.execute_switch),
        # netflow全流量查询--ip对流量统计
        url('v2/netflow_flow_filter_view/ip_pair_flow_statistics', netflow_flow_filter_view.ip_pair_flow_statistics),
        # 流量查询--ip对流量统计导出
        url('v2/ip_pair_flow_statistics_export', netflow_flow_filter_view.ip_pair_flow_statistics_export),
        # 流量查询--流量趋势图
        url('v2/traffic_trend_chart', tllog_key_unit.traffic_trend_chart),

        # 趋势图
        url('v2/trend_chart_view/trend_chart', trend_chart_view.trend_chart),
        # 持续性事件-批量研判
        url('v2/continuous_events/event_batch_judge', cont_event.event_batch_judge),

        # 根据ES索引查询告警原始日志
        url('v2/flow_logs/detail', tllog_alert.flow_logs_detail),
        # 下载最新的事件推荐文件
        url('v2/event_recommend/download_file', reported_events_view.download_event_recommend_file),

    ]

    # 1、DRF路由注册
    # 传入 trailing_slash=False 参数，则所有生成的接口无 / 后缀。
    router = routers.DefaultRouter(trailing_slash=False)
    # 运营情报配置
    router.register(r'/v2/operation_intel', operation_intel_view.OperationIntelView, base_name="operation_intel")
    # ip一对多标签下钻
    router.register(r'/v2/ip_one_to_more_tag', ip_one_to_more_tag_view.IpOneToMoreTagView,
                    base_name="ip_one_to_more_tag")
    # 已通报事件管理
    router.register(r'/v2/reported_events', reported_events_view.ReportedEventsView, base_name="reported_events")
    # 资产管理
    router.register(r'/v2/asset_manage', asset_manage_view.AssetManageViewSet, base_name="asset_manage")

    # IP过滤规则配置
    router.register(r'/v2/rule_detail', rule_detail_view.RuleDetailView, base_name="rule_detail")
    router.register(r'/v2/dynamic_baseline', static_baseline.DynamicDetectView, base_name="dynamic_baseline")
    # 单位设置 -  新增\修改\删除接口
    router.register(r'/v2/units_manager', tllog_key_unit.UnitsManagerViewSet, base_name="units_manager")
    # 单位设置 -  校验文件/导入接口
    router.register(r'/v2/key_unit', tllog_key_unit.TlLogKeyUnitViewSet, base_name="key_unit")
    # 单点登录 跳转KJ\ZX\1XX
    router.register(r'/v2/sso_login', login_view.SSOLoginView, base_name='sso_login')
    # 事件告警统计
    router.register(r'/v2/event_alert_daily_stats', event_alert_daily_stats_view.EventAlertDailyStatsView,
                    base_name="event_alert_daily_stats")

    # 白名单事件管理
    router.register(r'/v2/whitelist_events', whitelist_event_view.WhitelistEventsView, base_name="whitelist_events")
    # 灰名单事件管理
    router.register(r'/v2/greylist_events', greylist_event_view.GreylistEventsView, base_name="greylist_events")

    # 异常事件类型管理
    router.register(r'/v2/exception_event_conf', exception_event_view.ExceptionEventConf,
                    base_name="exception_event_conf")
    # 异常事件
    router.register(r'/v2/exception_event', exception_event_view.ExceptionEventView, base_name="exception_event_view")

    urlpatterns += router.urls

except Exception as e:
    mlog.error(e)
    mlog.error(traceback.format_exc())
    raise e
