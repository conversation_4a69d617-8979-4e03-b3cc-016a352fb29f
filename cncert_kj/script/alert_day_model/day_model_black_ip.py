#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@ Author    : <PERSON><PERSON><PERSON><PERSON><PERSON>
@ File      : day_model_black_ip.py
@ Time      : 2024/12/6 16:24
@ Desc      :恶意IP天模型
"""
import argparse
import datetime
import time

from cncert_kj.script.alert_day_model.operation_intel_day_model import OperationIntelDayModel
from cncert_kj.script.asset_event_add_app_type import AssetEventAppTypeEnhancer
from cncert_kj.script.event_asset_info import GetAssetInfo
from cncert_kj.script.event_malicious_tag_script import EventMaliciousTag
from cncert_kj.script.event_threat_type_script import enhance_event_intelligence_info
from cncert_kj.utils import logger

mlog = logger.init_logger('day_model_black_ip')

if __name__ == '__main__':
    """
    添加定时任务
    vi /home/<USER>/ISOP/bin/cron/crontabConf
    插入内容, 每天凌晨5点30跑一次
    * 30 5 * * * python /home/<USER>/ISOP/apps/cncert_kj/script/alert_day_model/day_model_black_ip.py
    """
    today = datetime.date.today()
    stime = int(time.mktime(today.timetuple())) - 86400
    etime = int(time.mktime(today.timetuple()))
    date = str(today - datetime.timedelta(days=1))
    parser = argparse.ArgumentParser(description="恶意IP天告警模型")
    parser.add_argument("--start_time", default=stime, help="开始时间", type=int)
    parser.add_argument("--end_time", default=etime, help="结束时间", type=int)
    args = parser.parse_args()
    mlog.info("接收参数：{}".format(args))
    stime_ = args.start_time
    etime_ = args.end_time
    begin_tiem = time.time()
    alert_name_type = {
        "2_2": (131, "境内向境外恶意IP持续上传数据告警"),  # 主动
        "2_1": (132, "境外恶意IP从境内持续下载数据告警"),  # 被动
        "1_2": (132, "境外恶意IP从境内持续下载数据告警"),  # 被动
        "1_1": (131, "境内向境外恶意IP持续上传数据告警"),  # 主动
    }
    threshold = 1024 * 1024 * 100
    intel_type = 1  # 黑IP
    detect_name = "恶意IP"
    # 通联日志
    OperationIntelDayModel(stime_, etime_, alert_name_type, mlog, threshold, intel_type, flow_type=1, boundary=2,
                           detect_name=detect_name).run()

    # NetFlow
    OperationIntelDayModel(stime_, etime_, alert_name_type, mlog, threshold, intel_type, flow_type=2, boundary=2,
                           detect_name=detect_name).run()
    mlog.info("开始进行资产事件应用类型增强")
    analysis_tech = 2
    AssetEventAppTypeEnhancer(report_type=1, start_time=stime_, end_time=etime_, analysis_tech=analysis_tech,
                              event_types=[131, 132]).run()
    AssetEventAppTypeEnhancer(report_type=2, start_time=stime_, end_time=etime_, analysis_tech=analysis_tech,
                              event_types=[131, 132]).run()

    mlog.info("开始增强资产信息")
    GetAssetInfo(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_,
                 event_type=[131, 132]).enhance_event_asset_info()
    mlog.info("开始增强恶意标签信息")
    EventMaliciousTag().run(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_, event_type=[131, 132])
    mlog.info("开始增强情报信息")
    enhance_event_intelligence_info(analysis_tech=analysis_tech, start_time=stime_, end_time=etime_,
                                    event_type=[131, 132])
    stop_time = time.time()
    mlog.info("总耗时: {} 秒".format(stop_time - begin_tiem))
