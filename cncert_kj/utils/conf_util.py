#!/usr/bin/env python
# -*-coding:utf-8 -*-
import base64
import os
import socket
import sys
import time
import re

from cncert_kj.conf.constant import CONF_PATH, ES_DEVICE_NUM
from cncert_kj.lib.json_utils import JsonTools
from cncert_kj.models import black_white_models, type_conf_model
from cncert_kj.utils import net

json_tool = JsonTools()
reload(sys)
sys.setdefaultencoding('utf-8')


class CommonConf(object):
    """
    a class for access common conf
    """

    def __init__(self):
        self.threat_type_conf_file_path = os.path.join(CONF_PATH, "threat_type_conf.json")
        self.threat_type_conf = json_tool.read_file(self.threat_type_conf_file_path)
        self.common_conf = json_tool.read_file(os.path.join(CONF_PATH, "db.json"))
        self.asset_knowledge_conf = json_tool.read_file(os.path.join(CONF_PATH, "asset_knowledge_conf.json"))

    def is_dev(self):
        """
        判断是否是开发环境
        """

        def get_current_ip():
            """获取本机ip"""
            return socket.gethostbyname(socket.gethostname())

        ip = get_current_ip()
        # 线上环境ip,都是10.58开头
        if ip.startswith("10.58."):
            return False
        return True

    def get_mysql_config(self):
        """
        获取mysql config
        """
        # 开发环境的也从配置读取
        return self.common_conf['mysql']

    def get_es_config(self):
        """
        获取es config
        """
        return self.common_conf['elasticsearch']

    def get_hadoop_config(self):
        """
        获取hdfs config
        """
        return self.common_conf['hadoop']

    def get_hdfs_path(self):
        """
        获取hdfs path
        """
        return self.common_conf['HDFS_CMD']

    def sso_login_url(self, system="KJ"):
        """
        获取单点登录的URL
        """
        if system == "KJ":
            return self.common_conf['KJ_SSO_URL']
        elif system == "ZX":
            return self.common_conf['ZX_SSO_URL']
        elif system == "1XX":
            return self.common_conf['1XX_SSO_URL']
        else:
            return self.common_conf['KJ_SSO_URL']

    def get_tllog_alert_type(self):
        """
        获取通联日志告警类型
        "{大流量异常主动外传}告警": "11"
        """
        type_conf_list = type_conf_model.type_conf_get_alert_category(is_tclog=True)
        data = {}
        for item in type_conf_list:
            data[u"{}告警".format(item[1])] = item[2]
        return data

    def get_netflow_alert_type(self):
        """
        获取NetFlow告警类型
        "{大流量异常主动外传}告警": "11"
        """
        type_conf_list = type_conf_model.type_conf_get_alert_category(is_tclog=False)
        data = {}
        for item in type_conf_list:
            data[u"{}告警".format(item[1])] = item[2]
        return data

    def get_bflow_merge_type(self):
        """
        获取netflow的双向流合并事件类型列表
        """
        type_conf_list = type_conf_model.type_conf_get_alert_category(is_tclog=False)
        data = []
        for item in type_conf_list:
            if item[4]:
                data.append(int(item[2]))
        return data

    def get_not_is_dynamic_type(self):
        """
        获取不是动态基线的告警类型
        """
        type_conf_list = type_conf_model.get_type_conf_all()
        data = []
        for item in type_conf_list:
            if not item[6]:
                data.append(int(item[2]))
        return data

    def get_continuous_event_type(self, analysis_tech=None):
        """
        获取持续性事件类型
        "{大流量异常主动外传}事件": "11"
        """
        type_conf_list = type_conf_model.get_type_conf(analysis_tech)
        data = {}
        for item in type_conf_list:
            data[u"{}事件".format(item[1])] = item[2]
        return data

    def get_continuous_event_type_reverse(self):
        """
        获取持续性事件 键/值 反转类型
        "11": "{大流量异常主动外传}事件"
        """
        type_conf_list = type_conf_model.get_type_conf_all()
        data = {}
        for item in type_conf_list:
            data[item[2]] = u"{}事件".format(item[1])
        return data

    def get_alert_type_reverse(self):
        type_conf_list = type_conf_model.get_type_conf()
        data = {}
        for item in type_conf_list:
            data[item[2]] = u"{}告警".format(item[1])
        return data

    def create_alert_type(self, type_value, type_name, is_tclog, category, status, is_bflow_merge=False,
                          is_dynamic=False, ):
        """
        type_value: 告警的枚举值， 唯一
        type_name: 名称
        is_tclog: 是否是通联  bool
        is_netflow: 是否为netflow bool
        is_dynamic:  通过脚本生成的无原始日志的告警true
        is_bflow_merge: 是否双向流合并 bool
        category: 告警种类
        """
        type_value = str(type_value)
        if not all([type_value, type_name, category]):
            raise ValueError(
                "枚举值:{}, 类型名称:{}, 所属类型:{}, 是否通联:{}".format(type_value, type_name, category, is_tclog))
        type_conf_model.type_conf_add_alert_type(
            category.rstrip("告警").rstrip("事件"),
            type_value,
            type_name.rstrip("告警").rstrip("事件"),
            is_tclog, status, is_bflow_merge, is_dynamic)

    def get_alert_category(self, alert_type="is_tclog", is_event=False):
        """
        获取告警种类
        """
        is_tclog = True if alert_type == "is_tclog" else False
        data = {}
        if is_event:
            alert_category_list = type_conf_model.get_type_conf()
        else:
            alert_category_list = type_conf_model.type_conf_get_alert_category(is_tclog)
        for item in alert_category_list:
            if is_event:
                category = item[7]
                type_value = str(int(item[2]))
                data[type_value] = category + "事件"
            else:
                category = item[7]
                type_value = str(int(item[2]))
                data[type_value] = category + "告警"
        return data

    def get_alert_category_all(self, alert_type="is_tclog"):
        """
        获取告警种类
        """
        is_tclog = True if alert_type == "is_tclog" else False
        data = {}
        alert_category_list = type_conf_model.get_alert_category_all(is_tclog)
        for item in alert_category_list:
            category = item[7]
            type_value = str(int(item[2]))
            data[type_value] = category + "告警"
        return data

    def get_cate_type(self, alert_type="is_tclog", is_event=False):
        """
        获取告警种类
        """
        data = {}
        type_conf_list = type_conf_model.get_type_conf()
        for item in type_conf_list:
            category = item[7]
            if is_event:
                data.setdefault(category, []).append(item[2])
            else:
                if (alert_type == "is_tclog" and item[3]) or (alert_type == "is_netflow" and item[4]):
                    data.setdefault(category, []).append(item[2])
        # 去重处理
        return {k: list(set(v)) for k, v in data.items()}

    def get_ftp_config(self):
        """
        获取ftp config
        """
        return self.common_conf['ftp']

    def get_path(self):
        """
        获取上传FTP相关路径
        """
        return self.common_conf['big_screen_path']

    def get_threat_type(self):
        """
        获取威胁标签枚举字典
        """
        threat_type = self.threat_type_conf["threat_type"]
        return threat_type

    def postgresql_conf(self):
        pg = self.common_conf['postgresql']
        return pg

    def ssh_put_event_conf(self):
        ssh_conf = self.common_conf['ssh_for_put_event']
        return ssh_conf

    def retrieval_task_wait_second(self):
        wait_second = self.common_conf['csemp_retrieval_task_wait_second']
        return wait_second

    def get_visit_record_unit(self):
        visit_record_unit = self.common_conf["visit_record_unit"]
        return visit_record_unit


es_info = CommonConf().get_es_config()
ES_INDEX_PREFIX = es_info.get("es_index_prefix")

if ES_DEVICE_NUM == 1:
    NETFLOW_IP_5MIN = "netflow_ip_5min"
    NETFLOW_IP_DAY = "netflow_ip_day"
    NETFLOW_IP_PAIR_5MIN = "netflow_ip_pair_5min"
    NETFLOW_LOG_INDEX = "netflow_log"

    TLLOG_IP_5MIN = "tclog_ip_5min"
    TLLOG_IP_DAY = "tclog_ip_day"
    TLLOG_IP_PAIR_5MIN = "tclog_ip_pair_5min"
    TLLOG_LOG_INDEX = "connection_log"
else:
    NETFLOW_IP_5MIN = "{}netflow_ip_5min".format(ES_INDEX_PREFIX)
    NETFLOW_IP_DAY = "{}netflow_ip_day".format(ES_INDEX_PREFIX)
    NETFLOW_IP_PAIR_5MIN = "{}netflow_ip_pair_5min".format(ES_INDEX_PREFIX)
    NETFLOW_LOG_INDEX = "{}netflow_log".format(ES_INDEX_PREFIX)

    TLLOG_IP_5MIN = "{}tclog_ip_5min".format(ES_INDEX_PREFIX)
    TLLOG_IP_DAY = "{}tclog_ip_day".format(ES_INDEX_PREFIX)
    TLLOG_IP_PAIR_5MIN = "{}tclog_ip_pair_5min".format(ES_INDEX_PREFIX)
    TLLOG_LOG_INDEX = "{}connection_log".format(ES_INDEX_PREFIX)


def get_tclog_ip_index_name(start_time):
    if time.time() - start_time < 8 * 24 * 60 * 60:
        return TLLOG_IP_5MIN
    else:
        return TLLOG_IP_DAY


def get_tclog_ip_pair_index_name():
    return TLLOG_IP_PAIR_5MIN


def get_netflow_ip_index_name(start_time):
    if time.time() - start_time < 8 * 24 * 60 * 60:
        return NETFLOW_IP_5MIN
    else:
        return NETFLOW_IP_DAY


def get_netflow_ip_pair_index_name():
    return NETFLOW_IP_PAIR_5MIN


def get_globa_white_list():
    global_white_list_res = black_white_models.get_total_whitelists()
    ips_to_list = []
    for white in global_white_list_res["white_list"]:
        ips_to_list.append(white[0])
    return net.ips_to_list(ips_to_list)


def get_globa_black_list():
    global_black_list_res = black_white_models.get_total_blacklists()
    ips_to_list = []
    for black in global_black_list_res["black_list"]:
        ips_to_list.append(black[0])
    return net.ips_to_list(ips_to_list)


def base_64_encode(data):
    # 编码
    encoded_data = base64.b64encode(data)
    # print(encoded_data)
    return encoded_data


PORT_DB_SERVER_MAP = {
    3306: "MySQL",
    5000: "MS SQL Server",
    5342: "PostgreSQL",
    5236: "国产DM达梦",
    5984: "CouchDB",
    5601: "Kibana",
    9092: "Kafka",
    6379: "Redis",
    9200: "Elasticsearch",
    9300: "Elasticsearch",
    1433: "SQL Server",
    1521: "Oracle Database",
    11211: "Memcached",
    27017: "MongoDB",
    10000: "Hive",
    16000: "HBase",
    11210: "Couchbase",
    8086: "InfluxDB",
    7687: "Neo4j",
    9042: "Cassandra",
    50000: "DB2",
    8030: "StarRocks",
    4000: "TiDB",
    9000: "ClickHouse",
}

# 持续性事件重要服务类型枚举
KEY_SERVICE_MAP = {
    "1": "POP3",
    "2": "FTP",
    "3": "IMAP",
    "4": "SMTP"
}
KEY_DB_SERVICE_MAP = {
    "1": "MySQL",
    "2": "MS SQL Server",
    "3": "PostgreSQL",
    "4": "国产DM达梦",
    "5": "CouchDB",
    "6": "Kibana",
    "7": "Kafka",
    "8": "Redis",
    "9": "Elasticsearch",
    "10": "Elasticsearch",
    "11": "SQL Server",
    "12": "Oracle Database",
    "13": "Memcached",
    "14": "MongoDB",
    "15": "Hive",
    "16": "HBase",
    "17": "Couchbase",
    "18": "InfluxDB",
    "19": "Neo4j",
    "20": "Cassandra",
    "21": "DB2",
    "22": "StarRocks",
    "23": "TiDB",
    "24": "ClickHouse",

}
# 事件处置状态
JUDGE_STATUS_MAP = {
    "1": "未研判",
    "2": "已研判，未通报",
    "3": "已通报",
    "4": "已安排分中心协查",
    "5": "处置完毕",
    "6": "误报",
    "7": "自动研判，未通报",
    "0": "其他"
}
# 事件来源
REPORT_TYPE = {"0": "手动录入", "1": "TlLog自动研判", "2": "NetFlow自动研判", "3": "特定协议日志研判"}

# 事件检测手段
ANALYSIS_TECH_MAP = {
    "1": "静态基线检测",
    "2": "动态基线检测"
}
# 事件检测手段
ANALYSIS_TECH_MAP_REVERSE = {
    "静态基线检测": "1",
    "动态基线检测": "2"
}

# 事件状态
STATUS_MAP = {
    "0": "已结束",
    "1": "持续中"
}

# 事件状态
STATUS_MAP_REVERSE = {
    "已结束": "0",
    "持续中": "1"
}

# 常用端口
COMMON_PORT = [
    443,
    80
]

# 云服务列表
CLOUD_SERVICE = [
    "Google Cloud", "DigitalOcean", "Vultr", "OVHcloud", "Linode", "IBMCloud", "IBM SoftLayer", "Amazon"
]

# 事件关联字段表-字段名称枚举
event_other_tag_name = {
    "物联网": "iot",
    "备案单位": "unit",
    "服务类型": "service",
    "应用类型": "app_type",
    "真实数据库": "real_db",
    "疑似DDOS攻击": "ddos",
    "威胁情报": "ioc",
    "恶意情报": "malicious_tag",
    "真实ftp": "real_ftp",
    "真实远程登录": "real_remote_login",
    "非常用端口": "uncommon_port_protocol",
    "端口分布": "port_distribution",
    "云平台IP": "cloud_platform_IP",
    "FTP": "ftp_special",
    "物联网资产": "iot_special",
    "防火墙": "firewall_special",
    "Web邮箱": "web_special",
    "远程登录": "remote_special",
    "视频监控": "video_special",
    "OA系统": "oa_special",
    "数据库": "database_special",
    "VPN": "vpn_special",
    "路由器": "router_special",
    "网络附属存储(NAS)": "nas_special",
    "云管理平台": "cmp_special",
}

# 端口分布映射---取反逻辑，1、4不变，2、3取反
PORT_DISTRIBUTION_MAP = {
    1: "源端口集中 目的端口集中",
    2: "源端口集中 目的端口分散",
    3: "源端口分散 目的端口集中",
    4: "源端口分散 目的端口分散",
}

ASSET_TYPE = {
    1: "数据库类应用",
    2: "FTP类应用",
    3: "远程登录类应用",
    4: "VPN类应用",
    5: "OA类应用",
    6: "Web邮件类应用",
    7: "视频类应用",
    8: "物联网类应用",
    9: "防火墙类应用"
}
APT_KIMSUKY = "APT组织kimsuky"
APT_SERVICE_MAP = {
    14146: "APT组织APT32",
    5633: "APT组织MacMa",
    1177: "APT组织njRAT",
    4782: "APT组织QuasarRAT",
    52390: APT_KIMSUKY,
    993: APT_KIMSUKY,
    14275: APT_KIMSUKY,
    14276: APT_KIMSUKY,
    2086: APT_KIMSUKY,
    33890: APT_KIMSUKY,
    50108: APT_KIMSUKY,
    52030: APT_KIMSUKY,
    57149: "APT组织sidewinder",
    57670: "APT组织sidewinder",
    46405: "APT组织oceanlotus"
}

TIME_DISTRIBUTION_MAP = {
    1: "工作时间",
    2: "非工作时间",
    3: "混合时间"

}

TASK = {
    u"GJK-NETFLOW-IP-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_netflow_ip_statistic.sh",
    u"GJK-NETFLOW-IPPAIR-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_netflow_ippair_statistic.sh",
    u"GJK-NETFLOW-SETL": "/opt/apps/gjk_data_proc/bin/start_netflow_setl.sh",
    u"GJK-TCLOG-SETL": "/opt/apps/gjk_data_proc/bin/start_tclog_setl.sh",
    u"GJK-TCLOG-IP-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_tclog_ip_statistic.sh",
    u"GJK-TCLOG-IPPAIR-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_tclog_ippair_statistic.sh",
    u"GJK-TCLOG-DETECT": "/opt/apps/gjk_data_proc/bin/start_tclog_detect.sh",
    u"GJK-NETFLOW-DETECT": "/opt/apps/gjk_data_proc/bin/start_netflow_detect.sh",
    u"GJK-NETFLOW-COUNT": "/opt/apps/gjk_data_proc/bin/start_netflow_count.sh",
}

GET_METHOD = "GET"
POST_METHOD = "POST"
DELETE_METHOD = "DELETE"
PUT_METHOD = "PUT"

# 脚本记录的时间戳： key=value
DDOS_TIMESTAMP = "ddos_timestamp"  # ddos研判时间戳

# 异常事件枚举
EXCEPTION_EVENTS_MAP = {
    "vulnerability_steal_secrets": 1,
    "database_steal_secrets": 2,
    "video_steal_secrets": 3,
    "trojan_steal_secrets": 4,
    "apt_steal_secrets": 5,
    "ddos_steal_secrets": 6
}

DROPDOWN_DATA = [
    '河北', '山西', '辽宁', '吉林', '黑龙江', '江苏', '浙江', '安徽', '福建',
    '江西', '山东', '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州', '云南',
    '陕西', '甘肃', '青海', '台湾', '内蒙古', '广西', '西藏', '宁夏', '新疆', '北京', '天津',
    '上海', '重庆']


def get_location_info(username):
    # 超管权限：返回全部
    # if 'admin' in username:
    if starts_with_english(username):
        return DROPDOWN_DATA

    # 普通用户：返回所属区域
    matched_regions = [region for region in DROPDOWN_DATA if region in username]

    return matched_regions


def starts_with_english(s):
    """
    判断字符串是否以英文字符开头。

    Args:
        s (str): 需要判断的字符串（Python 2 中为 str 类型）。

    Returns:
        bool: 如果字符串以英文字符开头返回 True，否则返回 False。
    """
    return bool(re.match(r'^[A-Za-z]', s))


if __name__ == '__main__':
    import json

    print(json.dumps(CommonConf().get_alert_category(), ensure_ascii=False, indent=4))
    print(json.dumps(CommonConf().get_cate_type(is_event=True), ensure_ascii=False, indent=4))
