#!/usr/bin/env python
# -*- coding: utf-8 -*-
import sys
import traceback

from dataInterface.db.params import CPgSqlParam
from dataInterface.functions import CFunction
from permissionmanage.cur_models.rightmodels import RightService


def update_menu(app_name):
    """
    更新菜单
    """
    try:
        # 删表
        del_sql = "delete from internal_app_permission.tb_right_info where app_name='%s'" % app_name
        CFunction.execute(CPgSqlParam(del_sql))
        right_service = RightService()
        right_service.register_component_menu('/home/<USER>/ISOP/apps/%s/right_config.json' % app_name, 1)
        print("APP:%s 菜单更新成功" % app_name)
    except Exception as e:
        print(e)


def update_menu_sdk(app_name):
    """
    更新菜单，先删除，再注册
    """
    try:
        right_service = RightService()
        # 先给旧的卸载了
        right_service.unregister_component_menu(app_name)
        # 再给新的整上去
        right_service.register_component_menu('/home/<USER>/ISOP/apps/{}/right_config.json'.format(app_name), 1)
        print("{}：update menu success".format(app_name))
    except Exception as _:
        print("{}：update menu failure".format(app_name))
        traceback.print_exc()


if __name__ == '__main__':
    try:
        app_name = sys.argv[1]
        update_menu(app_name)
    except Exception as e:
        print("请正确运行脚本: python %s [APP_NAME]" % __file__)
