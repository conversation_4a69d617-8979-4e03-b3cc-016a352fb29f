#!/usr/bin/env python
# -*-coding:utf-8 -*-
from cncert_kj.utils.ftp_util import FtpClientConfig, FtpFactoryConfig, FptClientFactory, FtpClientPool, FTPClientUtils
from cncert_kj.utils import logger
from cncert_kj.utils.system_alarm_util import SystemAlarmUtil

logger = logger.init_logger("FTPClient")


class FTPServerService(object):
    def __init__(self, ftp_config, thread_pool):
        front = ftp_config['front']
        borrow_time_out = ftp_config['pool_borrow_timeout']
        executor = thread_pool.get_executor('life_cycle_worker')

        front_ftp_config = FtpClientConfig(front['host'], front['port'], front['username'], front['password'],
                                           front['buffer_size'], front['mode'], front['alive_check_interval'])

        front_ftp_factory_config = FtpFactoryConfig(front['max_client'], 'front', front_ftp_config)
        front_ftp_factory = FptClientFactory(front_ftp_factory_config)
        front_pool = FtpClientPool(front_ftp_factory, executor, front_ftp_config.alive_check_interval)
        self.front_ftp_config = front_ftp_config
        self.front_ftp_util = FTPClientUtils(front_pool, borrow_time_out)

    def download_file_block_from_ftp(self, local_file, remote_file):
        """
        从FTP下载文件
        """
        try:
            self.front_ftp_util.download_file(local_file, remote_file)
        except Exception as e:
            SystemAlarmUtil.add_alarm("download file:{} error,reason is :{}".format(
                remote_file, e
            ))
            raise e

    def upload_file_to_ftp(self, local_file, remote_file):
        """
        上传文件至FTP
        """
        try:
            self.front_ftp_util.upload_file(local_file, remote_file)
        except Exception as e:
            SystemAlarmUtil.add_alarm("upload file:{} error,reason is :{}".format(
                remote_file, e
            ))
            raise e

    def get_ftp_file_set(self, remote_path):
        """
        FTP服务目录下的文件集合
        """
        try:
            return self.front_ftp_util.get_file_set(remote_path)
        except Exception as e:
            SystemAlarmUtil.add_alarm("access ftp path:{} error,reason is :{}".format(
                remote_path, e
            ))
            raise e

    def delete_front_file(self, remote_path):
        """
        删除FTP服务目录下的文件
        """
        try:
            return self.front_ftp_util.delete_file(remote_path)
        except Exception as e:
            SystemAlarmUtil.add_alarm("delete file :{} error,reason is :{}".format(
                remote_path, e
            ))
            raise e
