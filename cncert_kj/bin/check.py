# coding:utf-8
"""
    1分钟被调用一次，用于监控一些常驻进程，负责拉起他们
"""
import subprocess
import datetime

from cncert_kj.utils import logger

mlog = logger.init_logger('check')


def run_and_check():
    """
        巡检资产实时消费任务
    """
    cmd = 'ps aux|grep asset_consumer.py'
    res = subprocess.check_output(cmd, shell=True)
    #  print "++++++++++++:",res
    # print type(res)
    # mlog.info("巡检时间：{}".format(datetime.datetime.now()))
    if "python /home/<USER>/ISOP/apps/cncert_kj/script/asset_consumer.py" in res:
        mlog.info("资产实时消费任务正常")
        return True
    else:

        mlog.warning("资产实时消费任务已挂掉：/home/<USER>/ISOP/apps/cncert_kj/script/asset_consumer.py")
        return False


if __name__ == '__main__':
    result = run_and_check()
    print(result)

