#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@ Author    : <PERSON><PERSON><PERSON><PERSON><PERSON>
@ File      : day_model_custom_ports_flow_detect.py
@ Time      : 2024/9/12 10:49
@ Desc      :
指定端口流量检测,查询IP对索引，指定端口， 流出流量超过100M的IP对， 可能最后会生成告警
每天一次
境内重点单位，非常用端口， 流出流量大于流入流量，且流出流量超过阈值 数据生成CSV文件

境内重点单位IP在ES是asset_ip的话， asset_boundary必定是2, 所以我的数据不需要进行对调
只需要根据direct来区分主被动
1、数据对调逻辑：
                asset_boundary = 2                                数据不动
                asset_boundary = 1                                数据对调
2、主被动判断逻辑
                asset_boundary = 2, direct =2                     主动
                asset_boundary = 2, direct =1                     被动
                asset_boundary = 1, direct =1                     主动
                asset_boundary = 1, direct =2                     被动

"""
import codecs
import csv
import json
import os
import sys
import time
import traceback
import uuid
from datetime import datetime, timedelta

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.utils import net, params_validate, logger
from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.utils.conf_util import NETFLOW_IP_PAIR_5MIN, TLLOG_IP_PAIR_5MIN
from cncert_kj.utils.es_utils import ESUtil
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_filing_sys import RequestFilingSys
from cncert_kj.utils.time_trans import timestamp2day, timestamp2format

mlog = logger.init_logger('day_model_custom_ports_flow_detect')
reload(sys)
sys.setdefaultencoding('utf-8')


class TCPPortFlowDetect:
    def __init__(self, ports=None, flow_type=1, protocol=None, days=30, now_time=None):
        self.es_util = ESUtil()
        self.analysis_tech = 1  # 是否属于动态基线， 属于的话需要在单位查询过滤出开启动态基线的单位
        self.max_buckets = 10000
        # 阈值100MB
        self.threshold = 1024 * 1024 * 100
        self.type = ""
        # 端口
        self.ports = ports
        # 查询天数
        self.days = days
        # 协议类型
        self.protocol = protocol
        # 通联类型
        self.tclog_type = 1
        # netflow类型
        self.netflow_type = 2
        self.flow_type = flow_type
        self.file_path = "/home/<USER>/ISOP/store/kj_flow_detect/day_model_tcp_port_flow_detect_{}".format(self.ports)
        if self.flow_type == self.tclog_type:
            self.direct = [1, 2]
            self.type_msg = "通联日志"
            self.es_index = TLLOG_IP_PAIR_5MIN
            self.table_name = "internal_app_bsa_gjk.traffic_alert"
            self.fields = ','.join((
                "uuid",
                "name",
                "type",
                "start_time",
                "end_time",
                "sip",
                "src_region",
                "src_operator",
                "src_iot",
                "src_service",
                "src_unit",
                "src_com",
                "sport",
                "dip",
                "dst_region",
                "dst_operator",
                "dst_iot",
                "dst_service",
                "dst_unit",
                "dst_com",
                "dport",
                "up_bytes_all",
                "down_bytes_all",
                "up_packets_all",
                "down_packets_all",
                "flow_list",
                "analysis_tech",
                "ipv6",
                "flow_logs"
            ))
            mlog.info("开始查询 tclog_ip_pair_5min 索引")
        else:
            self.direct = []
            self.type_msg = "NetFlow"
            self.es_index = NETFLOW_IP_PAIR_5MIN
            self.table_name = "internal_app_bsa_gjk.netflow_alert_5min"
            self.fields = ','.join((
                "uuid",
                "name",
                "type",
                "start_time",
                "end_time",
                "sip",
                "src_region",
                "src_operator",
                "src_iot",
                "src_service",
                "src_unit",
                "src_com",
                "sport",
                "dip",
                "dst_region",
                "dst_operator",
                "dst_iot",
                "dst_service",
                "dst_unit",
                "dst_com",
                "dport",
                "bytes_all",
                "bytes_all_down",
                "packets_all",
                "packets_all_down",
                "analysis_tech",
                "ipv6",
                "flow_logs"
            ))
            mlog.info("开始查询 netflow_ip_pair_5min 索引")
        # 当前时间, 默认为当前时间， 用于从当前时间往前查询 days 天
        if now_time:
            self.now_time = now_time
        else:
            self.now_time = int(time.time())

        self.insert_sql_template = """
        INSERT INTO {table_name} ({fields}) VALUES ({params})
        """

    def generate_time_ranges(self):
        """生成时间范围的方法，从当天每次向前推一天"""
        today_end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        time_ranges = []
        # 从结束时间开始，循环 N 次
        for _ in range(self.days):
            # 计算开始时间为前一天的 0 点
            start_time = today_end_time - timedelta(days=1)
            # 将开始时间和结束时间转换为时间戳
            start_timestamp = int(time.mktime(start_time.timetuple()))
            end_timestamp = int(time.mktime(today_end_time.timetuple()))
            # 将时间区间加入到结果列表
            time_ranges.append((start_timestamp, end_timestamp))
            # 更新结束时间为开始时间，准备下一次循环
            today_end_time = start_time
        return time_ranges

    def get_key_unit(self):
        """查询境内重点单位"""
        sql = """
        SELECT id, name, ip_range FROM internal_app_bsa_gjk.key_unit WHERE boundary=2 
        """
        # 动态基线的话需要过滤
        if self.analysis_tech == 2:
            sql += " AND tclog_dynamic_baseline=True"
        if self.flow_type == self.tclog_type:
            sql += " AND tclog_key_monitor=True"
        else:
            sql += " AND netflow_key_monitor=True"
        unit_ip = json.loads(CFunction.execute(CPgSqlParam(sql)))
        ip_list = []
        ip_unit_data = {}
        for i in unit_ip:
            asset_id = i[0]
            asset_name = i[1]
            ip_range = i[2]
            asset_ips = net.ips_to_list(ip_range.split(","))
            for ip_ in asset_ips:
                ip_ = ip_.strip()
                if not ip_:
                    continue
                ip_check = params_validate.validate_ipv4(ip_)
                if ip_check:
                    ip_list.append(ip_)
                    ip_unit_data[ip_] = asset_name
                else:
                    mlog.error(u"错误IP：{} ， 单位：{}, ID: {}".format(ip_, asset_name, asset_id))
        ip_list = [net.ip_to_int32(i) for i in ip_list]
        return ip_list, ip_unit_data

    def filter_func(self, start_time, end_time, ip_list, direct=None, asset_boundary=None):
        """
        过滤ip流量超过阈值的IP， 减少部分IP数量
        :return:
        """
        mlog.info("日期:{} - {}; ip共有 :{}".format(timestamp2day(start_time), timestamp2day(end_time), len(ip_list)))
        SIZE = step = 10000
        ips = []
        for i in range(0, len(ip_list), step):
            query_dsl = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "terms": {
                                    "asset_ip": ip_list[i:i + step]
                                }
                            },
                            {
                                "range": {
                                    "end_time": {
                                        "gte": start_time,
                                        "lt": end_time
                                    }
                                }
                            },
                            {
                                "term": {
                                    "dport": self.ports
                                }
                            }
                        ],
                    }
                },
                "size": 1,
                "aggregations": {
                    "asset_ip": {
                        "terms": {
                            "field": "asset_ip",
                            "size": SIZE
                        },
                        "aggregations": {
                            "peer_ip": {
                                "aggregations": {
                                    "out_bytes": {
                                        "sum": {
                                            "field": "out_bytes"
                                        }
                                    },
                                    "in_bytes": {
                                        "sum": {
                                            "field": "in_bytes"
                                        }
                                    },
                                    # "asset_boundary_hits": {
                                    #     "top_hits": {
                                    #         "size": 1,
                                    #         "_source": {
                                    #             "includes": [
                                    #                 "asset_boundary"
                                    #             ]
                                    #         }
                                    #     }
                                    # },
                                    # "direct_hits": {
                                    #     "top_hits": {
                                    #         "size": 1,
                                    #         "_source": {
                                    #             "includes": [
                                    #                 "direct"
                                    #             ]
                                    #         }
                                    #     }
                                    # }
                                },
                                "terms": {
                                    "field": "peer_ip",
                                    "size": 1,
                                    "order": {
                                        "out_bytes": "desc"
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if self.protocol:
                query_dsl["query"]["bool"]["must"].append({
                    "term": {
                        "protocol": self.protocol.lower()
                    }
                })
            if direct and asset_boundary:
                query_dsl = self.add_direct(query_dsl, direct, asset_boundary)
            result = self.es_util.search_es(self.es_index, query_dsl)
            if not result:
                continue

            res_list = result.get("aggregations", {}).get("asset_ip", {}).get("buckets", [])
            for i in res_list:
                peer_buckets = i.get("peer_ip", {}).get("buckets", [{}])[0]
                out_bytes = peer_buckets.get("out_bytes", {}).get("value", 0)
                # in_bytes : peer_buckets.get("in_bytes", {}).get("value", 0)
                # 是否加流出大于流入的判断
                # if out_bytes < in_bytes:
                #     continue

                # direct_source = peer_buckets['direct_hits']["hits"]["hits"][0]["_source"]
                # direct_ = direct_source.get("direct", "") if direct_source else ""
                # asset_boundary_source = peer_buckets['asset_boundary_hits']["hits"]["hits"][0]["_source"]
                # asset_boundary = asset_boundary_source.get("asset_boundary", "") if asset_boundary_source else ""
                # direct_ == 1 and asset_boundary == 2 被动类型需要调换流量
                # direct_ == 1 and asset_boundary == 1 主动类型需要调换流量
                # if (direct and direct_ == 1 and asset_boundary == 2) or \
                #         (direct and direct_ == 1 and asset_boundary == 1):
                #     # 通联， 流量取反，in_bytes是流出
                #     out_bytes = in_bytes
                if out_bytes > self.threshold:
                    asset_ip = i.get("key")
                    ips.append(asset_ip)
        ips = list(set(ips))
        mlog.info("日期:{} - {}; 对端IP流量超过阈值的asset_ip数量为：{}".format(
            timestamp2day(start_time), timestamp2day(end_time), len(ips)
        ))
        return ips

    def add_direct(self, query_dsl, direct, asset_boundary):
        """
        根据direct给DSL加搜索条件
        """
        query_dsl["query"]["bool"]["must"].append({
            "term": {
                "direct": direct
            }
        })

        query_dsl["query"]["bool"]["must"].append({
            "term": {
                "asset_boundary": asset_boundary
            }
        })
        return query_dsl

    def generate_dsl(self, start_time, end_time, asset_ip, direct=None, asset_boundary=None):
        ASSET_IP_SIZE = len(asset_ip)
        PEER_IP_SIZE = self.max_buckets / ASSET_IP_SIZE
        dsl = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {
                                "end_time": {
                                    "gte": start_time,
                                    "lt": end_time
                                }
                            }
                        },
                        {
                            "terms": {
                                "asset_ip": asset_ip
                            }
                        },
                        {
                            "term": {
                                "dport": self.ports
                            }
                        }
                    ],
                }
            },
            "aggregations": {
                "asset_ip": {
                    "terms": {
                        "field": "asset_ip",
                        "size": ASSET_IP_SIZE
                    },
                    "aggregations": {
                        "peer_ip": {
                            "aggregations": {
                                "out_bytes": {
                                    "sum": {
                                        "field": "out_bytes"
                                    }
                                },
                                "in_bytes": {
                                    "sum": {
                                        "field": "in_bytes"
                                    }
                                },
                                "start_time": {
                                    "min": {
                                        "field": "start_time"
                                    }
                                },
                                "end_time": {
                                    "max": {
                                        "field": "end_time"
                                    }
                                },
                                "dport_hits": {
                                    "top_hits": {
                                        "size": 1,
                                        "_source": {
                                            "includes": [
                                                "dport"
                                            ]
                                        }
                                    }
                                },
                                "protocol_hits": {
                                    "top_hits": {
                                        "size": 1,
                                        "_source": {
                                            "includes": [
                                                "protocol"
                                            ]
                                        }
                                    }
                                },
                                "asset_boundary_hits": {
                                    "top_hits": {
                                        "size": 1,
                                        "_source": {
                                            "includes": [
                                                "asset_boundary"
                                            ]
                                        }
                                    }
                                },
                                "direct_hits": {
                                    "top_hits": {
                                        "size": 1,
                                        "_source": {
                                            "includes": [
                                                "direct"
                                            ]
                                        }
                                    }
                                }
                            },
                            "terms": {
                                "field": "peer_ip",
                                "size": PEER_IP_SIZE,
                                "order": {
                                    "out_bytes": "desc"
                                }
                            }
                        }
                    }
                }
            },
            "size": 0
        }
        if self.protocol:
            dsl["query"]["bool"]["must"].append({
                "term": {
                    "protocol": self.protocol.lower()
                }
            })
        if direct and asset_boundary:
            dsl = self.add_direct(dsl, direct, asset_boundary)
        return dsl

    def handle_data(self, result, ip_unit_data):
        direct_status = {
            1: "被动",
            2: "主动"
        }
        data = {}
        res_list = result.get("aggregations", {}).get("asset_ip", {}).get("buckets", [])
        for i in res_list:
            buckets = i.get("peer_ip", {}).get("buckets", [])
            asset_ip = i.get("key")
            _asset_ip = net.int32_to_ip(asset_ip)
            data.update(self.handle_bucket(buckets, ip_unit_data, asset_ip, _asset_ip, direct_status))
        return data

    def handle_bucket(self, buckets, ip_unit_data, asset_ip, _asset_ip, direct_status):
        data = {}
        for bucket in buckets:
            out_bytes = bucket.get("out_bytes", {}).get("value", 0)
            in_bytes = bucket.get("in_bytes").get("value", 0)
            # 是否加流出大于流入的判断
            # if out_bytes < in_bytes:
            #     continue
            direct_source = bucket['direct_hits']["hits"]["hits"][0]["_source"]
            direct_ = direct_source.get("direct", "") if direct_source else ""
            asset_boundary_source = bucket['asset_boundary_hits']["hits"]["hits"][0]["_source"]
            asset_boundary = asset_boundary_source.get("asset_boundary", "") if asset_boundary_source else ""

            if out_bytes < self.threshold:
                break
            peer_ip = bucket.get("key")
            _peer_ip = net.int32_to_ip(peer_ip)
            dport_source = bucket['dport_hits']["hits"]["hits"][0]["_source"]
            dport = dport_source.get("dport", "") if dport_source else ""
            mlog.info("asset_ip:{} , peer_ip:{}, dport:{}, 流出流量：{}".format(
                _asset_ip, _peer_ip, dport, long2unit(out_bytes)
            ))

            start_time = bucket.get("start_time", {}).get("value", 0)
            end_time = bucket.get("end_time", {}).get("value", 0)
            protocol_source = bucket['protocol_hits']["hits"]["hits"][0]["_source"]
            protocol = protocol_source.get("protocol", "") if protocol_source else ""
            key = "{}_{}_{}_{}_{}_{}".format(asset_ip, peer_ip, start_time, end_time, direct_, asset_boundary)
            # 这里将数据全部进行了格式化转换操作， 用于将数据写入CSV文件
            str_asset_ip = net.int32_to_ip(asset_ip)
            res = {
                "asset_ip": str_asset_ip,
                "peer_ip": net.int32_to_ip(peer_ip),
                "asset_unit": ip_unit_data.get(str_asset_ip, "").replace("\n", "").replace(",", " "),
                "out_bytes": long2unit(out_bytes),
                "in_bytes": long2unit(in_bytes),
                "start_time": timestamp2format(start_time),
                "end_time": timestamp2format(end_time),
                "dport": dport,
                "protocol": protocol,
                "outflow_inflow_ratio": "%.2f" % (out_bytes / in_bytes) if in_bytes else out_bytes,
            }
            if self.direct:
                res["direct"] = direct_status.get(direct_)
            data[key] = res
        return data

    def get_last_day_flow_logs(self, ip_pair, start_time, end_time):
        """
        获取flow_logs
        """
        try:
            res_data = {}
            for key, val in ip_pair.items():
                asset_ip = val.get("asset_ip")
                peer_ip = val.get("peer_ip")
                query_dsl = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "term": {
                                        "asset_ip": asset_ip
                                    }
                                },
                                {
                                    "term": {
                                        "peer_ip": peer_ip
                                    }
                                },
                                {
                                    "range": {
                                        "end_time": {
                                            "gte": start_time,
                                            "lte": end_time
                                        }
                                    }
                                },
                                {
                                    "term": {
                                        "dport": self.ports
                                    }
                                }
                            ],
                        }
                    },
                    "size": 1000
                }
                if self.protocol:
                    query_dsl["query"]["bool"]["must"].append({
                        "term": {
                            "protocol": self.protocol.lower()
                        }
                    })
                result = self.es_util.search_es(self.es_index, query_dsl)
                if not result and result.get("hits", {}).get("hits", []):
                    continue
            return res_data
        except Exception as e:
            mlog.error("flow_logs查询失败：{}".format(e))
            mlog.error(traceback.format_exc())
            return {}

    def generate_alert(self, es_data, start_time, end_time):
        """
        生成告警sql参数
        :return:
        """
        params_list = []
        new_params_list = []
        ip_pair = {}
        ip_list = []
        # key 是更加asset_ip和peer_ipMD5生成的数据的key
        for key, value in es_data.items():
            # 如果有数据
            if value:
                asset_ip = value.get("asset_ip")  # 重点单位IP
                peer_ip = value.get("peer_ip")  # 对端IP
                _start_time = value.get("start_time")  # ip对数据的最小开始时间
                _end_time = value.get("end_time")  # ip对数据的最大结束时间
                dst_port = value.get("dport")  # 对端端口
                in_bytes = value.get("in_bytes")
                out_bytes = value.get("out_bytes")
                if self.protocol:
                    alert_name = "{}协议主动向境外非常用端口传输超过{}".format(self.protocol, long2unit(self.threshold))
                else:
                    alert_name = "主动向境外非常用端口传输超过{}".format(long2unit(self.threshold))
                params_list.append({
                    "key": key,
                    "asset_ip": net.int32_to_ip(asset_ip),
                    "peer_ip": net.int32_to_ip(peer_ip),
                    # "asset_name": asset_name,
                    "name": alert_name,
                    "uuid": str(uuid.uuid4()),
                    "end_time": _end_time,
                    "out_bytes": in_bytes,
                    "in_bytes": out_bytes,
                    "start_time": _start_time,
                    "dst_port": dst_port
                })
                ip_list.append(asset_ip)
                ip_list.append(peer_ip)
                # 用来查flow_logs
                ip_pair[key] = {
                    "asset_ip": asset_ip,
                    "peer_ip": peer_ip
                }

        if not params_list:
            return new_params_list
        # 查询备案系统
        with RequestFilingSys() as request_filing_obj:
            ip_details = request_filing_obj.query_icp_api(ip_list)
        # 开发环境 ip_details = {}
        # 查flow_logs
        flow_logs = self.get_last_day_flow_logs(ip_pair, start_time, end_time)
        # 开发环境 flow_logs = {}
        for params in params_list:
            key = params.get("key")
            asset_ip = params.get("asset_ip")
            peer_ip = params.get("peer_ip")
            asset_ip_detail = ip_details.get(asset_ip, {})
            peer_ip_detail = ip_details.get(peer_ip, {})
            params["src_region"] = asset_ip_detail.get("region", "")
            params["src_operator"] = asset_ip_detail.get("operator", "")
            params["src_com"] = asset_ip_detail.get("user", "")

            params["dst_region"] = peer_ip_detail.get("region", "")
            params["dst_operator"] = peer_ip_detail.get("operator", "")
            params["dst_com"] = peer_ip_detail.get("user", "")

            flow_log = flow_logs.get(key, [])
            if len(flow_log) >= 10000:
                mlog.info("flow_logs数量大于等于10000")
            params["flow_log"] = "|".join([json.dumps(log, ensure_ascii=False) for log in flow_log])
            if not flow_log:
                flow_log = [{}]
            asset_name = flow_log[0].get("src_mark3")

            if is_ipv4_correct(asset_ip):
                ipv6 = False
            else:
                ipv6 = True
            if self.flow_type == self.tclog_type:
                new_params_list.append([
                    params.get("uuid"),
                    params.get("name"),
                    self.type,
                    params.get("start_time"),
                    params.get("end_time"),
                    params.get("asset_ip"),
                    params.get("src_region"),
                    params.get("src_operator"),
                    "",  # src_iot
                    "",  # src_service
                    asset_name,
                    params.get("src_com"),
                    -1,  # sport
                    params.get("peer_ip"),
                    params.get("dst_region"),
                    params.get("dst_operator"),
                    "",  # dst_iot
                    "",  # dst_service
                    "",  # dst_unit
                    params.get("dst_com"),
                    params.get("dst_port", -1),
                    params.get("out_bytes"),
                    params.get("in_bytes", 0),
                    0,  # up_packets_all
                    0,  # down_packets_all
                    "",  # flow_list
                    self.analysis_tech,
                    ipv6,
                    params.get("flow_log")
                ])
            else:
                new_params_list.append([
                    params.get("uuid"),
                    params.get("name"),
                    self.type,
                    params.get("start_time"),
                    params.get("end_time"),
                    params.get("asset_ip"),
                    params.get("src_region"),
                    params.get("src_operator"),
                    "",  # src_iot
                    "",  # src_service
                    asset_name,
                    params.get("src_com"),
                    -1,  # sport
                    params.get("peer_ip"),
                    params.get("dst_region"),
                    params.get("dst_operator"),
                    "",  # dst_iot
                    "",  # dst_service
                    "",  # dst_unit
                    params.get("dst_com"),
                    params.get("dst_port", -1),
                    params.get("out_bytes"),
                    params.get("in_bytes", 0),
                    0,  # packets_all
                    0,  # packets_all_down
                    self.analysis_tech,
                    ipv6,
                    params.get("flow_log")
                ])
        return new_params_list

    def insert_alert(self, params_list):
        """
        插入数据库
        :param params_list:
        :return:
        """
        for _ in range(3):
            try:
                for i in params_list:
                    val = ",".join(len(i) * ["%s"])
                    CFunction.execute(
                        CPgSqlParam(
                            self.insert_sql_template.format(table_name=self.table_name, fields=self.fields, params=val),
                            params=tuple(i)))
                if self.flow_type == self.tclog_type:
                    name = "通联"
                else:
                    name = "NetFlow"
                mlog.info("生成{}告警数量：{}".format(name, len(params_list)))
                break
            except Exception as e:
                mlog.error("告警数据入库失败：{}".format(e))
                mlog.error(traceback.format_exc())

    def write_csv(self, data, start_time, end_time):
        if self.flow_type == self.tclog_type:
            file_name = "{}-{}-tllog-alert.csv".format(timestamp2day(start_time), timestamp2day(end_time))
        else:
            file_name = "{}-{}-netflow-alert.csv".format(timestamp2day(start_time), timestamp2day(end_time))
        rows = data.values()
        file_path = os.path.join(self.file_path, file_name)
        with open(file_path, mode='wb') as f:
            f.write(codecs.BOM_UTF8)
            if self.direct:
                writer = csv.DictWriter(f, fieldnames=["asset_ip", "peer_ip", "asset_unit", "direct",
                                                       "out_bytes", "in_bytes", "dport", "protocol",
                                                       "start_time", "end_time", "outflow_inflow_ratio"])
            else:
                writer = csv.DictWriter(f, fieldnames=["asset_ip", "peer_ip", "asset_unit", "out_bytes",
                                                       "in_bytes", "dport", "protocol", "start_time",
                                                       "end_time", "outflow_inflow_ratio"])
            # 写入表头
            writer.writeheader()
            # 写入数据行
            writer.writerows(rows)

    def handle_run1(self, step, start_time, end_time, ip_list, ip_unit_data):
        n = 0
        data = {}
        ips_direct1 = self.filter_func(start_time, end_time, ip_list, direct=1, asset_boundary=2)
        ips_direct1 += self.filter_func(start_time, end_time, ip_list, direct=2, asset_boundary=2)

        total_n1 = (len(ips_direct1) + step - 1) / step
        for i in range(0, len(ips_direct1), step):
            asset_ip = ips_direct1[i:i + step]
            n += 1
            try:
                begin_time = time.time()
                res1_1 = self.es_util.search_es(self.es_index,
                                                self.generate_dsl(start_time, end_time, asset_ip, direct=1,
                                                                  asset_boundary=2))
                res1_2 = self.es_util.search_es(self.es_index,
                                                self.generate_dsl(start_time, end_time, asset_ip, direct=2,
                                                                  asset_boundary=2))
                stop_time = time.time()
                if res1_1:
                    data.update(self.handle_data(res1_1, ip_unit_data))
                if res1_2:
                    data.update(self.handle_data(res1_2, ip_unit_data))
                mlog.info(
                    "{}; 查询进度 {}/{}, 当前数据数量为:{}".format(timestamp2day(start_time), n, total_n1, len(data)))
                mlog.info("本次查询耗时: {}".format(stop_time - begin_time))
            except Exception as e:
                mlog.error("{}/{},查询报错:{}".format(n, total_n1, e))
                mlog.error(traceback.format_exc())
        return data

    def handle_run2(self, step, start_time, end_time, ip_list, ip_unit_data):
        n = 0
        data = {}
        ips = self.filter_func(start_time, end_time, ip_list)
        total_n = (len(ips) + step - 1) / step
        for i in range(0, len(ips), step):
            mlog.info("查询类型:{}".format(self.type_msg))
            asset_ip = ips[i:i + step]
            n += 1
            try:
                begin_time = time.time()
                res = self.es_util.search_es(self.es_index,
                                             self.generate_dsl(start_time, end_time, asset_ip))
                stop_time = time.time()
                if res:
                    data.update(self.handle_data(res, ip_unit_data))
                mlog.info(
                    "{}; 查询进度 {}/{}, 当前数据数量为:{}".format(timestamp2day(start_time), n, total_n, len(data)))
                mlog.info("本次查询耗时: {}".format(stop_time - begin_time))
            except Exception as e:
                mlog.error("{}/{},查询报错:{}".format(n, total_n, e))
                mlog.error(traceback.format_exc())
        return data
    def run(self):
        try:
            if not os.path.exists(self.file_path):
                os.mkdir(self.file_path)
            step = 500
            # 从当前时间开始向前推 ？ 天， 获取每天的开始和结束时间
            time_ranges = self.generate_time_ranges()
            ip_list, ip_unit_data = self.get_key_unit()
            for start_time, end_time in time_ranges:
                data = {}
                # 每天IP流量不一样, 所有需要按照时间进行一次过滤
                if self.direct:
                    data.update(self.handle_run1(step, start_time, end_time, ip_list, ip_unit_data))
                else:
                    data.update(self.handle_run2(step, start_time, end_time, ip_list, ip_unit_data))
                if data:
                    self.write_csv(data, start_time, end_time)
                mlog.info("{}-{}查询完成，数量:{}\n".format(timestamp2day(start_time), timestamp2day(end_time), len(data)))
                """
                数据入库
                data_params = self.generate_alert(data, start_time, end_time)
                mlog.info("开始插入数据库")
                self.insert_alert(data_params)
                """
        except Exception as err:
            mlog.error("{}".format(err))
            mlog.error(traceback.format_exc())


if __name__ == '__main__':
    """nohup python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_custom_ports_flow_detect.py &"""

    """
    添加定时任务 每天凌晨1点跑一次
    * 0 1 * * * python /home/<USER>/ISOP/apps/cncert_kj/script/day_model_custom_ports_flow_detect.py 1005 1
    """

    # 常用端口列表/ DSL中需要not in
    port = 1005
    days = 1
    if len(sys.argv) > 2:
        print("python day_model_custom_ports_flow_detect.py <port> <days>")
        print("python day_model_custom_ports_flow_detect.py 1005 7")
        port = int(sys.argv[1])
        days = int(sys.argv[2])
    mlog.info("查询端口:{}, 查询天数:{}".format(port, days))
    stime = time.time()
    TCPPortFlowDetect(ports=port, flow_type=1, protocol=None, days=days).run()
    TCPPortFlowDetect(ports=port, flow_type=2, protocol=None, days=days).run()
    etime = time.time()
    mlog.info("总耗时: {} 秒".format(etime - stime))
