#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: event_alert_daily_stats.py
# @time: 2025/3/13 10:45
"""
每天事件，告警数量统计脚本
"""
import argparse
import datetime
import json
import os
import sys
import time
import traceback

from collections import OrderedDict, defaultdict
from datetime import datetime, timedelta

from cncert_kj.lib import openpyxl
from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.base_model import EventAlertDailyStatsModel
from cncert_kj.utils import logger
from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('event_alert_daily_stats')


class EventAlertDailyStats(object):
    def __init__(self):
        self.time_format = DATE_TIME_FORMAT
        self.netflow_tag = "NETFLOW"
        self.tclog_tag = "TCLOG"

    def print_time_zone(self, start_timestamp, end_timestamp):
        start_of_yesterday_dt = datetime.fromtimestamp(start_timestamp)
        end_of_yesterday_dt = datetime.fromtimestamp(end_timestamp)
        start_of_yesterday_str = start_of_yesterday_dt.strftime(self.time_format)
        end_of_yesterday_str = end_of_yesterday_dt.strftime(self.time_format)
        mlog.info("本次任务运行查询的时间区间：{}--{}".format(start_of_yesterday_str, end_of_yesterday_str))

    @staticmethod
    def get_time_zone():
        """
        获取运行区间
        :return:
        """
        now = datetime.now()
        start_of_yesterday = now - timedelta(days=1)
        start_of_yesterday = start_of_yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_yesterday = start_of_yesterday + timedelta(days=1) - timedelta(seconds=1)
        timestamp_start_of_yesterday = int(time.mktime(start_of_yesterday.timetuple()))
        timestamp_end_of_yesterday = int(time.mktime(end_of_yesterday.timetuple()))
        return timestamp_start_of_yesterday, timestamp_end_of_yesterday

    def get_cycle_key(self, detect_cycle):
        cycle_key = "5min" if detect_cycle == "5min" else "day"
        return cycle_key

    def get_alert_template_data(self):
        """
        获取告警模版里的数据，用以后续数据比对获取
        """
        current_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        file_path = os.path.join(current_path, "template", "KJ告警模型梳理与统计.xlsx")
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)
        # 获取所有工作表对象
        sheets = workbook.worksheets
        sheet = sheets[1]
        is_tclog = True
        last_alert_category = ""
        last_data_category = ""
        # 初始化字典
        model_principles = {self.tclog_tag: {"5min": {}, "day": {}}, self.netflow_tag: {"5min": {}, "day": {}}}
        for row in sheet.iter_rows(min_row=3, values_only=True):
            data_category, alert_category, alert_name, model_principle, detect_type, detect_cycle, type_value = row[1:8]
            last_data_category = data_category if data_category and data_category != last_data_category else last_data_category
            last_alert_category = alert_category if alert_category and alert_category != last_alert_category else last_alert_category
            if row[0] == self.netflow_tag:
                is_tclog = False
            key = "APT_{}".format(type_value) if row[7] == 91 and "APT" in alert_name else type_value
            # 判断数据源
            cycle_key = self.get_cycle_key(detect_cycle)
            data_source = self.tclog_tag if is_tclog else self.netflow_tag
            model_principles[data_source][cycle_key][key] = {
                "data_source": data_source,
                "data_category": last_data_category,
                "alert_category": last_alert_category,
                "alert_name": alert_name,
                "model_principle": model_principle,
                "detect_type": detect_type,
                "detect_cycle": detect_cycle,
                "type_value": type_value
            }
        return (
            model_principles[self.tclog_tag]["5min"], model_principles[self.tclog_tag]["day"],
            model_principles[self.netflow_tag]["5min"],
            model_principles[self.netflow_tag]["day"])

    @staticmethod
    def get_alert_and_event_count(report_type, start_timestamp, end_timestamp, analysis_tech, belong):
        """
        创建临时表
        检测手段， 静态基线检测-5min：1， 动态基线检测-天模型：2
        时间筛选逻辑：事件的结束时间在这个时间范围内的及算当天的事件
        """

        APT_PARAMS = "%APT%"
        if report_type == 1:
            alert_table_name = "internal_app_bsa_gjk.traffic_alert"
        elif report_type == 2:
            alert_table_name = "internal_app_bsa_gjk.netflow_alert_5min"
        else:
            return []
        if analysis_tech == 1:
            _rule_table_name = "internal_app_bsa_gjk.detect_rule"
        else:
            _rule_table_name = "internal_app_bsa_gjk.dynamic_detect_rule"
        params = [belong, start_timestamp, end_timestamp, analysis_tech, report_type, start_timestamp, end_timestamp,
                  analysis_tech]
        create_temp_table = """
        DROP TABLE IF EXISTS temp_type_conf;
        DROP TABLE IF EXISTS temp_tllog;
        CREATE TEMP TABLE temp_tllog AS SELECT type,threshold, detect_name FROM {_rule_table_name} WHERE belong=%s AND type != 91;
    
        SELECT t.type, t.threshold, t1.alert_count, t2.event_count,t.detect_name FROM temp_tllog AS t
        LEFT JOIN (SELECT type,count(*) AS alert_count FROM {alert_table_name}
        WHERE end_time BETWEEN  %s AND %s AND analysis_tech=%s  
        group by type) AS t1 ON t1.type=t.type
        LEFT JOIN (SELECT event_type,count(*) AS event_count FROM internal_app_bsa_gjk.continuous_events 
        WHERE 
            report_type=%s AND end_time BETWEEN  %s AND %s AND analysis_tech=%s 
        GROUP BY event_type) AS t2 ON t2.event_type = t.type
        ORDER BY t.type;
        """.format(_rule_table_name=_rule_table_name, alert_table_name=alert_table_name)

        res = json.loads(CFunction.execute(CPgSqlParam(create_temp_table, params=params)))
        """
        由于境内向境外APT组织非常用端口上传告警和境内重点单位向境外非常用端口上传告警的type 都是91 所以要分开处理，
        """
        params2 = [APT_PARAMS, APT_PARAMS, start_timestamp, end_timestamp, analysis_tech,
                   APT_PARAMS,
                   APT_PARAMS, report_type, start_timestamp, end_timestamp, analysis_tech, belong]
        _sql = """
            WITH alert_counts AS (
            SELECT
                type,
                COUNT(CASE WHEN name LIKE %s THEN 1 END) AS alert_count_1,
                COUNT(CASE WHEN name NOT LIKE %s THEN 1 END) AS alert_count_2
            FROM
                {alert_table_name}
            WHERE
                end_time BETWEEN  %s AND %s and analysis_tech=%s and type=91
            GROUP BY
                type
                ),
            event_counts AS (
                SELECT
                    event_type,
                    COUNT(CASE WHEN c_e_tag.dst_info LIKE %s THEN 1 END) AS event_count_1,
                    COUNT(CASE WHEN c_e_tag.dst_info NOT LIKE %s OR c_e_tag.dst_info IS NULL THEN 1 END) AS event_count_2
                FROM
                    internal_app_bsa_gjk.continuous_events AS e1
                    LEFT JOIN (
                            SELECT event_id, string_agg(case when tag_type=2 then tag_content end,'&') dst_info
                            FROM internal_app_bsa_gjk.continuous_events_tag cet
                            GROUP BY event_id
                                        ) c_e_tag on e1.event_id = c_e_tag.event_id
                WHERE
                    report_type=%s AND end_time BETWEEN  %s AND %s AND analysis_tech=%s AND event_type=91
                GROUP BY
                    event_type
            )
            SELECT
                t.type,
                t.threshold,
                COALESCE(a.alert_count_1, 0) AS alert_count_1,
                COALESCE(a.alert_count_2, 0) AS alert_count_2,
                COALESCE(e.event_count_1, 0) AS event_count_1,
                COALESCE(e.event_count_2, 0) AS event_count_2,
                t.detect_name
            FROM
                {_rule_table_name} AS t
            LEFT JOIN
                alert_counts AS a ON a.type = t.type
            LEFT JOIN
                event_counts AS e ON e.event_type = t.type
            WHERE
                t.belong = %s
                AND t.type = 91
            ORDER BY
                t.type;
            """.format(alert_table_name=alert_table_name, _rule_table_name=_rule_table_name)
        res2 = json.loads(CFunction.execute(CPgSqlParam(_sql, params=tuple(params2))))
        for i in res2:
            if "APT" in i[6].upper():
                # APT类型的告警
                res.append([i[0], i[1], i[2], i[4], i[6]])
            else:
                res.append([i[0], i[1], i[3], i[5], i[6]])
        if report_type == 2 and analysis_tech == 1:
            # 只有netflow在静态基线下才走到这里
            _sql2 = """
                CREATE TEMP TABLE temp_type_conf AS
                SELECT type_value, type_name FROM internal_app_bsa_gjk.type_conf
                WHERE is_tclog=false AND type_value ::integer IN (12, 32, 42);
                SELECT
                    t.type_value,
                    0 AS threshold,
                    t1.alert_count,
                    t2.event_count,
                    t.type_name || '告警' AS type_name_with_alert
                FROM temp_type_conf as t
                LEFT JOIN (SELECT type,count(*) AS alert_count FROM {alert_table_name}
                WHERE end_time BETWEEN  %s AND %s AND analysis_tech=%s AND type IN (12, 32, 42)
                GROUP BY type) AS t1 ON t1.type=t.type_value::integer
                LEFT JOIN (SELECT event_type,count(*) AS event_count FROM internal_app_bsa_gjk.continuous_events
                WHERE
                    report_type=%s AND end_time BETWEEN  %s AND %s AND analysis_tech=%s AND event_type IN (12, 32, 42)
                GROUP BY event_type) AS t2 ON t2.event_type = t.type_value::integer
                ORDER BY t.type_value;
                """.format(alert_table_name=alert_table_name)
            res3 = json.loads(CFunction.execute(CPgSqlParam(_sql2, params=params[1:])))
            res.extend(res3)
        return res

    @staticmethod
    def dispose_data(_map, data):
        for i in data:
            if not i[0]:
                continue
            alert_name = i[4]
            alert_count_ = i[2] if i[2] else 0
            event_count_ = i[3] if i[3] else 0
            if alert_name in _map:
                _map[alert_name]["alert_count"] = alert_count_
                _map[alert_name]["event_count"] = event_count_
        return _map

    @staticmethod
    def get_all_detect_rule(belong):
        """
         获取detect_rule表中所有告警
         belong netflow为1,tclog为2
         """
        try:
            static_baseline_sql = '''
                SELECT
                    rule_id,
                    type,
                    detect_name,
                    threshold,
                    status
                FROM internal_app_bsa_gjk.detect_rule
                WHERE belong = %s AND type NOT IN (0,1,51)
                ORDER BY type ASC
            '''
            static_baseline_param = CPgSqlParam(static_baseline_sql, params=(belong,))
            static_baseline_res_list = json.loads(CFunction.execute(static_baseline_param))
            return static_baseline_res_list
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())
            return []

    @staticmethod
    def get_all_dynamic_detect_rule(belong):
        """
         获取dynamic_detect_rule表中所有告警
         belong netflow为1,tclog为2
         """
        try:
            _sql = '''
                SELECT
                    id,
                    type,
                    detect_name,
                    threshold,
                    status
                FROM internal_app_bsa_gjk.dynamic_detect_rule
                WHERE belong = %s
                ORDER BY type ASC
            '''
            _param = CPgSqlParam(_sql, params=(belong,))
            _res_list = json.loads(CFunction.execute(_param))
            return _res_list
        except Exception as e:
            mlog.error(e)
            mlog.error(traceback.format_exc())
            return []

    @staticmethod
    def handle_alert_data(data, _map, s_time, model_type):
        def create_alert_entry(item_arr, key, alert_name=None):
            """创建告警条目"""
            entry = {
                "data_source": _map.get(key, {}).get("data_source"),
                "data_category": _map.get(key, {}).get("data_category"),
                "alert_category": _map.get(key, {}).get("alert_category"),
                "alert_name": alert_name if alert_name else item_arr[2],
                "model_principle": _map.get(key, {}).get("model_principle"),
                "detect_type": _map.get(key, {}).get("detect_type"),
                "detect_cycle": _map.get(key, {}).get("detect_cycle"),
                "threshold": item_arr[3],
                "alert_count": 0,
                "event_count": 0,
                "enabled_status": "启用" if item_arr[4] else "停用",
                "type_value": _map.get(key, {}).get("type_value", item_arr[1]),
                "statistical_time": s_time,
                "model_type": model_type,
            }
            return entry

        # 定义特殊类型的映射
        special_types = {11: 12, 31: 32, 41: 42}
        res_map = defaultdict(dict)

        for item in data:
            # 生成 key
            key_ = "APT_{}".format(item[1]) if "APT" in item[2] and item[1] == 91 else item[1]

            # 添加主告警条目
            res_map[item[2]] = create_alert_entry(item, key_)

            # 处理特殊类型
            if item[1] in special_types:
                mapped_key = special_types[item[1]]
                alert_name_ = _map.get(mapped_key, {}).get("alert_name")
                res_map[alert_name_] = create_alert_entry(item, key_, alert_name_)

        # 按照 type_value 升序排序
        sorted_data = OrderedDict(sorted(res_map.items(), key=lambda i: i[1]['type_value']))
        return sorted_data

    @staticmethod
    def append_to_map(result_map, key1, key2, value):
        result_map.setdefault(key1, {}).setdefault(key2, []).append(value)

    @staticmethod
    def save_pg(result_map):
        EventAlertDailyStatsModel.objects.bulk_create(
            [EventAlertDailyStatsModel(**kwargs) for k, kwargs in result_map.items()]
        )

    def run_5min(self, start_timestamp, end_timestamp):
        tclog_model_principle, _, netflow_model_principle, __ = (
            self.get_alert_template_data())
        _res_list = self.get_all_detect_rule(1)
        netflow_map = self.handle_alert_data(_res_list, netflow_model_principle, start_timestamp, 2)
        tllog_res_list = self.get_all_detect_rule(2)
        tllog_map = self.handle_alert_data(tllog_res_list, tclog_model_principle, start_timestamp, 2)
        s = time.time()
        mlog.info("==========================通联日志==========================：")
        tllog_event_res = self.get_alert_and_event_count(1, start_timestamp, end_timestamp, 1, 2)
        self.dispose_data(tllog_map, tllog_event_res)

        mlog.info("==========================netflow==========================：")
        netflow_event_res = self.get_alert_and_event_count(2, start_timestamp, end_timestamp, 1, 1)
        self.dispose_data(netflow_map, netflow_event_res)
        mlog.info("======sql查询时间{}======".format(time.time() - s))
        # 写入数据库
        self.save_pg(tllog_map)
        self.save_pg(netflow_map)

        # print(json.dumps(tllog_map, ensure_ascii=False))

    def run_day(self, start_timestamp, end_timestamp):
        _, day_tclog_model_principle, __, day_netflow_model_principle = (
            self.get_alert_template_data())
        # netflow 天模型的
        day_res_list = self.get_all_dynamic_detect_rule(1)
        day_netflow_map = self.handle_alert_data(day_res_list, day_netflow_model_principle, start_timestamp, 1)

        # 通联 天模型的
        day_tllog_res_list = self.get_all_dynamic_detect_rule(2)
        day_tllog_map = self.handle_alert_data(day_tllog_res_list, day_tclog_model_principle, start_timestamp, 1)
        s = time.time()
        mlog.info("==========================通联日志天模型==========================：")
        day_tllog_event_res = self.get_alert_and_event_count(1, start_timestamp, end_timestamp, 2, 2)
        self.dispose_data(day_tllog_map, day_tllog_event_res)

        mlog.info("==========================netflow天模型==========================：")
        day_netflow_event_res = self.get_alert_and_event_count(2, start_timestamp, end_timestamp, 2, 1)
        self.dispose_data(day_netflow_map, day_netflow_event_res)
        mlog.info("======sql查询时间{}======".format(time.time() - s))
        # 写入数据库
        self.save_pg(day_tllog_map)
        self.save_pg(day_netflow_map)

        # print(json.dumps(tllog_map, ensure_ascii=False))


if __name__ == '__main__':
    """
    每天00:00:00跑一次
    0 0 0 * * ? python /home/<USER>/ISOP/apps/cncert_kj/script/event_alert_daily_stats.py
    # 指定时间范围
    python event_alert_daily_stats.py --start_time 1741708800 --end_time 1741795199
    """
    s = time.time()
    obj = EventAlertDailyStats()
    st, et = obj.get_time_zone()
    parser = argparse.ArgumentParser(usage="python event_alert_daily_stats.py", description="")
    parser.add_argument("--start_time", help="开始时间戳", default=st)
    parser.add_argument("--end_time", help="结束时间戳", default=et)
    parser.add_argument("--model_type", help="天模型或者5min,1是天模型，2是5min", default=2)
    args = parser.parse_args()
    mlog.info("参数:{}-{}-{}".format(args.start_time, args.end_time, args.model_type))
    if not all([args.start_time, args.end_time, args]):
        mlog.info("参数错误")
    else:
        obj.print_time_zone(int(args.start_time), int(args.end_time))
        model_type = int(args.model_type)
        if model_type == 1:
            obj.run_day(int(args.start_time), int(args.end_time))
        elif model_type == 2:
            obj.run_5min(int(args.start_time), int(args.end_time))
    mlog.info("脚本执行结束，共耗时：{}s".format(time.time() - s))
