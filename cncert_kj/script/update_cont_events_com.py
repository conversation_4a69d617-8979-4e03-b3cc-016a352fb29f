#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @file: update_cont_events_com_empty_supplement.py.py
# @time: 2025/4/8 11:19
"""补充事件备案单位"""
import argparse
import json
import os
import requests
import sys
import time
import traceback

from django.db import connection
from django.db import transaction

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction

from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import CLOUD_SERVICE
from cncert_kj.utils.params_validate import is_ipv4_address
from cncert_kj.utils.visit_record_unit import VisitAPI
from cncert_kj.utils.time_trans import timestamp2format
from cncert_kj.models.base_model import ContinuousEventsTagModel, ContinuousEventsConfModel, ContinuousEvents

reload(sys)
sys.setdefaultencoding('utf-8')
mlog = logger.init_logger('update_cont_events_com')


class UpdateContEventsCom:
    """
    将事件id存进配置表，每次查出大于该事件id的事件，取3000条进行补充
    """

    def __init__(self):
        self.token = VisitAPI().token
        self.api = VisitAPI().api
        self._session = requests.Session()
        self._session.headers.update({'Connection': 'keep-alive'})
        self.start_time = None
        self.end_time = None
        self.type = None
        self.conf_key = None
        self.conf_key2 = None
        self.event_type_name = ""

    def print_attribute(self):
        for k, v in vars(self).items():
            if k in ["start_time", "end_time"] and v:
                v = timestamp2format(v)
            mlog.info("当前类属性；{}: {}".format(k, v))

    def print_log(self, msg):
        mlog.info("{}:{}".format(self.event_type_name, msg))

    def init_params(self):
        if self.type == 1:
            # 通联
            self.conf_key = "update_tclog_cont_events_com_timestamp"
            self.conf_key2 = "update_tclog_cont_event_id"
            self.event_type_name = "通联日志"
        elif self.type == 2:
            # netflow
            self.conf_key = "update_cont_events_com_timestamp"
            self.conf_key2 = "update_cont_event_id"
            self.event_type_name = "netflow"
        else:
            self.conf_key = "全部"
            self.conf_key2 = "全部"
            self.event_type_name = "ALL"
        self.print_attribute()

    def query_asset_timestamp(self):
        queryset = ContinuousEventsConfModel.objects.filter(conf_key=self.conf_key).first()
        if not queryset:
            value = int(time.time()) - 60 * 60 * 24
        else:
            value = queryset.conf_value
        if not self.start_time and not self.end_time:
            self.print_log("开始时间:{}  - {}".format(value, timestamp2format(value)))
        return value

    def get_last_event_id(self):
        """查询上次处理的event_id"""
        queryset = ContinuousEventsConfModel.objects.filter(conf_key=self.conf_key2).first()
        if not queryset:
            event_id = 0
        else:
            event_id = queryset.conf_value
        if not self.start_time and not self.end_time:
            self.print_log("开始事件id为{}".format(event_id))
        return event_id

    def get_event_ids(self, last_end_time):
        # 获取时间区间内的事件id
        try:
            if self.start_time and self.end_time:
                queryset = ContinuousEvents.objects.filter(end_time__range=(self.start_time, self.end_time))
                event_ids = queryset.values_list("event_id", flat=True).distinct()
                return list(event_ids)

            if self.type == 3:
                queryset = ContinuousEvents.objects.filter(end_time__gte=last_end_time)
            else:
                queryset = ContinuousEvents.objects.filter(end_time__gt=last_end_time, report_type=self.type)
            event_ids = queryset.values_list("event_id", flat=True).distinct()
            return list(event_ids)
        except Exception as e:
            mlog.exception("查询sql失败：{}".format(e))
            return []

    def get_event_ids2(self, event_id):
        # 获取此次要处理的新增事件id
        try:
            if not event_id:
                return []
            event_ids = ContinuousEvents.objects.filter(event_id__gt=event_id, report_type=self.type).values_list(
                "event_id", flat=True).distinct()
            return list(event_ids)
        except Exception as e:
            mlog.exception("查询sql失败：{}".format(e))
            return []

    def get_event_data(self, event_ids):
        self.print_log("开始获取事件")
        try:
            events = ContinuousEvents.objects.filter(event_id__in=event_ids).order_by("event_id").all()
            self.print_log("获取事件结束")
            return events
        except Exception as e:
            mlog.exception("获取事件sql失败：{}".format(e))
            return []

    def get_event_data2(self, event_ids):
        self.print_log("开始获取事件")
        try:
            events = ContinuousEvents.objects.filter(event_id__in=event_ids, report_type=self.type).order_by(
                "event_id").all()
            self.print_log("获取事件结束")
            return events
        except Exception as e:
            mlog.exception("获取事件sql失败：{}".format(e))
            return []

    def get_ip_list(self, data):
        self.print_log("开始获取ip列表")
        ip_list = []
        for item in data:
            src_ip = item.src_ip
            dst_ip = item.dst_ip
            if is_ipv4_address(src_ip):
                if src_ip not in ip_list:
                    ip_list.append(src_ip)
            else:
                mlog.error("事件id：{}；src_ip:{}不是ipv4".format(item.event_id, src_ip))
            if is_ipv4_address(dst_ip):
                if dst_ip not in ip_list:
                    ip_list.append(dst_ip)
            else:
                mlog.error("事件id：{}；dst_ip:{}不是ipv4".format(item.event_id, dst_ip))
        self.print_log("获取ip列表结束")
        return ip_list

    def do_api(self, ip_):
        params_ = {
            "ipLocation": ip_,
            "token": self.token
        }
        try:
            data = self._session.get(self.api, params=params_)
            if data.status_code == 200:
                return data
            else:
                mlog.error("备案系统请求失败{}".format(data.content))
                return False
        except Exception:
            mlog.info("访问备案系统失败")
            mlog.info(traceback.format_exc())
            mlog.info("备案系统请求参数：{}".format(params_))
            return False

    def get_operator(self, operator_):
        if operator_:
            if u"电信" in operator_:
                operator_ = "电信"
            elif u"移动" in operator_:
                operator_ = "移动"
            elif u"联通" in operator_:
                operator_ = "联通"
            elif u"联合网络通信" in operator_:
                operator_ = "联通"
            else:
                operator_ = operator_.encode("utf-8")
        else:
            operator_ = ""
        return operator_

    def close(self):
        # 在应用退出或不再需要时，关闭 Session 释放资源
        self._session.close()

    def get_unit(self, ip_list):
        self.print_log("开始获取备案单位")
        data = {}
        num = 1
        for index, ip_ in enumerate(ip_list, start=1):
            if index % 100 == 0:
                self.print_log("第{}批，100个IP处理完毕".format(num))
                num += 1
            try:
                rep_data = self.do_api(ip_)
                if not rep_data:
                    continue
                else:
                    rep_data = rep_data.text
                rep_data = json.loads(rep_data)
                country = rep_data.get("Country", "")
                province = rep_data.get("Province", "")
                country = country if country != "NULL" else ""
                province = province if province != "NULL" else ""
                isp = rep_data.get("Isp", "").strip()

                data[ip_] = {
                    "isp": isp,
                    "user": rep_data.get("User", "").strip(),
                    "operator": self.get_operator(isp),
                    "country": country,
                    "province": province,
                    "region": " ".join([country, province])
                }
            except Exception as e:
                mlog.error("获取备案单位失败：{} , {}".format(ip_, e))
                mlog.error("获取备案单位失败：{}".format(traceback.format_exc()))
        self.close()
        mlog.info("获取备案单位结束")
        return data

    def get_result_data(self, data_list, max_end_time, last_event_id, events_unit_data):
        self.print_log("开始处理数据")
        cloud_service_list = []
        for item in data_list:
            event_id = item.event_id
            src_ip = item.src_ip
            dst_ip = item.dst_ip
            asset_ip_detail = events_unit_data.get(src_ip, {})
            peer_ip_detail = events_unit_data.get(dst_ip, {})
            src_com = asset_ip_detail.get("user", "")
            mlog.info("id:{}，src_ip:{}，src_com:{}".format(event_id, src_ip, src_com))

            dst_com = peer_ip_detail.get("user", "")
            mlog.info("id:{}，dst_ip:{}，dst_com:{}".format(event_id, dst_ip, dst_com))
            dst_isp = peer_ip_detail.get("isp", "")
            item.src_com = src_com
            item.dst_com = dst_com
            for cloud_service in CLOUD_SERVICE:
                if cloud_service in dst_isp:
                    mlog.info("id:{}，dst_ip:{}，Isp:{}，包含云服务:{}".format(event_id, dst_ip, dst_isp, cloud_service))
                    cloud_service_list.append({"event_id": event_id, "cloud_service": dst_isp})
                    break
            end_time = item.end_time
            if end_time > max_end_time:
                max_end_time = end_time
            if last_event_id < event_id:
                last_event_id = event_id
        self.print_log("处理数据结束")
        return data_list, max_end_time, last_event_id, cloud_service_list

    def update_event_id(self, event_id):
        ContinuousEventsConfModel.objects.filter(conf_key=self.conf_key2).update(conf_value=event_id)
        self.print_log("更新事件id结束- {}".format(event_id))

    def update_asset_timestamp(self, end_time):
        ContinuousEventsConfModel.objects.filter(conf_key=self.conf_key).update(conf_value=end_time)
        self.print_log("更新记录时间为{} - {}".format(end_time, timestamp2format(end_time)))

    def save_cloud_service(self, cloud_service_list):
        """
        保存云服务
        """
        if not cloud_service_list:
            return
        self.print_log("开始保存云服务")
        with transaction.atomic():
            # 先删除
            self.print_log("删除原有的云服务")
            event_ids = [i["event_id"] for i in cloud_service_list]
            ContinuousEventsTagModel.objects.filter(event_id__in=event_ids, tag_type=2,
                                                    tag_name="cloud_platform_IP").delete()
            self.print_log("删除完成")
            # 再新增
            self.print_log("新增云服务")
            create_list = []
            for i in cloud_service_list:
                create_list.append((i["event_id"], 2, "cloud_platform_IP", i["cloud_service"]))
            # 使用原生SQL批量插入
            cursor = connection.cursor()

            # 构建批量插入SQL
            sql = """
            INSERT INTO internal_app_bsa_gjk.continuous_events_tag 
            (event_id, tag_type, tag_name, tag_content) 
            VALUES (%s, %s, %s, %s)
            """

            # 分批执行，每批1000条
            batch_size = 1000
            for i in range(0, len(create_list), batch_size):
                batch = create_list[i:i + batch_size]
                cursor.executemany(sql, batch)

            mlog.info("成功插入新标签数据,数量:%d", len(create_list))

    def save_to_pg(self, data, step_num=1000):
        self.print_log("开始更新事件")

        if len(data) > step_num:
            self.print_log("更新数量：{}， 超过{}条，开始分批更新".format(len(data), step_num))
            n = 0
            for num in range(0, len(data), step_num):
                n += 1
                current_list = data[num: num + step_num]
                self.print_log("第{}次更新，数量：{}条".format(n, len(current_list)))
                self.execute_update_sql(current_list)
        else:
            self.execute_update_sql(data)
        self.print_log("更新事件结束，更新{}条".format(len(data)))

    def execute_update_sql(self, update_params_list, ):
        """
        更新归并的sql
        """

        update_sql = '''
            UPDATE internal_app_bsa_gjk.continuous_events 
            SET 
            src_com = CASE event_id {} END,
            dst_com = CASE event_id {} END
            WHERE event_id in %s
            '''
        event_id_list = []
        src_com_case = ""
        dst_com_case = ""
        for index, info in enumerate(update_params_list):
            event_id = info.event_id
            event_id_list.append(event_id)
            src_com_case += " WHEN '{}' THEN '{}' ".format(event_id, info.src_com)
            dst_com_case += " WHEN '{}' THEN '{}' ".format(event_id, info.dst_com)
        if event_id_list:
            update_sql = update_sql.format(src_com_case, dst_com_case)
            # mlog.info("更新的sql：{}".format(update_sql))
            update_event_param = CPgSqlParam(update_sql, params=(tuple(event_id_list),))
            CFunction.execute(update_event_param)

    def run(self, event_ids=None):
        """
        入口
        :return:
        """
        # 第一步，获取上次结束时间
        last_end_time = max_end_time = self.query_asset_timestamp()
        # 第三步，获取上次event_id
        event_id = self.get_last_event_id()
        if event_ids is None:
            # 第二步，获取大于上次结束时间的event_id
            update_event_ids = self.get_event_ids(last_end_time)

            # 第四步，获取本次要处理的新增的event_id
            add_event_ids = self.get_event_ids2(event_id)

            # 第五步，合并去重更新的和新增的事件id，然后获取本次要处理的数据
            if not update_event_ids and not add_event_ids:
                self.print_log("没有需要更新的数据")
                return

            event_ids = list(set(update_event_ids + add_event_ids))
            data = self.get_event_data(event_ids)
        else:
            data = self.get_event_data2(event_ids)

        self.print_log("本次处理的共有{}个event_id".format(len(data)))

        if not data:
            self.print_log("no data")
        else:
            # 第六步，获取ip列表
            ip_list = self.get_ip_list(data)
            # 第七步，查询备案单位
            events_unit_data = self.get_unit(ip_list)

            # 第八步，处理数据并获取结果
            data, max_end_time, event_id, cloud_service_list = self.get_result_data(data, max_end_time, event_id,
                                                                                    events_unit_data)
            # 第九步，更新数据
            self.save_cloud_service(cloud_service_list)
            self.save_to_pg(data, step_num=1000)

            # 第十步，记录本次运行的最大事件id和结束时间
            self.update_event_id(event_id)
            self.update_asset_timestamp(max_end_time)


if __name__ == '__main__':
    """
    1、查询通联
        python update_cont_events_com.py
    2、查询netflow
        python update_cont_events_com.py -t 2
    3、当前时间往前24小时的所有事件
        python update_cont_events_com.py -t 3
    4、指定时间范围更新所有类型事件
        python update_cont_events_com.py -s 1747652400 -e 1747745495 -t 3
    """
    try:
        mlog.info("=================开始任务=================")
        st = int(time.time())
        parser = argparse.ArgumentParser()
        parser.add_argument("--start_time", "-s", help="开始时间戳", type=int)
        parser.add_argument("--end_time", "-e", help="结束时间戳", type=int)
        parser.add_argument("--type", "-t", help="1通联日志，2netflow，3是所有, 默认通联，", type=int, default=1)
        args = parser.parse_args()
        mlog.info("参数:start_time：{}-end_time：{}-type：{}".format(args.start_time, args.end_time, args.type))
        obj = UpdateContEventsCom()
        obj.start_time = args.start_time
        obj.end_time = args.end_time
        obj.type = args.type
        obj.init_params()
        obj.run()
        mlog.info("=================结束任务:耗时：{}=================".format(int(time.time()) - st))

    except Exception as e:
        mlog.exception("备案单位补充失败：{}".format(e))
        mlog.error(traceback.format_exc())
