#!/bin/bash
# JSON 文件路径
CONFIG_FILE="/home/<USER>/ISOP/apps/cncert_kj/conf/db.json"
# 使用 Python 提取数据
get_value() {
  python -c "
import json
import sys

file_path = '$CONFIG_FILE'
key_path = '$1'

with open(file_path, 'r') as f:
    data = json.load(f)

keys = key_path.split('.')
value = data
for key in keys:
    value = value.get(key, None)
    if value is None:
        sys.exit(1)

print(value)
" 2>/dev/null
}

# 提取 数据库连接信息
DB_HOST=$(get_value "postgresql.host")
DB_PORT=$(get_value "postgresql.db_port")
DB_NAME=$(get_value "postgresql.db_database")
DB_USER=$(get_value "postgresql.db_username")
DB_PASSWORD=$(get_value "postgresql.db_password")

DIR_PATH="/home/<USER>/databasebackup"
# 检查目录是否存在
if [ -d "$DIR_PATH" ]; then
    echo "Directory $DIR_PATH already exists."
else
    echo "Directory $DIR_PATH does not exist. Creating now..."
    mkdir -p "$DIR_PATH"
    if [ $? -eq 0 ]; then
        echo "Directory $DIR_PATH created successfully."
    else
        echo "Failed to create directory $DIR_PATH." >&2
        exit 1
    fi
fi


export PGPASSWORD=$DB_PASSWORD
# 获取所有表名
TABLES=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'internal_app_bsa_gjk'")

# 遍历所有表并创建备份文件
for TABLE in $TABLES; do
  if [ "$TABLE" == "netflow_alert_5min" ] || [ "$TABLE" == "traffic_alert" ]; then
      echo "continue_table: $TABLE"
      continue  # 跳过当前循环迭代
  fi
  FILENAME="${TABLE}.sql"
  echo "Backing up table: $TABLE"
  pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t internal_app_bsa_gjk.$TABLE -f /home/<USER>/databasebackup/$FILENAME
done

# 将所有sql文件打成zip包
current_date=$(date +%Y-%m-%d)
zip /home/<USER>/databasebackup/database_backup$current_date.zip -j /home/<USER>/databasebackup/*.sql

# 删除多余sql文件
rm -f /home/<USER>/databasebackup/*.sql

# 删除3天内未操作的备份文件
find /home/<USER>/databasebackup -name "*.zip" -mtime +3 -exec rm {} \;
