#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
    新生成事件查询威胁情报信息存入事件详情表以及事件标签表
    每5分钟执行1次
    用于事件详情威胁情报标签字段查询展示
"""
import argparse
import datetime
import json
import time
import traceback
from collections import OrderedDict

from django.db.models import Q

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.base_model import EventDetailsModel, ContinuousEventsTagModel
from cncert_kj.models.continuous_events_tag_models import ContinuousEventsTag
from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import CommonConf
from cncert_kj.utils.net import is_ipv4_correct
from cncert_kj.utils.request_nti import NtiUtil
from cncert_kj.utils.time_trans import timestamp2format

mlog = logger.init_logger('event_threat_type_script')

THREAT_TYPE = "ioc"
SRC_TAG_TYPE = 1
DST_TAG_TYPE = 2

nti = NtiUtil()


def query_threat_nti_timestamp(conf_key):
    """查询最新处理的event_id"""
    sql = """
            SELECT conf_value FROM internal_app_bsa_gjk.continuous_events_conf WHERE conf_key='{}'
        """.format(conf_key)
    result = CFunction.execute(CPgSqlParam(sql))
    result = json.loads(result)
    if not result:
        # 如果没有该数据则进行初始化
        timestamp = int(time.time()) - 7200
        insert_sql = """
            INSERT INTO internal_app_bsa_gjk.continuous_events_conf (conf_key, conf_value) VALUES ('{}', {});
        """.format(conf_key, timestamp)
        CFunction.execute(CPgSqlParam(insert_sql))
        return timestamp
    value = result[0][0]
    mlog.info("开始时间: {}:{}  - {}".format(conf_key, value, timestamp2format(value)))
    return value


def update_ddos_timestamp(end_time, conf_key):
    if conf_key:
        conf_sql = '''
        UPDATE internal_app_bsa_gjk.continuous_events_conf 
        SET conf_value={end_time} WHERE conf_key='{conf_key}'
        '''.format(end_time=end_time, conf_key=conf_key)
        param = CPgSqlParam(conf_sql)
        CFunction.execute(param)
        mlog.info("{}_更新记录时间为{} - {}".format(conf_key, end_time, timestamp2format(end_time)))


def get_threat_type(ip_list):
    """
    批量查询情报信息
    """
    data = {}
    try:
        if not ip_list:
            mlog.info("ip列表为空")
            return data
        # 循环IP, 将IPV4和IPV6分别进行查询
        ipv4 = []
        ipv6 = []
        objects = []
        for i in ip_list:
            if is_ipv4_correct(i):
                ipv4.append(i)
            else:
                ipv6.append(i)
        if ipv4:
            res = nti.search_many(ipv4)
            result = json.loads(res)
            objects.extend(result.get("objects", []))

        if ipv6:
            res_ = nti.ipv6_search_many(ipv6)
            result_ = json.loads(res_)
            objects.extend(result_.get("objects", []))
        if objects:
            for obj in objects:
                observables = obj.get("observables", [{}])
                ip1 = observables[0].get("address", {}).get("value")
                ip2 = observables[0].get("value")
                ip_ = ip1 or ip2
                if not ip_:
                    continue
                res = {}
                if obj:
                    threat_tag = obj.get("threat_types", [])
                    res["threat_types"] = threat_tag
                data[ip_] = res
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())
    return data


def get_event_info(report_type, last_end_time, analysis_tech, start_time, end_time, event_type, event_ids):
    if event_ids is None:
        if analysis_tech == 2:
            msg = "动态基线"
            mlog.info("开始进行动态基线事件信息增强")
            event_info, ip_list, event_max_time = get_event(report_type, last_end_time, analysis_tech, start_time,
                                                            end_time,
                                                            event_type)
        else:
            msg = "静态基线:{}".format("通联" if report_type == 1 else "netflow")
            event_info, ip_list, event_max_time = get_event(report_type, last_end_time)
    else:

        msg = "常驻任务:{}".format("通联" if report_type == 1 else "netflow")
        event_info, ip_list, event_max_time = get_event2(report_type, event_ids)

    return msg, event_info, ip_list, event_max_time


def enhance_event_intelligence_info(report_type=None, conf_key=None, analysis_tech=None, start_time=None,
                                    end_time=None, event_type=None, event_ids=None):
    """
    检查新事件的情报信息
    :return:
    """
    msg = ""
    try:
        mlog.info("==========================开始情报信息增强任务{}============================".format(conf_key))
        mlog.info("查询脚本上次执行时间")
        last_end_time = max_end_time = None
        if conf_key:
            last_end_time = max_end_time = query_threat_nti_timestamp(conf_key)
        # 事件的最大结束事件，在没用查到三方系统信息时用于更新时间戳
        mlog.info("获取新生成事件信息")

        msg, event_info, ip_list, event_max_time = get_event_info(report_type, last_end_time, analysis_tech, start_time,
                                                                  end_time, event_type, event_ids)

        ip_list = list(set([i for i in ip_list if i]))
        mlog.info("{}ip数量:{}".format(msg, len(ip_list)))
        mlog.info("{}获取情报信息".format(msg))
        step = 100
        intelligence_info = {}
        begin_time = time.time()
        for i in range(0, len(ip_list), step):
            mlog.info("{} 查询情报信息中:{}".format(msg, i))
            intelligence_info.update(get_threat_type(ip_list[i:i + step]))
        nti.close_session()
        stop_time = time.time()
        mlog.info("{} 获取情报信息耗时:{}".format(msg, stop_time - begin_time))

        if ip_list and intelligence_info:
            max_end_time, objects_list, create_params, params_list = handle_event_info(
                event_info, intelligence_info, msg, max_end_time, conf_key
            )
            if create_params:
                # 分批批量插入， 没批1000条
                for i in range(0, len(create_params), 1000):
                    ContinuousEventsTag().bulk_create(create_params[i:i + 1000])
            insert_or_update_data(params_list)
            update_ddos_timestamp(max_end_time, conf_key)
            # mlog.info("{} 本次事件共 {} 条, 情报信息增强共{}条".format(msg, num, len(params_list)))
            if analysis_tech == 2:
                mlog.info("动态基线的事件增强不更新时间")
                return
        if event_info and event_max_time and analysis_tech != 2:
            # 有事件，但是没情报，改时间为事件的最大结束时间
            max_end_time = event_max_time
        update_ddos_timestamp(max_end_time, conf_key)

    except Exception as e:
        mlog.error("{} {}情报增强任务出错".format(msg, conf_key))
        mlog.error(e)
        mlog.error(traceback.format_exc())
    mlog.info(
        "=========================={} {}情报信息增强任务结束============================".format(msg, conf_key))


def handle_event_info(event_info, intelligence_info, msg, max_end_time, conf_key):
    params_list = []
    objects_list = []
    threat_type_dict = CommonConf().get_threat_type()
    create_params = []
    for event_id, value in event_info.items():
        # mlog.info("----------------正在处理第{}条数据，event_id是{}".format(num, event_id))
        src_ip = value[0]
        dst_ip = value[1]
        end_time = value[2]

        src_ip_intelligence_info = intelligence_info.get(src_ip, {})
        dst_ip_intelligence_info = intelligence_info.get(dst_ip, {})
        if not src_ip_intelligence_info and not dst_ip_intelligence_info:
            mlog.info(
                "{} 事件ID:{}, src_ip:{},dst_ip:{}  情报信息为空!".format(msg, event_id, src_ip, dst_ip))
            continue
        if end_time > max_end_time:
            max_end_time = end_time
        # mlog.info("end_time:{}".format(timestamp2format(end_time)))
        # 标签信息并入info字段， &来区分其他的信息， ‘,’区分标签
        s_threat_tag = [threat_type_dict.get(str(i)) for i in
                        src_ip_intelligence_info.get("threat_types", [])]
        d_threat_tag = [threat_type_dict.get(str(i)) for i in
                        dst_ip_intelligence_info.get("threat_types", [])]

        s_tags = ContinuousEventsTagModel.objects.filter(
            event_id=event_id, tag_type=SRC_TAG_TYPE, tag_name=THREAT_TYPE
        )
        objects_list.append(s_tags)
        for i in s_threat_tag:
            create_params.append((event_id, SRC_TAG_TYPE, THREAT_TYPE, i))

        d_tags = ContinuousEventsTagModel.objects.filter(
            event_id=event_id, tag_type=DST_TAG_TYPE, tag_name=THREAT_TYPE
        )
        objects_list.append(d_tags)

        for i in d_threat_tag:
            create_params.append((event_id, DST_TAG_TYPE, THREAT_TYPE, i))

        # 更新事件表  源目信息字段
        create_time = update_time = int(time.time())
        params_list.append(
            (event_id, json.dumps(src_ip_intelligence_info, ensure_ascii=False),
             json.dumps(dst_ip_intelligence_info, ensure_ascii=False),
             create_time, update_time))

        # mlog.info("{} 本条处理完成:{}".format(msg, event_id))
        if len(params_list) >= 1000:
            mlog.info("情报信息满一千条，更新")
            insert_or_update_data(params_list)
            params_list = []
            ContinuousEventsTag().bulk_create(create_params)
            create_params = []
            for i in objects_list:
                i.delete()
            objects_list = []
            update_ddos_timestamp(max_end_time, conf_key)
    return max_end_time, objects_list, create_params, params_list


def update_event(event_id, src_info, dst_info):
    conf_sql = '''
                    UPDATE internal_app_bsa_gjk.continuous_events
                    SET src_info=%s, dst_info=%s where event_id=%s
                    '''
    param = CPgSqlParam(conf_sql, params=(src_info, dst_info, event_id))
    CFunction.execute(param)
    mlog.info("--------------事件event_id为{}增强 威胁情报标签 信息成功".format(event_id))


def get_event(report_type=None, last_end_time=None, analysis_tech=None, start_time=None, end_time=None,
              event_type=None):
    sql = """
        SELECT event_id, src_ip, dst_ip, start_time, end_time, dst_info, src_info 
        FROM internal_app_bsa_gjk.continuous_events
        WHERE 
    """
    params = []
    max_end_time = 0
    if analysis_tech == 2:
        if not all([start_time, end_time]):
            # 没有传入开始结束时间， 则查询前一整天的
            today = datetime.date.today()
            start_time = int(time.mktime(today.timetuple())) - 86400
            end_time = int(time.mktime(today.timetuple()))

        sql += " analysis_tech = %s AND end_time > %s and end_time <= %s"
        params.extend([analysis_tech, start_time, end_time])
        if report_type:
            sql += " AND report_type = %s"
            params.append(report_type)
        if event_type:
            if len(event_type) == 1:
                event_type *= 2
            sql += " AND event_type IN %s"
            params.append(tuple(event_type))
    else:
        params.append(last_end_time)
        sql += " end_time > %s"
        if report_type:
            sql += " AND report_type = %s"
            params.append(report_type)
    sql += " ORDER BY end_time ASC"
    mlog.info("查询事件sql:{}".format(sql))
    mlog.info("查询事件params:{}".format(params))
    result = json.loads(CFunction.execute(CPgSqlParam(sql, params=tuple(params))))
    mlog.info("最新生成的事件数量:{}".format(len(result)))
    # 所有ip列表， 去重，方便查询情报
    event_ip_list = []
    event_info = OrderedDict()
    # 查询更新时间在最近1个月的事件详情， 存在情报信息的id
    search_time = int(time.time()) - 86400 * 30
    event_ids = set(EventDetailsModel.objects.filter(
        update_time__gt=search_time
    ).filter(
        Q(src_intelligence_info__isnull=False) | Q(dst_intelligence_info__isnull=False)
    ).values_list("event_id", flat=True).distinct())
    mlog.info("最近一个月增强威胁情报的数据量：{}".format(len(event_ids)))
    for item in result:
        # 如果事件id已存在则不增强
        event_id = item[0]
        if event_id in event_ids:
            continue
        src_ip = item[1]
        dst_ip = item[2]
        end_time = item[4]
        dst_info = item[5]
        src_info = item[6]
        event_ip_list.extend([src_ip, dst_ip])
        event_info[event_id] = [src_ip, dst_ip, end_time, dst_info, src_info]

        if max_end_time < end_time:
            max_end_time = end_time
    return event_info, event_ip_list, max_end_time


def get_event2(report_type, event_ids):
    sql = """
        SELECT event_id, src_ip, dst_ip, start_time, end_time, dst_info, src_info 
        FROM internal_app_bsa_gjk.continuous_events
        WHERE event_id IN %s   AND report_type = %s ORDER BY end_time ASC;
    """
    params = [tuple(event_ids), report_type]
    max_end_time = 0
    mlog.info("查询事件sql:{}".format(sql))
    mlog.info("查询事件params:{}".format(params))
    result = json.loads(CFunction.execute(CPgSqlParam(sql, params=tuple(params))))
    mlog.info("最新生成的事件数量:{}".format(len(result)))
    # 所有ip列表， 去重，方便查询情报
    event_ip_list = []
    event_info = OrderedDict()
    # 查询更新时间在最近1个月的事件详情， 存在情报信息的id
    search_time = int(time.time()) - 86400 * 30
    event_ids = set(EventDetailsModel.objects.filter(
        update_time__gt=search_time
    ).filter(
        Q(src_intelligence_info__isnull=False) | Q(dst_intelligence_info__isnull=False)
    ).values_list("event_id", flat=True).distinct())
    mlog.info("最近一个月增强威胁情报的数据量：{}".format(len(event_ids)))
    for item in result:
        # 如果事件id已存在则不增强
        event_id = item[0]
        if event_id in event_ids:
            continue
        src_ip = item[1]
        dst_ip = item[2]
        end_time = item[4]
        dst_info = item[5]
        src_info = item[6]
        event_ip_list.extend([src_ip, dst_ip])
        event_info[event_id] = [src_ip, dst_ip, end_time, dst_info, src_info]

        if max_end_time < end_time:
            max_end_time = end_time
    return event_info, event_ip_list, max_end_time


def insert_or_update_data(params):
    """
    数据已存在则更新， 不存在则插入
    """
    mlog.info("开始插入/更新事件详情, 共{}条".format(len(params)))
    step = 1000
    for i in range(0, len(params), step):
        mlog.info("--{}--".format(i))
        param = params[i: i + step]
        sql = """
        INSERT INTO internal_app_bsa_gjk.event_details 
        (event_id, src_intelligence_info, dst_intelligence_info, create_time, update_time)
        VALUES {format}
        ON CONFLICT (event_id) DO 
        UPDATE SET 
            src_intelligence_info = EXCLUDED.src_intelligence_info,
            dst_intelligence_info = EXCLUDED.dst_intelligence_info,
            update_time=EXCLUDED.update_time;
        """.format(format=",".join(["%s"] * len(param)))
        CFunction.execute(CPgSqlParam(sql, params=tuple(param)))
    mlog.info("插入成功事件详情, 共{}条".format(len(params)))


if __name__ == '__main__':
    netflow_conf_key = "threat_nti_timestamp"
    tclog_conf_key = "tclog_threat_nti_timestamp"
    try:
        parser = argparse.ArgumentParser(description='威胁情报增强脚本')
        parser.add_argument('--report_type', type=int, default=1, help='通联/Netflow')
        args = parser.parse_args()
        report_type = args.report_type
        if report_type == 1:
            conf_key = tclog_conf_key
        else:
            conf_key = netflow_conf_key
        enhance_event_intelligence_info(report_type=report_type, conf_key=conf_key)
    except Exception as e:
        mlog.error("运行失败")
        mlog.error(traceback.format_exc())
