{"name": "跨境异常流量分析系统", "key": "cncert_kj", "app_name": "cncert_kj", "prefix": "cncert_kj", "allfix": ["^/cncert_kj"], "fix": ["^/cncert_kj"], "pinyin": "cncert_kj", "link": "/WebApi/cncert_kj/static/#/operator/tllog-flow-detection", "children": [{"fix": [], "name": "通联日志异常告警", "key": "tllog-flow", "link": "/WebApi/cncert_kj/static/#/operator/tllog-flow-detection", "children": [{"fix": ["^/cncert_kj/v2/tllog_flow/overview"], "name": "通联日志流量统计", "key": "tclog_flow_detection", "link": "/WebApi/cncert_kj/static/#/operator/tllog-flow-detection", "children": []}, {"fix": ["^/cncert_kj/v2/continuous_events", "^/cncert_kj/v2/tllog_alert/overview"], "name": "通联日志异常综述", "key": "tclog_abnormal_detection", "link": "/WebApi/cncert_kj/static/#/operator/tllog-abnormal-detection", "children": []}, {"fix": ["^/cncert_kj/v2/field_conf", "^/cncert_kj/v2/type", "^/cncert_kj/v2/blacklists", "^/cncert_kj/v2/blacklist", "^/cncert_kj/v2/whitelists", "^/cncert_kj/v2/whitelist", "^/cncert_kj/v2/custom_tags", "^/cncert_kj/v2/tllog_alert", "^/cncert_kj/v2/trend_chart_view/trend_chart", "^/cncert_kj/v2/ip_category"], "name": "通联日志异常列表", "key": "tclog_abnormal_detail", "link": "/WebApi/cncert_kj/static/#/operator/tllog-abnormal-detail", "children": []}]}, {"fix": [], "name": "NetFlow异常告警", "key": "netflow-flow", "link": "/WebApi/cncert_kj/static/#/operator/netflow-flow-detection", "children": [{"fix": ["^/cncert_kj/v2/netflow_flow/overview"], "name": "NetFlow流量统计", "key": "netflow_flow_detection", "link": "/WebApi/cncert_kj/static/#/operator/netflow-flow-detection", "children": []}, {"fix": ["^/cncert_kj/v2/continuous_events", "^/cncert_kj/v2/tllog_alert/overview"], "name": "NetFlow异常综述", "key": "netflow_abnormal_detection", "link": "/WebApi/cncert_kj/static/#/operator/netflow-abnormal-detection", "children": []}, {"fix": ["^/cncert_kj/v2/field_conf", "^/cncert_kj/v2/type", "^/cncert_kj/v2/blacklists", "^/cncert_kj/v2/blacklist", "^/cncert_kj/v2/whitelists", "^/cncert_kj/v2/whitelist", "^/cncert_kj/v2/custom_tags", "^/cncert_kj/v2/netflow_alert", "^/cncert_kj/v2/trend_chart_view/trend_chart", "^/cncert_kj/v2/ip_category"], "name": "NetFlow异常列表", "key": "netflow_abnormal_detail", "link": "/WebApi/cncert_kj/static/#/operator/netflow-abnormal-detail", "children": []}]}, {"fix": [], "name": "持续性告警", "key": "persistent-events", "link": "/WebApi/cncert_kj/static/#/operator/persistent-event-list", "children": [{"fix": ["^/cncert_kj/v2/continuous_events", "^/cncert_kj/v2/event_alert_daily_stats", "^/cncert_kj/v2/ip_one_to_more_tag/"], "name": "持续性告警概览", "key": "persistent-event-overview", "link": "/WebApi/cncert_kj/static/#/operator/persistent-event-overview", "children": []}, {"fix": ["^/cncert_kj/v2/get_department_type", "^/cncert_kj/v2/type", "^/cncert_kj/v2/get_judge_status_map", "^/cncert_kj/v2/field_conf", "^/cncert_kj/v2/blacklists", "^/cncert_kj/v2/whitelists", "^/cncert_kj/v2/custom_tags", "^/cncert_kj/v2/continuous_events", "^/cncert_kj/v2/ip_category", "^/cncert_kj/v2/blacklist", "^/cncert_kj/v2/whitelist", "^/cncert_kj/v2/trend_chart_view/trend_chart", "^/cncert_kj/v2/event/push_issue", "^/cncert_kj/v2/event/ports", "^/cncert_kj/v2/event_recommend/download_file", "^/cncert_kj/v2/whitelist_events/create_event", "^/cncert_kj/v2/get_tag_name_map"], "name": "持续性告警列表", "key": "persistent-event-list", "link": "/WebApi/cncert_kj/static/#/operator/persistent-event-list", "children": []}, {"fix": ["^/cncert_kj/v2/retrieval_task/"], "name": "持续性告警取证", "key": "metadatapcap", "link": "/WebApi/cncert_kj/static/#/operator/metadatapcap", "children": []}, {"fix": ["^/cncert_kj/v2/exception_event/", "^/cncert_kj/v2/exception_event_conf/"], "name": "异常场景分析", "key": "event-summary", "link": "/WebApi/cncert_kj/static/#/operator/event-summary", "children": []}, {"fix": [], "name": "事件管理", "key": "incident-manager", "link": "/WebApi/cncert_kj/static/#/operator/reported-incident", "children": [{"fix": ["^/cncert_kj/v2/reported_events"], "name": "已通报事件管理", "key": "reported-incident", "link": "/WebApi/cncert_kj/static/#/operator/reported-incident", "children": []}, {"fix": ["^/cncert_kj/v2/whitelist_events"], "name": "白名单事件管理", "key": "whitelist-incident", "link": "/WebApi/cncert_kj/static/#/operator/whitelist-incident", "children": []}, {"fix": ["^/cncert_kj/v2/greylist_events"], "name": "灰名单事件管理", "key": "greylist-incident", "link": "/WebApi/cncert_kj/static/#/operator/greylist-incident", "children": []}]}]}, {"fix": [], "name": "流量排名", "key": "seize-seat", "link": "/WebApi/cncert_kj/static/#/operator/seize-seat-detection", "children": [{"fix": ["^/cncert_kj/v2/top_1w/", "^/cncert_kj/v2/tllog_key_unit/details/", "^/cncert_kj/v2/trend_chart_view/trend_chart", "^/cncert_kj/v2/tllog_key_unit/detail/ip_pair_flow_monitor"], "name": "通联日志排名", "key": "seize-seat-detection", "link": "/WebApi/cncert_kj/static/#/operator/seize-seat-detection", "children": []}, {"fix": ["^/cncert_kj/v2/top_1w/", "^/cncert_kj/v2/netflow_key_unit/", "^/cncert_kj/v2/trend_chart_view/trend_chart"], "name": "NetFlow排名", "key": "seize-seat-detail", "link": "/WebApi/cncert_kj/static/#/operator/seize-seat-detail", "children": []}]}, {"fix": [], "name": "通联日志重点监控", "key": "tllog-important-unit", "link": "/WebApi/cncert_kj/static/#/operator/tllog-important-unit-overview", "children": [{"fix": ["^/cncert_kj/v2/tllog_key_unit/overview/"], "name": "重点监控概览", "key": "tllog-important-unit-overview", "link": "/WebApi/cncert_kj/static/#/operator/tllog-important-unit-overview", "children": []}, {"fix": ["^/cncert_kj/v2/tllog_key_unit/detail/", "^/cncert_kj/v2/tllog_key_unit/details/", "^/cncert_kj/v2/tllog_key_unit/key_units", "^/cncert_kj/v2/tllog_key_unit/industry", "^/cncert_kj/v2/tllog_key_unit/n_flow_statistics", "^/cncert_kj/v2/tllog_key_unit/now_flow_statistics", "^/cncert_kj/v2/tllog_key_unit/overview/update_address_type"], "name": "重点监控详情", "key": "tllog-important-unit-detail", "link": "/WebApi/cncert_kj/static/#/operator/tllog-important-unit-detail", "children": []}, {"fix": ["^/cncert_kj/v2/tllog_key_unit/details/", "^/cncert_kj/v2/ip_pair_flow_statistics_export", "^/cncert_kj/v2/netflow_flow_filter_view", "^/cncert_kj/v2/traffic_trend_chart"], "name": "通联流量查询", "key": "tllog-traffic-inquiry", "link": "/WebApi/cncert_kj/static/#/operator/tllog-traffic-inquiry", "children": []}]}, {"fix": [], "name": "NetFlow重点监控", "key": "netflow-important-unit", "link": "/WebApi/cncert_kj/static/#/operator/netflow-important-unit-overview", "children": [{"fix": ["^/cncert_kj/v2/tllog_key_unit/overview/execute_refresh_cache", "^/cncert_kj/v2/netflow_key_unit/overview/"], "name": "重点监控概览", "key": "netflow-important-unit-overview", "link": "/WebApi/cncert_kj/static/#/operator/netflow-important-unit-overview", "children": []}, {"fix": ["^/cncert_kj/v2/netflow_key_unit"], "name": "重点监控详情", "key": "netflow-important-unit-detail", "link": "/WebApi/cncert_kj/static/#/operator/netflow-important-unit-detail", "children": []}, {"fix": ["^/cncert_kj/v2/netflow_flow_filter_view", "^/cncert_kj/v2/netflow_key_unit", "^/cncert_kj/v2/ip_pair_flow_statistics_export", "^/cncert_kj/v2/traffic_trend_chart"], "name": "NetFlow流量查询", "key": "netflow-traffic-inquiry", "link": "/WebApi/cncert_kj/static/#/operator/netflow-traffic-inquiry", "children": []}]}, {"fix": [], "name": "系统设置", "key": "system-settings", "link": "/WebApi/cncert_kj/static/#/operator/white-list", "children": [{"fix": ["^/cncert_kj/v2/whitelists", "^/cncert_kj/v2/whitelist"], "name": "白名单管理", "key": "white-list", "link": "/WebApi/cncert_kj/static/#/operator/white-list", "children": []}, {"fix": ["^/cncert_kj/v2/blacklists", "^/cncert_kj/v2/blacklist", "^/cncert_kj/v2/ip_category"], "name": "黑名单管理", "key": "black-list", "link": "/WebApi/cncert_kj/static/#/operator/black-list", "children": []}, {"fix": ["^/cncert_kj/v2/custom_tags", "^/cncert_kj/v2/custom_tag"], "name": "标签管理", "key": "custom-tag", "link": "/WebApi/cncert_kj/static/#/operator/custom-tag", "children": []}, {"fix": ["^/cncert_kj/v2/tllog_key_unit/department_key_unit", "^/cncert_kj/v2/tllog_key_unit/departments", "^/cncert_kj/v2/tllog_key_unit/department_statistics", "^/cncert_kj/v2/tllog_key_unit/industry", "^/cncert_kj/v2/tllog_key_unit/xls/_export", "^/cncert_kj/v2/key_unit/batch_data_check", "^/cncert_kj/v2/units_manager", "^/cncert_kj/v2/units_configuration", "^/cncert_kj/v2/tllog_industry"], "name": "单位设置", "key": "tllog-important-unit-manager", "link": "/WebApi/cncert_kj/static/#/operator/tllog-important-unit-manager", "children": []}, {"fix": ["^/cncert_kj/v2/static_baseline_config", "^/cncert_kj/v2/static_baseline_config/distribute_config", "^/cncert_kj/v2/dynamic_baseline"], "name": "静态基线阈值配置", "key": "static-baseline-threshold", "link": "/WebApi/cncert_kj/static/#/operator/static-baseline-threshold", "children": []}, {"fix": ["^/cncert_kj/v2/operation_intel/"], "name": "运营情报IP配置", "key": "intelligence", "link": "/WebApi/cncert_kj/static/#/operator/intelligence", "children": []}, {"fix": ["^/cncert_kj/v2/alert_rule", "^/cncert_kj/v2/top_1w/agg_avg_flow", "^/cncert_kj/v2/top_1w/start_baseline_task"], "name": "动态基线规则配置", "key": "rule-configuration", "link": "/WebApi/cncert_kj/static/#/operator/rule-configuration", "children": []}, {"fix": ["^/cncert_kj/v2/asset_manage"], "name": "资产管理", "key": "asset-manage", "link": "/WebApi/cncert_kj/static/#/operator/asset-manage", "children": []}, {"fix": ["^/cncert_kj/v2/sso_login"], "name": "108出网流量分析系统", "key": "cncert_1xx", "link": "cncert_1xx", "children": []}, {"fix": ["^/cncert_kj/v2/sso_login"], "name": "中心出网流量分析系统", "key": "cncert_zx", "link": "cncert_zx", "children": []}]}]}