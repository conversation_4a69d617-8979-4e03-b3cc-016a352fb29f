# -*- coding: utf-8 -*-
import sys
import os

from django.apps.registry import apps  # django的实例application
from django.core.exceptions import AppRegistryNotReady

app_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(app_root, 'lib'))
reload(sys)

#  ENVIRONMENT_VARIABLE = "DJANGO_SETTINGS_MODULE"
# settings_module = os.environ.get(ENVIRONMENT_VARIABLE)
# if not settings_module:
#     os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ISOP.settings")

if not os.environ.get('DJANGO_SETTINGS_MODULE'):
    import django
    sys.path.append("/home/<USER>/ISOP")
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ISOP.settings")
    django.setup()

try:
    apps.check_apps_ready()
except AppRegistryNotReady as e:
    pass
