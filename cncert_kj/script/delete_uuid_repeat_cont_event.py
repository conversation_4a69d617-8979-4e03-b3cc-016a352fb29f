#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    删除持续性事件uuid重复的数据，只保留最新的一个
"""
from django.db import transaction
from django.db.models import Count, Q

from cncert_kj.models.base_model import ContinuousEvents, NetflowAlert5MinModel, TrafficAlertModel


class DeleteDuplicate(object):

    def delete_event(self, delete_list):
        """删除事件"""
        if delete_list:
            ContinuousEvents.objects.filter(event_id__in=delete_list).delete()
            print "删除完成:{}".format(len(delete_list))

    def update_event(self, update_id_list):
        """
        更新事件:流量，告警次数，关联uuid，，注意取反标识
        """
        if update_id_list:
            event_obj_list = ContinuousEvents.objects.filter(event_id__in=update_id_list)
            for event_obj in event_obj_list:
                report_type = event_obj.report_type
                related_alerts = event_obj.related_alerts
                reverse_tag = event_obj.reverse_tag

                uuid_list = related_alerts.split(';')

                new_uuid_list = []
                up_bytesall = 0
                down_bytesall = 0
                if report_type == 1:
                    res_obj_list = TrafficAlertModel.objects.filter(uuid__in=uuid_list).values('uuid', 'up_bytes_all', 'down_bytes_all')
                    for res_obj in res_obj_list:
                        uuid = res_obj.get('uuid')
                        new_uuid_list.append(uuid)
                        up_bytes_all = res_obj.get('up_bytes_all')
                        if up_bytes_all:
                            up_bytesall += up_bytes_all
                        down_bytes_all = res_obj.get('down_bytes_all')
                        if down_bytes_all:
                            down_bytesall += down_bytes_all
                elif report_type == 2:
                    res_obj_list = NetflowAlert5MinModel.objects.filter(uuid__in=uuid_list).values('uuid', 'bytes_all', 'bytes_all_down')
                    for res_obj in res_obj_list:
                        uuid = res_obj.get('uuid')
                        new_uuid_list.append(uuid)
                        bytes_all = res_obj.get('bytes_all')
                        if bytes_all:
                            up_bytesall += bytes_all
                        bytes_all_down = res_obj.get('bytes_all_down')
                        if bytes_all_down:
                            down_bytesall += bytes_all_down

                if reverse_tag == 1:
                    up_bytesall, down_bytesall = down_bytesall, up_bytesall

                related_alerts_count = len(new_uuid_list)

                event_obj.related_alerts = ";".join(new_uuid_list)
                event_obj.related_alerts_count = related_alerts_count
                event_obj.up_bytesall = up_bytesall
                event_obj.down_bytesall = down_bytesall
                event_obj.save()

    def run(self, *args, **kwargs):
        print "---开始删除uuid重复的持续性事件脚本---"
        # 获取所有uuid重复的持续性事件
        dup_events = ContinuousEvents.objects.exclude(Q(uuid=None) | Q(uuid='')).filter(judge_status=1).values(
            'uuid').annotate(Count=Count('uuid')).filter(Count__gt=1)
        print "获取到{}个uuid重复的持续性事件".format(len(dup_events))

        # 将要删除的持续性事件id列表
        delete_list = []
        # 要更新的id列表
        update_id_list = []
        n = 0
        for event in dup_events:
            n += 1
            uuid = event['uuid']

            if not uuid:
                continue

            events = ContinuousEvents.objects.filter(uuid=uuid, judge_status=1).values('event_id', 'end_time').order_by('-end_time', '-event_id')

            if len(events) <= 1:
                continue
            # 只保留最新的一个
            print_id_list = []
            for i in range(len(events)):
                if i == 0:
                    update_id_list.append(int(events[i].get('event_id')))
                    continue
                else:
                    event_id = int(events[i].get('event_id'))
                    delete_list.append(event_id)
                    print_id_list.append(event_id)

            print "--开始处理第{}条记录; uuid:{}, 删除以下事件：{}".format(n, uuid, print_id_list)

        print "删除以下事件：{}".format(delete_list)
        print "更新以下事件：{}".format(update_id_list)
        print "删除事件数量：{}".format(len(delete_list))
        print "更新事件数量：{}".format(len(update_id_list))

        with transaction.atomic():
            # 更新事件
            self.update_event(update_id_list)
            # 删除
            self.delete_event(delete_list)

            pass


if __name__ == '__main__':
    obj = DeleteDuplicate()
    obj.run()
