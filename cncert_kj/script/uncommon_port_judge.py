#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    非常用端口事件研判脚本
    每5分钟运行一次
    根本事件的服务端ip+端口查询资产，然后将资产信息写入标签表，去重，最多写入三条
"""
import datetime
import json
import sys
import time
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()

from collections import defaultdict

from cncert_kj.lib.gjkDataInterface.db.params import CPgSqlParam
from cncert_kj.lib.gjkDataInterface.functions import CFunction
from cncert_kj.models.continuous_events_tag_models import ContinuousEventsTag
from cncert_kj.utils.request_asset import RequestAsset
from cncert_kj.models.base_model import ContinuousEventsTagModel, ContinuousEvents, TrafficAlertModel, \
    NetflowAlert5MinModel
from cncert_kj.models.cont_event_models import get_port_traffic
from cncert_kj.script.cont_event_judge import EventJudgeUtil
from cncert_kj.utils import logger, lock_util

mlog = logger.init_logger('uncommon_port_judge')

UNCOMMON_PORT_CONSTANT = "非常用端口"
CURLY_BRACES_3 = "{},{},{}"

class UncommonPortJudge(EventJudgeUtil):
    def __init__(self, event_type_name=UNCOMMON_PORT_CONSTANT, record_key="", analysis_tech=1):
        super(UncommonPortJudge, self).__init__(event_type_name, record_key)
        self.port_list = []
        self.types = [91, 92]
        self.tag_count = 3  # 非常用端口服务标签数量上限
        self.analysis_tech = analysis_tech
        self.request_asset_obj = RequestAsset()

    def get_yesterday_time(self):
        """获取昨天的0点"""
        today = datetime.datetime.now()
        yesterday = today - datetime.timedelta(days=1)

        today = today.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        return int(time.mktime(yesterday.timetuple())), int(time.mktime(today.timetuple()))

    def filter_events(self, st, et, report_type):
        """查询非常用端口事件"""
        # >>
        # filter_sql = """
        # select event_id, src_ip, dst_ip, event_type, end_time, related_alerts, report_type
        # from internal_app_bsa_gjk.continuous_events
        # where event_type in %s and end_time > %s and end_time <= %s and report_type = %s
        # order by end_time
        # """
        # mlog.info("filter_cont_event_sql:{}".format(filter_sql))
        # filter_res = json.loads(
        #     CFunction.execute(CPgSqlParam(filter_sql, params=(tuple(self.types), st, et, report_type))))
        filter_res = ContinuousEvents.objects.filter(end_time__range=(st, et),
                                                     report_type=report_type,
                                                     event_type__in=self.types,
                                                     analysis_tech=self.analysis_tech)
        # .values_list("event_id", "src_ip",
        #                                                                                 "dst_ip",
        #                                                                                 "event_type", "end_time",
        #                                                                                 "related_alerts",
        #                                                                                 "report_type")
        return filter_res

    def filter_events2(self, event_ids, report_type):
        """查询非常用端口事件"""

        filter_res = ContinuousEvents.objects.filter(
            event_id__in=event_ids, report_type=report_type, event_type__in=self.types)

        return filter_res

    def filter_port_distribution(self, event_id):
        """查询事件标签"""
        res = ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_name="port_distribution")
        if res.exists():
            return res.first().tag_content
        return ""

    def get_port_list_(self, report_type, related_alerts):
        """下钻告警查询端口"""

        if report_type == 1:
            res = TrafficAlertModel.objects.filter(uuid__in=related_alerts).values_list("dport").distinct()
        else:
            res = NetflowAlert5MinModel.objects.filter(uuid__in=related_alerts).values_list("dport").distinct()
        if res:
            return [i[0] for i in res if i[0] and i[0] != -1]
        return []

    def search_asset_url(self, ip_, port_list=[]):
        """
            查询资产系统中的url
        """
        #>> assets = []
        data_list = []

        #>> asset_res = self.aj.do_asset_url(ip_, port_list)
        asset_res = self.request_asset_obj.query_asset([ip_], port_list)
        if not asset_res:
            return []
        asset_list = asset_res.get(ip_, [])
        for asset in asset_list:
            if not isinstance(asset, dict):
                continue
            protocol = asset.get("protocol", "")  # 是字符串
            product = asset.get("product", [])  # 列表
            port = asset.get("port", 0)  # 端口

            data_dic = {}

            if protocol and protocol != "null" and protocol != u"unknown" and protocol != "unknown":
                #>> protocol_list.append(protocol)
                data_dic["protocol"] = protocol
            if product:
                #>> product_list.extend(product)
                data_dic["product"] = product
            if port:
                data_dic["port"] = port

            data_list.append(data_dic)
        return data_list

        # >>
        # if asset_res:
        #     # assets = asset_res.get("data", {}).get("assets", [])
        #     if isinstance(asset_res, dict):
        #         data_dic = asset_res.get("data", {})
        #         if isinstance(data_dic, dict):
        #             assets = data_dic.get("assets", [])
        # protocol_list = []
        # product_list = []
        #
        #
        # if assets:
        #     for asset in assets:
        #         protocol = asset.get("protocol", "")  # 是字符串
        #         product = asset.get("product", [])  # 列表
        #         port = asset.get("port", 0)  # 端口
        #
        #         data_dic = {}
        #
        #         if protocol and protocol != "null" and protocol != u"unknown" and protocol != "unknown":
        #             # protocol_list.append(protocol)
        #             data_dic["protocol"] = protocol
        #         if product:
        #             # product_list.extend(product)
        #             data_dic["product"] = product
        #         if port:
        #             data_dic["port"] = port
        #
        #         data_list.append(data_dic)
        #
        # else:
        #     pass
        # return data_list

    def get_event_obj_list(self, st, et, report_type, event_ids):
        if event_ids is None:
            event_obj_list = self.filter_events(st, et, report_type)
        else:
            event_obj_list = self.filter_events2(event_ids, report_type)
        return event_obj_list

    def run(self, report_type, event_ids=None):
        """
            二次研判探测任务下发未返回数据的事件
            相同端口的服务信息只保留一条最新的; 插入之前先将相同端口的其他应用类型信息删除，只保留最新的
        """
        t1 = datetime.datetime.now()
        mlog.info("========开始{}类型事件增强应用类型：{}".format(self.event_type_name, t1))

        # 获取上次事件研判时间
        if self.analysis_tech == 2:
            st, et = self.get_yesterday_time()
            if report_type == 1:
                report_str = "tclog"
            else:
                report_str = "netflow"
            mlog.info("按天-非常用端口类型-{}-事件增强应用类型，时间范围：{}~{}".format(report_str, st, et))
        else:
            st = self.aj.get_conf_value(self.record_key)
            et = int(time.time())
            if report_type == 1:
                report_str = "tclog"
            else:
                report_str = "netflow"
            mlog.info("5min-非常用端口类型-{}-事件增强应用类型，时间范围：{}~{}".format(report_str, st, et))

        # 查询持续性事件
        event_obj_list = self.get_event_obj_list(st, et, report_type, event_ids)
        mlog.info("{}-持续性事件数量：{}".format(report_str, len(event_obj_list)))

        record_id = 0
        record_end_time = st
        try:
            if len(event_obj_list) == 0:
                t = datetime.datetime.now()
                mlog.info("=======无事件，研判结束：{}秒".format((t - t1).total_seconds()))
                return
            # 这里做成批量查询事件应用类型标签速度应该会更快些
            event_ids = [i.event_id for i in event_obj_list]
            #>> tag_res = self.cet_obj.filter(event_id=event_ids, tag_type=0, tag_name="app_type")
            tag_res = ContinuousEventsTagModel.objects.filter(event_id__in=event_ids, tag_type=10,
                                                              tag_name="app_type")
            # 查询所有的应用类型，map {event_id：[app_type]}
            tag_data = defaultdict(list)
            for tag_item in tag_res:
                event_id = tag_item.event_id
                old_tag = tag_item.tag_content
                tag_data[event_id].append(old_tag)
                #>> tags = tag_data.get(event_id, [])
                # tag_data[event_id] = tags.append(old_tag)

            for event_obj in event_obj_list:
                if event_obj:
                    event_id = event_obj.event_id
                    record_id = event_id
                    # mlog.info("当前事件id:{}".format(event_id))
                    event_type = event_obj.event_type
                    end_time = event_obj.end_time
                    src_ip = event_obj.src_ip
                    dst_ip = event_obj.dst_ip
                    related_alerts = event_obj.related_alerts.split(";")
                    report_type = event_obj.report_type

                    if end_time > record_end_time:
                        record_end_time = end_time

                    # 区分主被动类型
                    if event_type % 2 == 0:  # 下载
                        ip_ = src_ip
                        up_or_down_tag = 2  # 下载
                    else:
                        ip_ = dst_ip
                        up_or_down_tag = 1  # 上传

                    try:
                        # 下钻获取端口
                        port_list = self.get_port_list_(report_type, related_alerts)
                        mlog.info("----id:{},ip:{},端口结果：{}".format(event_id, ip_, port_list))
                        # 查询资产，获取服务信息，服务信息存在于protocol
                        data_list = self.search_asset_url(ip_, port_list)
                        old_app_type_list = tag_data.get(event_id, [])

                        is_asset = False
                        if data_list:
                            mlog.info("----有资产，通过资产增强".format())
                            app_data_list = []
                            ports = []
                            for data_dic in data_list:
                                protocol = data_dic.get("protocol")
                                product = data_dic.get("product")
                                port = data_dic.get("port")
                                if port and int(port) in port_list:
                                    if all([product, protocol, port]):
                                        product = product[0]
                                        # mlog.info("----id:{},ip:{},端口：{},协议：{}".format(event_id, ip_, port, protocol))
                                        app_data = CURLY_BRACES_3.format(product, protocol, port)
                                        ports.append(port)
                                    elif product and port:
                                        product = product[0]
                                        try:
                                            data = get_port_traffic(alert_ids=related_alerts, report_type=report_type)
                                            protocol = data.get("protocol").split(",")[0] if data.get(
                                                "protocol") else ""
                                        except Exception as e:
                                            mlog.error("--id:{},ip:{},获取协议失败，原因：{}".format(event_id, ip_, e))
                                            continue
                                        app_data = CURLY_BRACES_3.format(product, protocol, port)
                                        ports.append(port)
                                    elif protocol and port:
                                        app_data = CURLY_BRACES_3.format(protocol, protocol, port)
                                        ports.append(port)
                                    else:
                                        app_data = ""

                                    if app_data:
                                        is_asset = True  # 资产查询到了，但是有重复，标记未true
                                        # 该端口已查询到资产，则将该端口从列表中删除，不在新增该端口的应用类型
                                        port_list.remove(int(port))
                                        if app_data not in old_app_type_list:
                                            mlog.info(
                                                "--id:{},ip:{},端口：{},应用类型增强：{}".format(event_id, ip_, port,
                                                                                               app_data))
                                            # app_data_list.append((event_id, 0, "app_type", app_data))
                                            old_app_type_list = [i for i in old_app_type_list if
                                                                 not i.endswith(",{}".format(port))]
                                            app_data_list.append((event_id, 10, "app_type", app_data))
                                            # >>
                                            # app_data_list.append(ContinuousEventsTagModel(
                                            #     event_id=event_id,
                                            #     tag_type=10,  # 非常用端口事件脚本增强的应用类型使用10，与常用端口区分
                                            #     tag_name="app_type",
                                            #     tag_content=app_data
                                            # ))
                                            # self.cet_obj.bulk_create([(event_id, 0, "app_type", app_data)])
                                            # continue
                                else:
                                    mlog.info("--其他端口信息：{}".format(port))

                            # 将旧的合并进去
                            for old_app_type in old_app_type_list:
                                app_data_list.append((event_id, 10, "app_type", old_app_type))
                                # app_data_list.append(
                                #     ContinuousEventsTagModel(
                                #         event_id=event_id,
                                #         tag_type=10,  # 非常用端口事件脚本增强的应用类型使用10，与常用端口区分
                                #         tag_name="app_type",
                                #         tag_content=old_app_type
                                #     )
                                # )
                            # 删除原有的tag_type=10的应用类型
                            ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_name="app_type",
                                                                    tag_type=10).delete()
                            # 增强，最多新增3条
                            if app_data_list:
                                #>> app_data_list = list(set(app_data_list))
                                # # 新增前先删除
                                # for p in ports:
                                #     self.cet_obj.delete_(tag_name="app_type", event_id=event_id, tag_type=0,
                                #                          tag_content=",{}%".format(p))
                                # self.cet_obj.bulk_create(app_data_list)
                                if len(app_data_list) > 3:
                                    app_data_list = app_data_list[:3]
                                # ContinuousEventsTagModel.objects.bulk_create(app_data_list)
                                ContinuousEventsTag().bulk_create(app_data_list)

                                continue

                        if not is_asset:
                            # 资产为空，查询事件详情中的应用协议
                            mlog.info("----无资产或资产信息与事件不一致，开始查询事件详情应用协议")
                            port_distribution = self.filter_port_distribution(event_id)
                            if port_distribution:
                                data = get_port_traffic(alert_ids=related_alerts, report_type=report_type)
                                protocol = data.get("protocol").split(",")[0] if data.get("protocol") else ""
                                if "源端口集中" in port_distribution and up_or_down_tag == 2:
                                    port = data.get("out")[0].get("port")
                                elif "源端口分散" in port_distribution and up_or_down_tag == 2:
                                    port = UNCOMMON_PORT_CONSTANT
                                elif "目的端口集中" in port_distribution and up_or_down_tag == 1:
                                    port = data.get("in")[0].get("port")
                                elif "目的端口分散" in port_distribution and up_or_down_tag == 1:
                                    port = UNCOMMON_PORT_CONSTANT
                                else:
                                    port = ""
                            else:
                                mlog.info("----无端口分布，无法判断端口")
                                continue

                            if protocol and port:
                                app_data = CURLY_BRACES_3.format("未知服务", protocol, port)
                                if app_data and app_data not in old_app_type_list:
                                    mlog.info("--应用类型增强：{}".format(app_data))
                                    #>> self.cet_obj.delete_(tag_name="app_type", event_id=event_id, tag_type=0,
                                    #                      tag_content=",{}%".format(port))
                                    # self.cet_obj.bulk_create([(event_id, 0, "app_type", app_data)])
                                    # 去除端口相同的应用类型
                                    app_type_list = [app_data] + [i for i in old_app_type_list
                                                                  if not i.endswith(",{}".format(port))]
                                    # 整理新增逻辑
                                    create_list = [(event_id, 10, "app_type", i) for i in app_type_list]
                                    # create_list = [ContinuousEventsTagModel(
                                    #     event_id=event_id,
                                    #     tag_type=10,  # 非常用端口事件脚本增强的应用类型使用10，与常用端口区分
                                    #     tag_name="app_type",
                                    #     tag_content=i
                                    # ) for i in app_type_list]
                                    # 删除原有的tag_type=10的应用类型
                                    ContinuousEventsTagModel.objects.filter(event_id=event_id, tag_name="app_type",
                                                                            tag_type=10).delete()
                                    # 新增
                                    if len(create_list) > 3:
                                        create_list = create_list[:3]
                                    # ContinuousEventsTagModel.objects.bulk_create(create_list)
                                    ContinuousEventsTag().bulk_create(create_list)

                    except Exception as e:
                        mlog.exception("----id:{},ip:{},获取协议失败，原因：{}, 跳过".format(event_id, ip_, e))

            # 5min类型事件记录最新时间
            if self.analysis_tech == 1:
                self.aj.update_conf_value(self.record_key, record_end_time)
            t2 = datetime.datetime.now()
            mlog.info("=======研判结束：{},耗时：{}秒".format(t2, (t2 - t1).total_seconds()))
        except Exception as e:
            mlog.exception("研判失败，记录当前id:{}，失败原因：{}".format(record_id, e))
        finally:
            self.request_asset_obj.close_session()


if __name__ == '__main__':
    report_type = 0
    if len(sys.argv) == 2:
        report_type = int(sys.argv[1])

    #>> lock_name = ""
    conf_key = ""
    if report_type == 1:
        #>> lock_name = "TLLOG_UNCOMMON_PORT_JUDGE"
        conf_key = "tllog_uncommon_port_judge_timestamp"
    elif report_type == 2:
        #>> lock_name = "NETFLOW_UNCOMMON_PORT_JUDGE"
        conf_key = "netflow_uncommon_port_judge_timestamp"
    else:
        mlog.error("参数不正确，退出:{}".format(report_type))
        exit(0)

    # 脚本运行
    UncommonPortJudge(record_key=conf_key).run(report_type=report_type)

    mlog.info("=================删除锁---结束任务=================")
