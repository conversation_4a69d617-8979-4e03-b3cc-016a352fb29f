#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import sys

from appManager.module.global_api import ComponentHelper


def add_task(app_name=None):
    # 组件批量注册任务
    ch = ComponentHelper(app_name)
    ch_ret = ch.add_task()
    if ch_ret['status'] != 200:
        msg = '定时任务注册失败, 失败原因： %s' % ch_ret['msg']
        print(msg)
        raise ValueError(msg)
    else:
        print('定时任务注册成功')


def start_task(app_name=None):
    """
    组件批量启动任务
    """
    ch = ComponentHelper(app_name)
    ch_ret = ch.start_task()
    print('定时任务启动成功:{}'.format(ch_ret))


def remove_task(app_name=None):
    """
    组件批量注销任务
    """
    ch = ComponentHelper(app_name)
    ch.remove_task()
    print('定时任务注销成功')


if __name__ == '__main__':
    flag = sys.argv[1]
    if flag == '1':
        add_task(app_name='cncert_kj')
    elif flag == '2':
        start_task(app_name='cncert_kj')
    elif flag == '3':
        remove_task(app_name='cncert_kj')
    else:
        print('参数错误')
