#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    资产类事件增强应用类型脚本
    每天执行一次
"""
import copy
import json
import os
import sys
import time
from datetime import datetime

import django

# 设置Django环境
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ISOP.settings')
django.setup()
from django.db.models import Q
from django.db import transaction

from cncert_kj.models.base_model import AssetInfoModel, ContinuousEventsConfModel, ContinuousEvents, \
    ContinuousEventsTagModel
from cncert_kj.utils.conf_util import ASSET_TYPE
from cncert_kj.utils import logger

mlog = logger.init_logger('asset_event_add_app_type')


class AssetEventAppTypeEnhancer:
    def __init__(self, report_type=0, start_time=None, end_time=None, analysis_tech=1, event_types=[], event_ids=None):
        """初始化
        Args:
            report_type: 报表类型 1-tllog 2-netflow
        """
        self.report_type = report_type
        self.start_time = start_time if start_time else None
        self.end_time = end_time if end_time else None
        self.analysis_tech = analysis_tech
        self.event_types = event_types
        self.event_ids = event_ids
        self.conf_key = self._get_conf_key()
        self.important_asset_types = self._get_important_asset_types()
        self.last_timestamp = self._get_last_timestamp()
        self.asset_tag_and_asset_type_map = ASSET_TYPE

    def _get_conf_key(self):
        """获取配置键名"""
        conf_keys = {
            1: "tllog_asset_event_add_app_type_timestamp",
            2: "netflow_asset_event_add_app_type_timestamp"
        }
        return conf_keys.get(self.report_type, "")

    def _get_last_timestamp(self):
        """获取上次处理的时间戳"""
        try:
            last_time = ContinuousEventsConfModel.objects.filter(
                conf_key=self.conf_key
            )
            if not last_time.exists():
                # 如果配置不存在，则创建配置
                conf_value = int(time.time()) - 60 * 60 * 24 * 3
                ContinuousEventsConfModel.objects.create(
                    conf_key=self.conf_key,
                    conf_value=conf_value
                )
                return conf_value
            return int(last_time.first().conf_value)
        except Exception as e:
            mlog.exception("获取上次处理时间失败: {}".format(e))
            return 0

    def _save_last_timestamp(self, timestamp):
        """保存处理时间戳
        Args:
            timestamp: 要保存的时间戳
        """
        try:
            ContinuousEventsConfModel.objects.filter(
                conf_key=self.conf_key
            ).update(
                conf_value=timestamp
            )

            mlog.info("保存处理时间戳成功: {}".format(timestamp))
        except Exception as e:
            mlog.error("保存处理时间戳失败: {}".format(e))

    def _get_important_asset_types(self):
        """获取重要资产的事件类型列表"""
        try:
            from cncert_kj.utils.conf_util import CommonConf
            conf = CommonConf()
            asset_types = conf.get_cate_type(is_event=True).get(u"重要资产", [])
            iot_types = conf.get_cate_type(is_event=True).get(u"物联网设备", [])
            return [int(t) for t in asset_types + iot_types]
        except Exception as e:
            mlog.exception("获取重要资产类型失败: {}".format(e))
            return []

    def _get_events_to_process(self):
        """获取需要处理的事件列表"""
        try:
            if self.event_ids is None:
                # 获取时间范围
                if self.start_time and self.end_time:
                    start_time = self.start_time
                    end_time = self.end_time
                else:
                    start_time = self.last_timestamp - 60 * 5
                    end_time = int(time.time())

                mlog.info("查询时间范围: {} - {}".format(
                    datetime.fromtimestamp(start_time).strftime(DATE_TIME_FORMAT),
                    datetime.fromtimestamp(end_time).strftime(DATE_TIME_FORMAT)
                ))
                # 构建查询条件字典
                query_dict = {
                    'event_type__in': self.important_asset_types,
                    'end_time__gt': start_time,
                    'end_time__lte': end_time,
                    'report_type': self.report_type,
                    'analysis_tech': self.analysis_tech
                }
                if self.event_types:
                    query_dict['event_type__in'] = self.event_types
            else:
                query_dict = {
                    'event_id__in': tuple(self.event_ids),
                    'report_type': self.report_type,
                }
            # 查询事件
            events = ContinuousEvents.objects.filter(**query_dict).values(
                'event_id', 'src_ip', 'dst_ip', 'src_port', 'dst_port', "event_type", 'end_time'
            ).order_by('end_time')

            if not events:
                return []

            # 获取所有事件ID
            event_ids = [event['event_id'] for event in events]
            self.event_ids = event_ids

            # 批量查询标签
            port_tags = {
                tag.event_id: tag.tag_content
                for tag in ContinuousEventsTagModel.objects.filter(
                    event_id__in=event_ids,
                    tag_name='port_distribution'
                )
            }

            # 在内存中关联数据
            for event in events:
                event['port_distribution'] = port_tags.get(event['event_id'])
            return list(events)
        except Exception as e:
            mlog.exception("获取事件列表失败: {}".format(e))
            return []

    def _get_concentrated_endpoints(self, event):
        """获取端口分布状态为集中的一端或两端的IP和端口
        
        Args:
            event: 事件信息字典
            
        Returns:
            list: [(ip, port), ...] 需要查询的IP和端口列表
        """
        try:

            endpoints = []
            status_content = event["port_distribution"]
            if not status_content:
                mlog.warning("事件 {} 未找到端口分布状态".format(event['event_id']))
                return []

            if status_content == '源端口集中 目的端口集中':
                # 源目都集中，都需要查询
                endpoints.append((event['src_ip'], event['src_port']))
                endpoints.append((event['dst_ip'], event['dst_port']))
                # mlog.debug("事件 {} 源端口集中 目的端口集中".format(event['event_id']))

            elif status_content == '源端口集中 目的端口分散':
                # 源端集中，查询源端
                endpoints.append((event['src_ip'], event['src_port']))
                # mlog.debug("事件 {} 源端口集中".format(event['event_id']))

            elif status_content == '源端口分散 目的端口集中':
                # 目的端集中，查询目的端
                endpoints.append((event['dst_ip'], event['dst_port']))
                # mlog.debug("事件 {} 目的端口集中".format(event['event_id']))

            elif status_content == '源端口分散 目的端口分散':
                # 都分散，不需要查询
                pass
            else:
                mlog.warning("事件 {} 端口分布状态异常: {}".format(
                    event['event_id'], status_content))

            return endpoints

        except Exception as e:
            mlog.exception("解析端口分布状态失败, 事件ID: {}, 错误: {}".format(
                event['event_id'], e))
            return []

    def _get_asset_info(self, ip, port):
        """查询资产信息"""
        try:
            port_list = port.strip(',').split(',')

            asset = AssetInfoModel.objects.filter(
                asset_ip=ip,
                asset_port__in=port_list
            ).values(
                # 'asset_type',  # 资产大类
                'product',  # 产品
                'protocol',  # 协议
                'asset_port'  # 端口
            )

            return asset
        except Exception as e:
            mlog.exception("查询资产信息失败, IP: {}, 端口: {}, 错误: {}".format(ip, port, e))
            return None

    def _compose_app_type(self, asset_info_list):
        """组装应用类型标签内容"""
        if not asset_info_list:
            return None

        try:
            app_type_list = []
            for asset_info in asset_info_list:
                product = asset_info.get('product', '')
                protocol = asset_info.get('protocol', '')
                port = asset_info.get('asset_port', '')
                if not protocol or not product:
                    continue

                app_type_list.append("{},{},{}".format(product, protocol, port))
            return app_type_list
        except Exception as e:
            mlog.exception("组装应用类型标签失败: {}".format(e))
            return None

    def _save_app_type_tag(self, event_id, app_type_list):
        """保存应用类型标签"""
        try:
            with transaction.atomic():
                if self.analysis_tech == 2:
                    analysis_tech_tag = "动态基线事件"
                else:
                    analysis_tech_tag = "静态基线事件"
                # 删除原有的app_type标签
                ContinuousEventsTagModel.objects.filter(
                    event_id=event_id,
                    tag_type=0,
                    tag_name='app_type'
                ).delete()
                mlog.info("删除{}应用类型, event_id: {}".format(analysis_tech_tag, event_id))

                # 创建新标签
                create_list = []
                for app_type in app_type_list:
                    create_list.append(ContinuousEventsTagModel(
                        event_id=event_id,
                        tag_type=0,
                        tag_name='app_type',
                        tag_content=app_type
                    ))
                ContinuousEventsTagModel.objects.bulk_create(create_list)
                mlog.info("{}应用类型, event_id: {}, app_type: {}".format(analysis_tech_tag, event_id,
                                                                          json.dumps(app_type_list,
                                                                                     ensure_ascii=False)))

        except Exception as e:
            mlog.exception("保存应用类型标签失败, event_id: {}, app_type: {}, 错误: {}".format(
                event_id, app_type, e))

    def _batch_get_asset_info(self, endpoints):
        """批量获取资产信息
        Args:
            endpoints: 端点列表 [(ip1, port1), (ip2, port2), ...]
        Returns:
            dict: {(ip, port): [asset_info_list]}
        """
        result = {}

        if not endpoints:
            return result

        try:
            # 构建批量查询条件

            # 使用Q对象构建OR条件
            query = Q()
            for asset_ip, asset_port in endpoints:
                query |= (Q(asset_ip=asset_ip) & Q(asset_port=asset_port))

            # 执行批量查询
            assets = AssetInfoModel.objects.filter(query)

            # 按(ip, port)分组结果
            for asset in assets:
                # 有没有可能同样的ip与port，但是product和protocol不一样？？
                key = (asset.asset_ip, asset.asset_port)
                if key not in result:
                    result[key] = []

                result[key].append({
                    'asset_ip': asset.asset_ip,
                    'asset_port': asset.asset_port,
                    'product': asset.product,
                    'protocol': asset.protocol
                })

            return result
        except Exception as e:
            mlog.error("批量获取资产信息失败: {}".format(e))
            return result

    def run_(self):
        """主处理流程"""
        if not self.conf_key:
            mlog.error("配置键名为空，退出处理")
            return
        if self.analysis_tech == 2:
            analysis_tech_tag = "动态基线"
        else:
            analysis_tech_tag = "静态基线"

        if self.report_type == 1:
            report_tag = "tllog"
        elif self.report_type == 2:
            report_tag = "netflow"
        else:
            report_tag = "unknown"

        mlog.info("开始处理--{}-{}--资产类事件应用类型增强...".format(report_tag, analysis_tech_tag))

        if not self.important_asset_types:
            mlog.error("未获取到重要资产类型，退出处理")
            return

        events = self._get_events_to_process()
        if not events:
            mlog.info("未找到需要处理的事件")
            return

        mlog.info("{}{}找到 {} 个事件需要处理".format(report_tag, analysis_tech_tag, len(events)))

        processed_count = 0
        success_count = 0
        last_timestamp = self.last_timestamp

        create_list = []
        for event in events:
            try:
                event_id = event['event_id']
                # 更新最后处理时间
                last_timestamp = max(last_timestamp, event['end_time'])

                # 获取需要查询的端口列表
                endpoints = self._get_concentrated_endpoints(event)
                if not endpoints:
                    continue

                # 处理每个需要查询的端点
                for ip, port in endpoints:
                    # 查询资产信息
                    asset_info_list = self._get_asset_info(ip, port)
                    if not asset_info_list:
                        continue

                    # 组装应用类型
                    app_type_list = self._compose_app_type(asset_info_list)
                    if not app_type_list:
                        continue

                    for app_type in app_type_list:
                        create_list.append(ContinuousEventsTagModel(
                            event_id=event_id,
                            tag_type=0,
                            tag_name='app_type',
                            tag_content=app_type
                        ))
                    # self._save_app_type_tag(event_id, app_type_list)

                    success_count += 1

            except Exception as e:
                mlog.error("处理事件失败, event_id: {}, 错误: {}".format(event_id, e))
            finally:
                processed_count += 1

            # 每处理100条记录输出一次进度
            if processed_count % 100 == 0:
                mlog.info(
                    "{}{},已处理 {} 条事件，成功 {} 条".format(report_tag, analysis_tech_tag, processed_count,
                                                              success_count))

        if create_list:
            with transaction.atomic():
                mlog.info("批量删除 {} 条事件原有应用类型".format(len(self.event_ids)))
                ContinuousEventsTagModel.objects.filter(
                    event_id__in=self.event_ids,
                    tag_type=0,
                    tag_name='app_type'
                ).delete()
                mlog.info("批量创建 {} 条应用类型标签".format(len(create_list)))
                ContinuousEventsTagModel.objects.bulk_create(create_list)

        if (self.start_time and self.end_time) or self.analysis_tech == 2:
            mlog.info("指定时间范围处理，不保存最后处理时间")
        else:
            # 保存最后处理时间
            if last_timestamp > self.last_timestamp:
                self._save_last_timestamp(last_timestamp)

        mlog.info("资产类--{}-{}--事件应用类型增强处理完成，共处理 {} 条事件，成功 {} 条".format(
            report_tag, analysis_tech_tag, processed_count, success_count))

    def run(self):
        """主处理流程:二次优化，批量查询、匹配、处理，处理速度比事件归并要快，查询时间往前推5分钟"""
        if not self.conf_key:
            mlog.error("配置键名为空，退出处理")
            return
        if self.analysis_tech == 2:
            analysis_tech_tag = "动态基线"
        else:
            analysis_tech_tag = "静态基线"

        if self.report_type == 1:
            report_tag = "tllog"
        elif self.report_type == 2:
            report_tag = "netflow"
        else:
            report_tag = "unknown"

        mlog.info("开始处理--{}-{}--资产类事件应用类型增强...".format(report_tag, analysis_tech_tag))

        if not self.important_asset_types:
            mlog.error("未获取到重要资产类型，退出处理")
            return

        events = self._get_events_to_process()
        if not events:
            mlog.info("未找到需要处理的事件")
            return

        mlog.info("{}{}找到 {} 个事件需要处理".format(report_tag, analysis_tech_tag, len(events)))

        processed_count = 0
        success_count = 0
        last_timestamp = copy.deepcopy(self.last_timestamp)
        # 批量处理参数
        BATCH_SIZE = 1000  # 每批处理的事件数量

        # 收集所有需要查询的端点
        all_endpoints = {}  # {(ip, port): [event_ids]}

        # 第一步：收集所有需要查询的端点和事件映射
        for event in events:
            try:
                event_id = event['event_id']

                # 更新最后处理时间
                last_timestamp = max(last_timestamp, event['end_time'])

                # 获取需要查询的端口列表
                endpoints = self._get_concentrated_endpoints(event)

                # 处理每个IP和其对应的端口列表
                for ip, port_str in endpoints:
                    # 解析端口字符串获取端口列表
                    ports = port_str.strip(',').split(',')

                    # 为每个端口创建端点映射
                    for port in ports:
                        key = (ip, port)
                        if key not in all_endpoints:
                            all_endpoints[key] = []
                        all_endpoints[key].append(event_id)

            except Exception as e:
                mlog.error("收集端点信息失败, event_id: {}, 错误: {}".format(event.get('event_id'), e))
        mlog.info("共收集到 {} 个唯一端点需要查询".format(len(all_endpoints)))

        # 第二步：分批处理端点
        endpoint_list = all_endpoints.keys()
        create_list = []

        for i in range(0, len(endpoint_list), BATCH_SIZE):
            batch_endpoints = endpoint_list[i:i + BATCH_SIZE]
            # mlog.info("处理第 {}/{} 批端点，共 {} 个".format(
            #     i // BATCH_SIZE + 1,
            #     (len(endpoint_list) + BATCH_SIZE - 1) // BATCH_SIZE,
            #     len(batch_endpoints)
            # ))

            # 批量获取资产信息
            asset_info_batch = self._batch_get_asset_info(batch_endpoints)
            # 处理每个端点的资产信息
            for endpoint, asset_info_list in asset_info_batch.items():
                if not asset_info_list:
                    continue

                # 组装应用类型
                app_type_list = self._compose_app_type(asset_info_list)
                if not app_type_list:
                    continue

                # 获取关联的事件ID列表
                related_event_ids = all_endpoints.get(endpoint, [])

                # 为每个关联事件保存标签
                for event_id in related_event_ids:
                    for app_type in app_type_list:
                        create_list.append((event_id, 0, "app_type", app_type))

                        # create_list.append(ContinuousEventsTagModel(
                        #     event_id=event_id,
                        #     tag_type=0,
                        #     tag_name='app_type',
                        #     tag_content=app_type
                        # ))
                    success_count += 1

                processed_count += len(related_event_ids)

            # 输出进度
            mlog.info("已处理 {} 条事件，成功 {} 条".format(processed_count, success_count))

        if create_list:
            with transaction.atomic():
                mlog.info("批量删除 {} 条事件原有应用类型".format(len(self.event_ids)))
                ContinuousEventsTagModel.objects.filter(
                    event_id__in=self.event_ids,
                    tag_type=0,
                    tag_name='app_type'
                ).delete()
                mlog.info("批量创建 {} 条应用类型标签".format(len(create_list)))
                # ContinuousEventsTagModel.objects.bulk_create(create_list)
                if create_list:
                    # 使用原生SQL批量插入
                    from django.db import connection
                    cursor = connection.cursor()

                    # 构建批量插入SQL
                    sql = """
                    INSERT INTO internal_app_bsa_gjk.continuous_events_tag 
                    (event_id, tag_type, tag_name, tag_content) 
                    VALUES (%s, %s, %s, %s)
                    """

                    # 分批执行，每批1000条
                    batch_size = 10000
                    for i in range(0, len(create_list), batch_size):
                        batch = create_list[i:i + batch_size]
                        cursor.executemany(sql, batch)
                    mlog.info("成功插入新标签数据,数量:%d", len(create_list))

        if (self.start_time and self.end_time) or self.analysis_tech == 2:
            mlog.info("指定时间范围处理，不保存最后处理时间")
        else:
            # 保存最后处理时间
            if last_timestamp >= self.last_timestamp:
                self._save_last_timestamp(last_timestamp)

        mlog.info("资产类--{}-{}--事件应用类型增强处理完成，共处理 {} 条事件，成功 {} 条".format(
            report_tag, analysis_tech_tag, processed_count, success_count))


if __name__ == '__main__':
    try:
        import argparse

        parser = argparse.ArgumentParser(description='资产类事件增强应用类型脚本')
        parser.add_argument('report_type', type=int, help='报表类型: 1-tllog 2-netflow')
        parser.add_argument('--start', '-s', type=int, help='开始时间，时间戳')
        parser.add_argument('--end', '-e', type=int, help='结束时间，时间戳')
        parser.add_argument('--analysis_tech', '-a', type=int, default=1, help='动静态基线')
        args = parser.parse_args()

        if args.report_type not in [1, 2]:
            mlog.error("报表类型参数不正确，需要为1(tllog)或2(netflow)，退出: {}".format(args.report_type))
            sys.exit(1)

        if args.start and args.end:
            mlog.info("指定时间范围处理，开始时间: {}, 结束时间: {}".format(
                datetime.fromtimestamp(args.start).strftime(DATE_TIME_FORMAT),
                datetime.fromtimestamp(args.end).strftime(DATE_TIME_FORMAT)
            ))
        else:
            mlog.info("未指定时间范围，使用默认时间范围")

        mlog.info("================开始执行{}{}资产类事件增强应用类型脚本".format(
            "tllog" if args.report_type == 1 else "netflow",
            "动态基线" if args.analysis_tech == 1 else "静态基线"
        ))

        enhancer = AssetEventAppTypeEnhancer(
            report_type=args.report_type,
            start_time=args.start,
            end_time=args.end,
            analysis_tech=args.analysis_tech
        )
        enhancer.run()
        mlog.info("================{}{}资产类事件增强应用类型脚本执行完成".format(
            "tllog" if args.report_type == 1 else "netflow",
            "动态基线" if args.analysis_tech == 1 else "静态基线"

        ))
    except Exception as e:
        mlog.exception("脚本执行失败: {}".format(e))
        sys.exit(1)
