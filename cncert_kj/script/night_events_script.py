#!/usr/bin/env python
# -*- coding:utf-8 -*-
import time

from collections import defaultdict
from datetime import datetime, timedelta
from django.db import connection, transaction
from django.db.models import When, Value, Case, F

from cncert_kj.utils.bytes_trans import long2unit
from cncert_kj.models.base_model import NightEventsModel, ContinuousEventsConfModel
from cncert_kj.utils.request_filing_sys import RequestFilingSys
from cncert_kj.utils import logger
from cncert_kj.utils.time_trans import DATE_TIME_FORMAT

mlog = logger.init_logger("night_events_script")


class Config(object):
    """配置类"""
    # 记录时间戳key
    NIGHT_TIMESTAMP_KEY = "night_events_timestamp"

    # 第一次查询，默认查7天范围，前一天为结束时间
    FIRST_QUERY_DAYS = 7
    # 后续查询默认2天范围，前一天为结束时间
    SECOND_QUERY_DAYS = 2

    # 夜间时间段定义
    NIGHT_START_HOUR = 22
    NIGHT_END_HOUR = 6

    # 批处理大小
    BATCH_SIZE = 1000

    # 日志配置
    LOG_CONFIG = {
        'filename': 'night_transfer.log',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'level': 'INFO'
    }

    # 状态定义
    STATUS_ONGOING = 1  # 持续中
    STATUS_FINISHED = 0  # 已结束
    STATUS_MAP = {
        STATUS_ONGOING: "持续中",
        STATUS_FINISHED: "已结束"
    }

    # 取反标识
    REVERSE_NO = 0  # 不取反
    REVERSE_YES = 1  # 取反

    # 告警类型
    REPORT_TYPE_TRAFFIC = 1  # 通联日志告警
    REPORT_TYPE_NETFLOW = 2  # netflow告警

    # 检测手段
    ANALYSIS_TECH_STATIC = 1  # 静态基线检测
    ANALYSIS_TECH_DYNAMIC = 2  # 动态基线检测

    # 告警表名
    ALERT_TABLE_MAP = {
        REPORT_TYPE_TRAFFIC: "internal_app_bsa_gjk.traffic_alert",
        REPORT_TYPE_NETFLOW: "internal_app_bsa_gjk.netflow_alert_5min"
    }
    # 告警流量字段名
    ALERT_BYTES_MAP = {
        REPORT_TYPE_TRAFFIC: {"up_bytes_all": "up_bytes_all", "down_bytes_all": "down_bytes_all"},
        REPORT_TYPE_NETFLOW: {"up_bytes_all": "bytes_all", "down_bytes_all": "bytes_all_down"}
    }


class AlertUtils(object):
    """工具类"""

    def timestamp_to_datetime(self, ts):
        """时间戳转datetime对象"""
        return datetime.fromtimestamp(ts)

    def datetime_to_timestamp(self, dt):
        """datetime对象转时间戳"""
        return int(time.mktime(dt.timetuple()))
    
    def timestamp_to_str(self, ts):
        """时间戳转str"""
        return time.strftime(DATE_TIME_FORMAT, time.localtime(ts))

    def get_time_range(self):
        """获取时间范围。NightEventsModel如果有数据，则取结束时间"""
        night_timestamp_obj = ContinuousEventsConfModel.objects.filter(conf_key=Config.NIGHT_TIMESTAMP_KEY)
        if night_timestamp_obj.exists() and night_timestamp_obj.first().conf_value:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=Config.SECOND_QUERY_DAYS)
            st = self.datetime_to_timestamp(start_time.replace(hour=0, minute=0, second=0, microsecond=0))
            et = self.datetime_to_timestamp(end_time.replace(hour=0, minute=0, second=0, microsecond=0))
        else:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=Config.FIRST_QUERY_DAYS)
            st = self.datetime_to_timestamp(start_time.replace(hour=0, minute=0, second=0, microsecond=0))
            et = self.datetime_to_timestamp(end_time.replace(hour=0, minute=0, second=0, microsecond=0))
        return st, et

    def is_night_time(self, start_time, end_time):
        """判断告警开始时间与结束时间是否在夜间时间段(22:00-06:00)
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
        Returns:
            bool: True-都在夜间且连续, False-其他情况
        """

        def is_night_hour(hour):
            """判断小时是否在夜间范围内(22:00-06:00)"""
            return hour >= 22 or hour < 6

        # 获取开始和结束时间的详细信息
        start_local = time.localtime(start_time)
        end_local = time.localtime(end_time)

        # 获取日期和小时
        start_date = time.strftime("%Y%m%d", start_local)
        end_date = time.strftime("%Y%m%d", end_local)
        start_hour = start_local.tm_hour
        end_hour = end_local.tm_hour

        # 如果开始或结束时间有任一不在夜间，直接返回False
        if not (is_night_hour(start_hour) and is_night_hour(end_hour)):
            return False

        # 计算日期差
        date_diff = int(end_date) - int(start_date)

        # 判断是否连续
        if date_diff == 0:
            # 同一天的情况，只要都在夜间即可
            return True
        elif date_diff == 1:
            # 相邻两天的情况，开始时间必须在22:00后，结束时间必须在6:00前
            return start_hour >= 22 and end_hour < 6

        # 其他情况都不是连续夜间
        return False

    def is_info_(self, unit):
        """判断源目信息"""
        return bool(unit and unit.strip())

    def get_alerts(self, st, et):
        """查询告警数据"""
        try:
            end_time = et
            start_time = st

            alert_dict = defaultdict(list)

            for report_type in [Config.REPORT_TYPE_TRAFFIC, Config.REPORT_TYPE_NETFLOW]:
                sql = """
                SELECT 
                    sip,
                    dip,
                    uuid,
                    start_time,
                    end_time,
                    src_region,
                    src_com,
                    src_unit,
                    dst_region,
                    dst_unit,
                    {up_bytes_all} as up_bytes_all,
                    {down_bytes_all} as down_bytes_all
                FROM {table}
                WHERE end_time BETWEEN %s AND %s
                """.format(table=Config.ALERT_TABLE_MAP[report_type],
                           up_bytes_all=Config.ALERT_BYTES_MAP[report_type]["up_bytes_all"],
                           down_bytes_all=Config.ALERT_BYTES_MAP[report_type]["down_bytes_all"])

                with connection.cursor() as cursor:
                    cursor.execute(sql, [start_time, end_time])
                    columns = [col[0] for col in cursor.description]
                    rows = cursor.fetchall()
                    mlog.info("{},查询到{}条告警数据".format(Config.ALERT_TABLE_MAP[report_type], len(rows)))

                    # 重构返回数据格式
                    for row in rows:
                        alert = dict(zip(columns, row))
                        # 判断是否需要取反
                        should_reverse = self.should_reverse(alert)
                        if should_reverse:
                            self.swap_attributes(alert)
                        alert_dict[(alert['sip'], alert['dip'])].append(alert)

            return alert_dict
        except Exception as e:
            mlog.error("查询告警失败: %s", str(e))
            raise

    def should_reverse(self, alert):
        """判断是否需要取反"""
        if not alert['src_region'] and not alert['dst_region']:
            return self.is_info_(alert['dst_unit']) and not self.is_info_(alert['src_unit'])
        elif (any([
            not alert['src_region'],
            "中国" not in alert['src_region'],
            any(region in alert['src_region'] for region in (u"台湾", u"香港", u"澳门")),
        ]) and all([
            "中国" in alert['dst_region'],
            not any(region in alert['dst_region'] for region in (u"台湾", u"香港", u"澳门")),
        ])):
            return True
        return False

    def swap_attributes(self, alert):
        """交换源目信息"""
        swap_fields = [
            ('sip', 'dip'),
            ('src_region', 'dst_region'),
            ('src_com', 'dst_com'),
            ('src_unit', 'dst_unit'),
            ('up_bytes_all', 'down_bytes_all')
        ]

        for src_field, dst_field in swap_fields:
            if src_field in alert and dst_field in alert:
                alert[src_field], alert[dst_field] = alert[dst_field], alert[src_field]

    def count_different_days(self, time_list, old_end_time=None):
        """统计告警列表中不同日期的数量
        Args:
            time_list:[(start_time,end_time), (start_time,end_time),...]
            old_end_time:已存在事件的结束时间
        Returns:
            int: 不同日期的数量
        """
        try:
            # 使用集合来存储不同的日期
            days = set()

            # for start_time, end_time in zip(start_times, end_times):
            for start_time, end_time in time_list:
                # 获取开始时间的日期
                start_date = time.strftime("%Y%m%d", time.localtime(start_time))
                # 获取结束时间的日期
                end_date = time.strftime("%Y%m%d", time.localtime(end_time))

                if old_end_time:
                    old_end_time_date = time.strftime("%Y%m%d", time.localtime(old_end_time))
                    if start_date == old_end_time_date or end_date == old_end_time_date:
                        # 如果开始时间或结束时间等于old_end_time_date, 则跳过
                        continue

                # 将日期添加到集合中
                days.add(start_date)
                days.add(end_date)

            # 返回集合的长度，即不同日期的数量
            return len(days)

        except Exception as e:
            mlog.exception(u"统计不同日期数量失败: %s" % str(e))
            return 0

    def update_abnormal_days(self, old_end_time, time_list):
        """更新异常天数
        Args:
            old_end_time: 原结束时间
            time_list: 时间列表
        Returns:
            int: 异常天数
        """
        try:
            # 保留time_list中所有开始时间大于old_end_time的时间元组；如果开始时间小于等于old_end_time，但是结束时间大于old_end_time，保留该时间元组,且将开始时间更新为old_end_time+1
            filtered_time_list = []
            for start_time, end_time in time_list:
                if start_time > old_end_time:
                    filtered_time_list.append((start_time, end_time))
                elif start_time <= old_end_time:
                    filtered_time_list.append((old_end_time + 1, end_time))
            time_list = filtered_time_list
            # 计算异常天数
            abnormal_days = self.count_different_days(time_list, old_end_time=old_end_time)
            return abnormal_days
        except Exception as e:
            mlog.exception(u"更新异常天数失败: %s" % str(e))
            return 0


class NightTransferProcessor(object):
    """夜间传输事件处理类"""

    def __init__(self):
        self.utils = AlertUtils()

    def process(self):
        """主处理流程"""
        try:
            start_time, end_time = self.utils.get_time_range()
            mlog.info(u"开始处理夜间传输事件, 处理事件范围：%s--%s", start_time, end_time)

            # 1. 查询告警数据
            alerts_dic = self.utils.get_alerts(start_time, end_time)
            if not alerts_dic:
                mlog.warning(u"未查询到告警数据")
                return
            mlog.info(u"查询到 %d 对数据", len(alerts_dic))

            # 2. 获取所有IP对的现有事件
            # ip_pairs = [(src_ip, dst_ip) for src_ip, dst_ip in alerts_dic.keys()]
            ip_pairs = alerts_dic.keys()
            # 所有的scr_ip
            all_src_ip = list(set([pair[0] for pair in ip_pairs]))
            # 所有的dst_ip
            all_dst_ip= list(set([pair[1] for pair in ip_pairs]))
            existing_events = {}
            for event in NightEventsModel.objects.filter(
                    src_ip__in=all_src_ip,
                    dst_ip__in=all_dst_ip
            ):
                existing_events[(event.src_ip, event.dst_ip)] = event

            # 2. 预处理所有告警数据
            processed_alerts = {}
            # 获取所有的备案单位src_com
            with RequestFilingSys() as request_filing_obj:
                src_com_dic = request_filing_obj.query_icp_api(all_src_ip)

            for (src_ip, dst_ip), alerts in alerts_dic.iteritems():
                # 一次性处理每个IP对的所有数据
                start_times = []
                end_times = []
                up_bytes = 0  # 直接在循环中维护最大值
                down_bytes = 0
                alert_uuids = []
                is_all_night = True  # 标记是否全部是夜间

                first_alert = alerts[0]  # 记录第一条告警

                time_list = []
                for alert in alerts:
                    start_time = alert['start_time']
                    end_time = alert['end_time']
                    start_times.append(start_time)
                    end_times.append(end_time)

                    # 直接更新最大值
                    up_bytes = max(up_bytes, alert['up_bytes_all'])
                    down_bytes = max(down_bytes, alert['down_bytes_all'])
                    alert_uuids.append(alert['uuid'])

                    # 只要有一个不是夜间就标记False
                    if is_all_night and not self.utils.is_night_time(start_time, end_time):
                        is_all_night = False
                    # 记录时间列表
                    time_list.append((start_time, end_time))

                # 获取单位信息
                src_com = src_com_dic.get(src_ip, {}).get("user", "") or first_alert['src_com']
                # src_com = first_alert['src_com']      # todo 开发环境，不查备案系统

                # 存储处理结果
                processed_alerts[(src_ip, dst_ip)] = {
                    "start_time": min(start_times),
                    "end_time": max(end_times),
                    "src_ip": src_ip,
                    "src_com": src_com,
                    "src_unit": first_alert['src_unit'],
                    "dst_ip": dst_ip,
                    "dst_region": first_alert['dst_region'],
                    "up_bytes": up_bytes,
                    "down_bytes": down_bytes,
                    "alert_uuids": alert_uuids,
                    "alert_count": len(alerts),
                    "time_list": time_list,
                    "is_all_night": is_all_night
                }

            # 3. 批量处理新增和更新
            create_list = []
            update_batch = []

            for (src_ip, dst_ip), processed_data in processed_alerts.iteritems():
                existing_event = existing_events.get((src_ip, dst_ip))

                if existing_event:
                    if existing_event.status == Config.STATUS_ONGOING:
                        # 更新数据
                        alert_list = list(set(existing_event.related_alerts.split(";") + processed_data["alert_uuids"]))
                        update_data = {
                            "start_time": min(processed_data["start_time"], existing_event.start_time),
                            "end_time": max(processed_data["end_time"], existing_event.end_time),
                            "up_bytes": max(processed_data["up_bytes"], existing_event.up_bytes),
                            "down_bytes": max(processed_data["down_bytes"], existing_event.down_bytes),
                            "related_alerts": ";".join(alert_list),
                            "related_alerts_count": len(alert_list),
                            "abnormal_days": existing_event.abnormal_days + self.utils.update_abnormal_days(existing_event.end_time, processed_data["time_list"]),
                            "status": Config.STATUS_ONGOING if processed_data[
                                "is_all_night"] else Config.STATUS_FINISHED
                        }
                        update_batch.append((existing_event.id, update_data))
                else:
                    # 新增数据, 只有全部是夜间才创建
                    if processed_data["is_all_night"]:
                        create_list.append(NightEventsModel(
                            start_time=processed_data["start_time"],
                            end_time=processed_data["end_time"],
                            src_ip=processed_data["src_ip"],
                            src_com=processed_data["src_com"],
                            src_unit=processed_data["src_unit"],
                            dst_ip=processed_data["dst_ip"],
                            dst_region=processed_data["dst_region"],
                            up_bytes=processed_data["up_bytes"],
                            down_bytes=processed_data["down_bytes"],
                            related_alerts=";".join(processed_data["alert_uuids"]),
                            related_alerts_count=processed_data["alert_count"],
                            abnormal_days=self.utils.count_different_days(processed_data["time_list"]),
                            status=Config.STATUS_ONGOING
                        ))

            # 4. 批量执行数据库操作
            try:
                with transaction.atomic():
                    # 批量创建
                    if create_list:
                        mlog.info(u"待创建 %d 条记录", len(create_list))
                        NightEventsModel.objects.bulk_create(create_list, batch_size=5000)
                        mlog.info(u"成功创建 %d 条记录", len(create_list))

                    # 批量更新
                    if update_batch:
                        mlog.info(u"待更新 %d 条记录", len(update_batch))
                        # 分批处理更新
                        batch_size = 1000
                        total_updated = 0
                        n = 0
                        for i in range(0, len(update_batch), batch_size):
                            batch = update_batch[i:i + batch_size]
                            # 构建Case表达式
                            start_time_case = Case(
                                *[When(id=event_id, then=Value(data['start_time']))
                                  for event_id, data in batch],
                                default=F('start_time')
                            )

                            end_time_case = Case(
                                *[When(id=event_id, then=Value(data['end_time']))
                                  for event_id, data in batch],
                                default=F('end_time')
                            )

                            up_bytes_case = Case(
                                *[When(id=event_id, then=Value(data['up_bytes']))
                                  for event_id, data in batch],
                                default=F('up_bytes')
                            )

                            down_bytes_case = Case(
                                *[When(id=event_id, then=Value(data['down_bytes']))
                                  for event_id, data in batch],
                                default=F('down_bytes')
                            )

                            related_alerts_case = Case(
                                *[When(id=event_id, then=Value(data['related_alerts']))
                                  for event_id, data in batch],
                                default=F('related_alerts')
                            )

                            related_alerts_count_case = Case(
                                *[When(id=event_id, then=Value(data['related_alerts_count']))
                                  for event_id, data in batch],
                                default=F('related_alerts_count')
                            )

                            abnormal_days_case = Case(
                                *[When(id=event_id, then=Value(data['abnormal_days']))
                                  for event_id, data in batch],
                                default=F('abnormal_days')
                            )

                            status_case = Case(
                                *[When(id=event_id, then=Value(data['status']))
                                  for event_id, data in batch],
                                default=F('status')
                            )

                            # 执行批量更新
                            updated = NightEventsModel.objects.filter(
                                id__in=[event_id for event_id, _ in batch]
                            ).update(
                                start_time=start_time_case,
                                end_time=end_time_case,
                                up_bytes=up_bytes_case,
                                down_bytes=down_bytes_case,
                                related_alerts=related_alerts_case,
                                related_alerts_count=related_alerts_count_case,
                                abnormal_days=abnormal_days_case,
                                status=status_case
                            )

                            total_updated += updated
                            n += 1
                            mlog.info("第%d批更新成功，更新%d条记录", n, updated)

                        mlog.info(u"成功更新 %d 条记录", total_updated)

                    # 更新时间戳
                    ContinuousEventsConfModel.objects.update_or_create(
                        conf_key=Config.NIGHT_TIMESTAMP_KEY,
                        defaults={'conf_value': str(int(time.time()))}
                    )

            except Exception as e:
                mlog.exception(u"批量处理记录失败: %s", str(e))
                raise

        except Exception as e:
            mlog.exception(u"处理夜间传输事件失败: %s", str(e))

    def filter_data(self):
        """查询全表数据，封装后返回"""
        data = []
        title_ = ["开始时间", "结束时间", "境内IP", "境内备案单位", "单位名称", "境外IP", "境外IP地理位置", "流出流量", "流入流量",
                  "告警次数", "异常天数", "状态"]
        data.append(title_)

        # 获取所有数据
        night_events = NightEventsModel.objects.all()
        mlog.info(u"查询到 %d 条数据", night_events.count())

        for night_event in night_events:
            data.append([
                self.utils.timestamp_to_str(night_event.start_time),
                self.utils.timestamp_to_str(night_event.end_time),
                night_event.src_ip,
                night_event.src_com,
                night_event.src_unit,
                night_event.dst_ip,
                night_event.dst_region,
                long2unit(night_event.up_bytes),
                long2unit(night_event.down_bytes),
                night_event.related_alerts_count,
                night_event.abnormal_days,
                Config.STATUS_MAP.get(night_event.status)
            ])
        return data


def main():
    processor = NightTransferProcessor()
    processor.process()
    data = processor.filter_data()
    return data


if __name__ == "__main__":
    main()
