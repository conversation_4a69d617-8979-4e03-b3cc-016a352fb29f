#!/usr/bin/env python
# -*- coding:utf-8 -*-
import json
import datetime
from cncert_kj.utils.kafka_op import KafkaService
from cncert_kj.utils.redis_utils import RedisCache
from cncert_kj.models.base_model import ContinuousEvents

kafka_service = KafkaService()
topic  = "kj_cont_events"
def run():
    # events = ContinuousEvents.objects.filter(end_time__gt=1749139200)[:10]
    events = ContinuousEvents.objects.filter(event_id=3046882)
    insert_events = {"type": "insert", "events": []}
    update_events = {"type": "update", "events": []}
    for event_obj in events:
        alerts_count = event_obj.related_alerts_count
        event_info = {
            "event_id": event_obj.event_id,
            "report_type": event_obj.report_type,
            "reverse_tag": event_obj.reverse_tag,
            "event_type": event_obj.event_type,
            "analysis_tech": event_obj.analysis_tech,
            "related_alerts": [event_obj.related_alerts.split(';')[-1]],
        }
        # print "事件信息: %s" % event_info  # Python 2.7 格式打印
        if alerts_count > 1:
            # RedisCache().execute("lpush", "flow_surge_event_update", json.dumps(event_info, ensure_ascii=False))
            update_events.get("events").append(event_info)
            # print "已推送更新事件 - %s" % (json.dumps(event_info, ensure_ascii=False))
        else:
            # RedisCache().execute("lpush", "flow_surge_event_insert", json.dumps(event_info, ensure_ascii=False))
            insert_events.get("events").append(event_info)
            # print "已推送新增事件 - %s" % (json.dumps(event_info, ensure_ascii=False))

    # kafka_service.create_topic(topic)
    if insert_events.get("events"):
        insert_events.update({"time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        kafka_service.send_kafka(topic, json.dumps(insert_events, ensure_ascii=False))
        print "已推送新增事件:%d - %s" % (len(insert_events.get("events")), json.dumps(insert_events, ensure_ascii=False))

    if update_events.get("events"):
        update_events.update({"time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        kafka_service.send_kafka(topic, json.dumps(update_events, ensure_ascii=False))
        print "已推送更新事件:%d - %s" % (len(update_events.get("events")), json.dumps(update_events, ensure_ascii=False))

if __name__ == '__main__':
    run()
