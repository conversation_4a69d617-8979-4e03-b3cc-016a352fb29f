#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
大数据任务监控脚本， 通过check.sh脚本执行
每5分钟执行一次
用于检测大数据任务的运行状态, 返回挂掉的任务拉起脚本路径
"""
import traceback

import requests

from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import TASK

mlog = logger.init_logger('check_big_data')

IP = "************"
username = 'u_wa_wa1c_crossborderanomaly_nsfocus'
password = '92d7ddd2a010c59511dc2905b7e14f64'


def handle_apps(data, ):
    task_name_list = []
    task_run_count = {}
    mlog.info("-" * 108)
    for app in data.get('apps', {}).get('app', []):
        task_name_list.append(app['name'])
        # 统计任务运行中的数量，大于1的任务需要kill掉重新拉起
        if task_run_count.get(app['name']):
            task_data = task_run_count.get(app['name'])
            task_data[0] = task_data[0] + 1
            task_data[1].append(app['id'])

            task_run_count[app['name']] = task_data
        else:
            task_run_count[app['name']] = [1, [app['id']]]
        mlog.info(
            "Task_name:{name},     \tState: {state}, \tApplication ID: {id}".format(id=app['id'],
                                                                                    state=app['state'],
                                                                                    name=app['name']))
    mlog.info("-" * 108)
    return task_name_list, task_run_count


def check_task():
    """
    找出需要拉起的任务
    返回任务脚本的路径
    :return:
    """
    try:
        task_list = TASK.keys()
        url = 'http://{}:8088/ws/v1/cluster/apps'.format(IP)
        params = {'state': 'RUNNING', 'queue': 'root.wa1c.wa_wa1c_crossborderanomaly_realtime'}
        auth = (username, password)
        mlog.info("请求方式: GET, URL: {} , params: {} , auth: {}".format(url, params, auth))
        response = requests.get(url, params=params, auth=auth)

        if response.status_code == 200:
            data = response.json()
            # 处理返回的JSON数据
            task_name_list, task_run_count = handle_apps(data)
        else:
            mlog.error("Failed to retrieve data. Status code: {}".format(response.status_code))
            # 查询RUNNING任务失败--抛出异常
            raise ValueError("请求失败")
        now_task_name_list = list(set(task_list) - set(task_name_list))
        mlog.info("任务运行中的数量:{}".format(task_run_count))
        # 当认为RUNNING数量大于1， kill掉任务后, 将其放入待拉起列表
        for name, count_id in task_run_count.items():
            if count_id[0] > 1:
                # kill 掉任务
                data = {"state": "KILLED"}
                code_list = []
                for id_ in count_id[1]:
                    kill_url = 'http://{}:8088/ws/v1/cluster/apps/{}/state'.format(IP, id_)
                    kill_response = requests.put(url=kill_url, json=data, auth=auth)
                    mlog.info("请求url：{}, 请求方式：PUT, data:{}, auth:{}, Application：{}, 响应状态码：{}".format(
                        kill_url, data, auth, id_, kill_response.status_code))
                    if kill_response.status_code == 202:
                        code_list.append(True)
                    else:
                        code_list.append(False)
                mlog.info("kill_task_status_list:{}".format(code_list))
                if all(code_list) and code_list:
                    now_task_name_list.append(name)
        mlog.info("now_task_name_list:{}".format(now_task_name_list))
        for name in now_task_name_list:
            path = TASK.get(name)
            mlog.info("需要拉起的任务脚本路径：{}".format(path))
            # 因sh脚本需要，以print返回数据
            print path  # NOSONAR
    except Exception as e:
        mlog.error(e)
        mlog.error(traceback.format_exc())


if __name__ == '__main__':
    check_task()
