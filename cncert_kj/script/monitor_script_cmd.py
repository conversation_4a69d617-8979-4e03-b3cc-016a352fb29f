#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
巡检脚本, 通过命令行方式查询堆积, root用户执行
每5分钟或者是10分钟运行一次
对运行的大数据任务， 接入系统，告警/事件数据进行巡检，观察系统是否正常
"""
import logging
import os
import subprocess
import time
from logging.handlers import TimedRotatingFileHandler

import grp
import pwd

DATE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
PATH_APP = "/home/<USER>/ISOP/apps/cncert_kj/"

broker_list = ["***********:9094", "*************:9094", "*************:9094", "*************:9094",
               "*************:9094", "*************:9094", "*************:9094", "*************:9094",
               "*************:9094", "*************:9094", "*************:9094", "*************:9094",
               "*************:9094", "*************:9094", "*************:9094"]

# 大数据任务
TASK = {
    u"GJK-NETFLOW-IP-DAY-STATISTIC": "/opt/apps/gjk_data_proc/esloaderbin/start_netflow_ip_day_statistic.sh",
    u"GJK-NETFLOW-STATISTIC": "/opt/apps/gjk_data_proc/esloaderbin/start_netflow_statistic.sh",
    u"GJK-NETFLOW-SETL": "/opt/apps/gjk_data_proc/bin/start_netflow_setl.sh",
    u"GJK-TCLOG-SETL": "/opt/apps/gjk_data_proc/bin/start_tclog_setl.sh",
    u"GJK-TCLOG-IP-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_tclog_ip_statistic.sh",
    u"GJK-TCLOG-IPPAIR-STATISTIC": "/opt/apps/gjk_data_proc/bin/start_tclog_ippair_statistic.sh",
    u"GJK-TCLOG-DETECT": "/opt/apps/gjk_data_proc/bin/start_tclog_detect.sh",
    u"GJK-NETFLOW-DETECT": "/opt/apps/gjk_data_proc/bin/start_netflow_detect.sh"
}

# 消费者组
groups = [
    "wa_kjycllfx_nsfocus_dzy_group",
    "DZY_GROUP_ID_TCLOG_IP_STATISTIC",
    "DZY_GROUP_ID_TCLOG_IP_PAIR_STATISTIC",
    "GROUP_ID_NETFLOW_STATISTIC",
    "GROUP_ID_NETFLOW_DAY_STATISTIC",
    "GJK_KJ_TCLOG_DETECT_GROUP_ID",
    "GJK_KJ_NETFLOW_DETECT_GROUP_ID",
    "GJK_KJ_NETFLOW_COUNT_GROUP",

]

cmd = "/opt/apps/kafka/bin/kafka-consumer-groups.sh --bootstrap-server {broker_list} --describe --group {group} " \
      "--timeout 60000 --command-config /opt/apps/kafka/config/client_SK_crossborder_sha_512.properties"


def get_logger(logfile="monitor"):
    """
    获取日志句柄的方法
    """
    logger = logging.getLogger(logfile)
    logger.setLevel(logging.DEBUG)
    log_root = os.path.join(PATH_APP, "logs")
    if not os.path.exists(log_root):
        os.mkdir(log_root)
    file_handle = TimedRotatingFileHandler(os.path.normpath(log_root + "/" + logfile + ".log"), 'midnight',
                                           backupCount=7)
    file_handle.suffix = "%Y-%m-%d"
    file_handle.setLevel(logging.DEBUG)
    console_handle = logging.StreamHandler()
    console_handle.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(message)s')
    file_handle.setFormatter(formatter)
    console_handle.setFormatter(formatter)
    logger.addHandler(file_handle)
    logger.addHandler(console_handle)

    # 定义文件路径和用户、组名称
    file_path = os.path.normpath(log_root + "/" + logfile + ".log")
    user_name = 'master'
    group_name = 'master'

    # 获取用户和组的ID
    user_id = pwd.getpwnam(user_name).pw_uid
    group_id = grp.getgrnam(group_name).gr_gid
    # 更改文件的所有者和组
    os.chown(file_path, user_id, group_id)
    return logger


mlog = get_logger("monitor")


def print_table(array):
    # 计算每列的最大宽度
    col_widths = [max(len(str(item)) for item in col) for col in zip(*array)]

    # 格式化表格的行
    def format_row(row_):
        return " | ".join("{:{}}".format(item, col_widths[idx]) for idx, item in enumerate(row_))

    # 打印表头
    header = array[0]
    mlog.info(format_row(header))
    mlog.info("-+-".join("-" * width for width in col_widths))
    # 打印数据行
    for row in array[1:]:
        mlog.info(format_row(row))


def give_unit(num):
    units = ["", "十", "百", "千", "万", "十万", "百万", "千万", "亿", "十亿", "百亿", "千亿", "万亿"]
    return units[len(str(num)) - 1]


def monitor():
    """
    查看kafka组的堆积情况, 总lag   和  平均lag
    :return:
    """
    try:
        data = [["TYPE", "GROUP", "TOTAL_LAG", "TOTAL_UNIT", "AVG_LAG", "AVG_UNIT"]]
        topic1 = "wa_zfd510_jc510_t_connlog_wa1zdmb_dt"
        topic2 = "wa_zfd510_jc510_t_flowdata_pms_dt"
        broker_str = ",".join(broker_list)
        for group in groups:
            data = handle_monitor_group(group, topic1, topic2, broker_str, data)
        print_table(data)
    except Exception as e:
        mlog.error(e)


def handle_monitor_group(group, topic1, topic2, broker_str, data):
    new_cmd = cmd.format(broker_list=broker_str, group=group)
    output = subprocess.check_output(new_cmd, shell=True, stderr=subprocess.PIPE).decode("utf-8")
    # 解析输出数据
    lines = output.strip().split("\n")[1:]  # 去掉表头，只保留数据行
    lag_values = []
    for line in lines:
        fields = line.split()
        lag = int(fields[5])  # 现网环境第6个字段是LAG值
        # 开发环境 lag = int(fields[4])  # 开发环境第5个字段是LAG值
        lag_values.append(lag)
    # 计算和与平均值
    total_lag = sum(lag_values)
    if total_lag == 0:
        average_lag = 0
    else:
        average_lag = total_lag / len(lag_values)
    if group == "wa_kjycllfx_nsfocus_dzy_group":
        topic1_lag = []
        topic2_lag = []
        for line in lines:
            fields = line.split()
            lag = int(fields[5])  # 现网环境第6个字段是LAG值
            # 开发环境 lag = int(fields[4])  # 开发环境第5个字段是LAG值
            topic = fields[1]
            if topic == topic1:
                topic1_lag.append(lag)
            elif topic == topic2:
                topic2_lag.append(lag)
        total1_lag = sum(topic1_lag)
        total2_lag = sum(topic2_lag)
        if total1_lag == 0:
            average1_lag = 0
        else:
            average1_lag = total1_lag / len(topic1_lag)

        if total2_lag == 0:
            average2_lag = 0
        else:
            average2_lag = total2_lag / len(topic2_lag)
        data.append(["TOPIC", topic1, total1_lag, give_unit(total1_lag), average1_lag, give_unit(average1_lag)])
        data.append(["TOPIC", topic2, total2_lag, give_unit(total2_lag), average2_lag, give_unit(average2_lag)])
        return data
    data.append(["GROUP", group, total_lag, give_unit(total_lag), average_lag, give_unit(average_lag)])
    return data

def timestamp2format(timestamp):
    time_local = time.localtime(timestamp)
    time_format = time.strftime(DATE_TIME_FORMAT, time_local)
    return time_format


def execute_database_middle_script_sh():
    script_path = '/home/<USER>/ISOP/apps/cncert_kj/script/database_middle_script.sh'
    os.chmod(script_path, 0o777)
    subprocess.Popen(script_path, shell=True)


if __name__ == '__main__':
    """
    root用户执行
    crontab -e
    写入
    */20 * * * * source /etc/profile; su master; python /home/<USER>/ISOP/apps/cncert_kj/script/monitor_script_cmd.py 2>&1
    """
    now_time = timestamp2format(int(time.time()))
    mlog.info("【查看kafka组的堆积情况:{}】".format(now_time))
    monitor()
    # 告警和事件最大结束时间
    execute_database_middle_script_sh()
