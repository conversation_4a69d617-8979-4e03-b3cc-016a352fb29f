#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    资产系统
"""
import json
import os
import time
from collections import defaultdict

import requests

from cncert_kj.utils import logger
from cncert_kj.utils.conf_util import CommonConf, JsonTools, base_64_encode
from cncert_kj.utils.oauth_server import OAuthUtil
from cncert_kj.utils.request_base import BaseRequestsClient
from cncert_kj.utils.redis_utils import cache

mlog = logger.init_logger('request_asset')
Content_Type = "application/json"
Content_Bearer = "Bearer "
CONTENT_IP_FORMAT = 'ip="{}"'


class RequestAsset(BaseRequestsClient):

    def __init__(self, base_url=None, headers=None, timeout=300, retries=None):
        super(RequestAsset, self).__init__(base_url=base_url, headers=headers, timeout=timeout, retries=retries)
        self.common_conf = CommonConf()
        self.visit_record_unit = self.common_conf.get_visit_record_unit()
        # 资产系统
        self.asset_api = self.visit_record_unit.get("asset_api")  # 资产搜索接口
        self.asset_es_search_next_api = self.visit_record_unit.get("asset_es_search_next_api")  # 资产滚动查询，post

        # 资产系统登录信息
        self.api_login_url = self.visit_record_unit.get("api_login_url")  # 资产通过权限系统的登录接口
        self.oauth = OAuthUtil(remote_app_id="<EMAIL>")
        self.request_source = self.visit_record_unit.get("asset_request_source")  # 标识符
        # >> self.login_url = self.visit_record_unit.get("login_url")  # 资产系统登录接口
        # >> self.username = self.visit_record_unit.get("asset_username")  # 账号
        # >> self.password = self.visit_record_unit.get("asset_password")  # 密码。需要sha1加密
        # 资产探测任务接口
        self.tasks_url = self.visit_record_unit.get("tasks_url")

        # 资产登录接口返回的token存储路径
        self.token_path = "/home/<USER>/store/kj_flow_detect/oauth_token.json"

        #  资产系统登录后的token
        self.token_ = ''
        self.record_time = None
        self.redis = cache
        self.redis_expired = 60 * 60 * 4

    def set_token_to_redis(self, asset_token):
        """
            向redis中存入资产的token
        """
        if asset_token:
            dic_ = {"token": asset_token, "timestamp": int(time.time())}
            mlog.info("将token写入redis:{}".format(dic_))
            self.redis.execute("set", "asset_token", json.dumps(dic_))
            self.redis.execute("expire", "asset_token", self.redis_expired)
            return True
        else:
            return False

    def get_token_from_redis(self):
        """
            从redis中获取资产的token
        """
        try:
            result = self.redis.execute("get", "asset_token")
            if result:
                dic_ = json.loads(result)
                # mlog.info("从redis中获取到token:{}".format(dic_.get("token")))
                return dic_.get("token")
            else:
                mlog.warning("redis中没有找到token,需要重新登录")
                return False
        except Exception as e:
            mlog.exception("从redis中获取token报错：{}".format(e))
            return False

    def login(self):
        """
            接口地址：{{ host_server}}/api/v1/login
            请求类型：application/json
            请求方式：POST
            Hearder头信息: Request-Source = 'xxx'
            接口备注：系统登录接口

            参数说明：Request-Source是资产探测系统分配的第三方标识，对接前需要前获得该标识才能正常登录。
        """

        try:
            token, headers, access_token = self.oauth.get_token()
            params_ = {
                "access_token": access_token,
                "token": token
            }
            headers = {"Content-Type": Content_Type}
            # mlog.info("登录接口请求参数：{}".format(params_))
            data = requests.post(self.api_login_url, json=params_, headers=headers)
            # mlog.info("登录接口返回结果：{}".format(data.content))

            if data.status_code == 200:
                if data.text:
                    res = json.loads(data.text)
                    code = res.get("code")
                    if code == 200:
                        token_ = res.get("token")
                        self.set_token_to_redis(token_)
                        # >>
                        # dic_ = {"token": token_, "timestamp": int(time.time())}
                        # JsonTools().write_file(self.token_path, dic_)
                        # mlog.info("登录成功:{}".format(token_))
                        return token_
                    else:
                        mlog.error("登录失败，data.text.code:{}，接口返回结果：{}".format(code, data.content))
                        return False
                else:
                    mlog.error("登录失败，data.text为空，接口返回结果：{}".format(data.content))
                    return False
            else:
                mlog.error("登录失败，data.status_code状态码：{},接口返回结果：{}".format(data.status_code, data.content))
                return False
        except Exception as e:
            mlog.exception("资产系统登录方法报错：{}".format(e))
            return False

    def get_oauth_token(self):
        """
        获取oauth_token，存储在本地，并验证其有效性
        :return: 有效的token或None
        """
        try:
            #>> 首先尝试使用本地存储的有效token
            # local_token = self._get_valid_local_token()
            local_token = self.get_token_from_redis()
            if local_token:
                return local_token

            # 本地token无效，重新登录获取
            return self._login_with_retry()

        except Exception as e:
            mlog.exception("获取OAuth token时发生异常: {}".format(e))
            return None

    def _get_valid_local_token(self):
        """
        获取本地有效的token
        :return: 有效的token或None
        """
        # 读取OAuth token
        mlog.info("开始获取本地oauth_token...")
        oauth_token = self.oauth.read_token()
        if not oauth_token:
            mlog.warning("本地未找到OAuth token")
            return None

        mlog.info("获取到本地oauth_token。开始校验oauth_token...")

        # 验证OAuth token是否有效
        if not self.oauth.verify_token(oauth_token):
            mlog.warning("本地OAuth token已失效")
            return None

        mlog.info("本地OAuth token有效")

        # 检查asset token文件是否存在
        if not os.path.exists(self.token_path):
            mlog.warning("Asset token文件不存在: {}".format(self.token_path))
            return None

        # 读取asset token
        try:
            token_data = JsonTools().read_file(self.token_path)
            asset_token = token_data.get("token")
            if not asset_token:
                mlog.warning("Asset token文件中未找到有效token")
                return None

            return asset_token
        except Exception as e:
            mlog.exception("读取Asset token文件失败: {}".format(e))
            return None

    def _login_with_retry(self, max_retries=3):
        """
        重试登录获取token
        :param max_retries: 最大重试次数
        :return: token或None
        """
        for attempt in range(max_retries):
            try:
                mlog.info("尝试登录获取token (第{}/{}次)".format(attempt + 1, max_retries))

                token = self.login()
                if token:
                    mlog.info("登录成功")
                    return token

                # 登录失败，记录日志并等待重试
                if attempt < max_retries - 1:  # 不是最后一次尝试
                    wait_time = 2 * (attempt + 1)  # 递增等待时间
                    mlog.warning("登录失败，{}秒后进行第{}次重试".format(wait_time, attempt + 2))
                    time.sleep(wait_time)
                else:
                    mlog.error("登录失败，已达到最大重试次数")

            except Exception as e:
                wait_time = 3 * (attempt + 1)  # 异常情况等待更长时间
                mlog.error("登录过程中发生异常: {}".format(e))

                if attempt < max_retries - 1:
                    mlog.warning("{}秒后重试".format(wait_time))
                    time.sleep(wait_time)

        return None


    # >>
    # def query_asset(self, ip_list=[], port_list=[], size="10"):
    #     """
    #     查询资产系统API数据
    #     :param params: 查询参数
    #     :return: 资产系统API响应数据
    #     """
    #     try:
    #
    #         # 分批处理ip_list
    #         params_list = []
    #         for i in range(0, len(ip_list), 20):
    #             # 验证token
    #             ip_ = ip_list[i:i + 20]
    #             ip_str_list = ['ip="{}"'.format(i) for i in ip_]
    #             q_ = " || ".join(ip_str_list)
    #             q = "({})".format(q_)
    #             if port_list:
    #                 port_str_list = ['port="{}"'.format(port) for port in port_list]
    #                 q += " && ({})".format(" || ".join(port_str_list))
    #             qbase64 = base_64_encode(q)
    #             params_ = {
    #                 "q": q,
    #                 "qbase64": qbase64,
    #                 "full": "true",
    #                 "fraud": "false",
    #                 "page": "1",
    #                 "size": size
    #             }
    #             params_list.append(params_)
    #
    #         # >> mlog.info("资产搜索接口请求参数：{} ".format(json.dumps(params_, indent=4, ensure_ascii=False)))
    #         # token_ = self.get_oauth_token()
    #         # headers = {
    #         #     "Authorization": "Bearer " + token_.encode("utf-8"),
    #         #     "Request-Source": self.request_source,
    #         #     "Content-Type": "application/json"
    #         # }
    #         res_dic = defaultdict(list)
    #
    #         for params_ in params_list:
    #             token_ = self.get_oauth_token()
    #             headers = {
    #                 "Authorization": "Bearer " + token_.encode("utf-8"),
    #                 "Request-Source": self.request_source,
    #                 "Content-Type": Content_Type
    #             }
    #             response_ = self.get(self.asset_api, params=params_, headers=headers)
    #             if not response_:
    #                 continue
    #             if response_.status_code == 200:
    #                 res = json.loads(response_.text)
    #                 if res.get("code") != 200:
    #                     mlog.error(u"资产搜索失败，接口结果：{} \n 请求结果:{}".format(res, params_))
    #                     if "expired" in res.get("msg") or res.get("code") == 401:
    #                         mlog.warning(u"资产token已失效，重新登录获取token，并再次请求")
    #                         token_ = self.login()
    #                         headers.update({"Authorization": "Bearer " + token_.encode("utf-8")})
    #                         response_ = self.get(self.asset_api, params=params_, headers=headers)
    #                         if response_.status_code == 200:
    #                             res = json.loads(response_.text)
    #                             if res.get("code") != 200:
    #                                 mlog.error(u"再次请求资产搜索失败，接口结果：{} \n 请求结果:{}".format(res, params_))
    #                                 continue
    #                             else:
    #                                 asset_data = res.get("data", {}).get("assets", [])
    #                                 if not asset_data:
    #                                     continue
    #                                 for asset in asset_data:
    #                                     ip_ = asset.get("ip")
    #                                     res_dic[ip_].append(asset)
    #
    #                     continue
    #                 else:
    #                     asset_data = res.get("data", {}).get("assets", [])
    #                     if not asset_data:
    #                         continue
    #                     for asset in asset_data:
    #                         ip_ = asset.get("ip")
    #                         res_dic[ip_].append(asset)
    #             else:
    #                 mlog.error(u"资产搜索失败，接口结果：{}".format(response_.content))
    #
    #         # data_list = self.get(self.asset_api, params=params_list, headers=headers)
    #         # if not data_list:
    #         #     return {}
    #         #
    #         # res_dic = defaultdict(list)
    #         # for index, data in enumerate(data_list):
    #         #     # 获取对应的请求参数
    #         #     request_params = params_list[index]
    #         #     if data.status_code == 200:
    #         #         res = json.loads(data.text)
    #         #         if res.get("code") != 200:
    #         #             mlog.error(u"资产搜索失败，接口结果：{} \n 请求结果:{}".format(res, request_params))
    #         #             if "expired" in res.get("msg") or res.get("code") == 401:
    #         #                 mlog.warning(u"资产token已失效，重新登录获取token")
    #         #
    #         #             continue
    #         #         else:
    #         #             asset_data = data.get("data", {}).get("assets", [])
    #         #             if not asset_data:
    #         #                 continue
    #         #             for asset in asset_data:
    #         #                 ip_ = asset.get("ip")
    #         #                 res_dic[ip_].append(asset)
    #         #     else:
    #         #         mlog.error(u"资产搜索失败，接口结果：{}".format(data.content))
    #         return res_dic
    #     except Exception as e:
    #         mlog.exception(u"访问资产搜索接口失败:{}".format(e))
    #         return {}

    def query_asset(self, ip_list=[], port_list=[], size="10"):
        """
        查询资产系统API数据
        :param ip_list: IP地址列表
        :param port_list: 端口列表
        :param size: 每页大小
        :return: 资产系统API响应数据
        """
        if not ip_list:
            mlog.warning("IP列表为空，无法查询资产")
            return {}

        try:
            params_list = self._build_query_params(ip_list, port_list, size)
            res_dic = defaultdict(list)

            for params_ in params_list:
                assets = self._query_single_batch(params_)
                if not assets or assets is None:
                    mlog.info("返回为None:{}, 请求参数:{}".format(assets, params_))
                    continue
                self._merge_assets_to_result(assets, res_dic)

            return res_dic
        except Exception as e:
            mlog.exception(u"访问资产搜索接口失败:{}".format(e))
            return {}

    def _build_query_params(self, ip_list, port_list, size):
        """构建查询参数列表"""
        params_list = []

        # 分批处理ip_list，每批最多20个IP
        for i in range(0, len(ip_list), 20):
            batch_ips = ip_list[i:i + 20]
            query = self._build_query_string(batch_ips, port_list)

            params_ = {
                "q": query,
                "qbase64": base_64_encode(query),
                "full": "true",
                "fraud": "false",
                "page": "1",
                "size": size
            }
            params_list.append(params_)

        return params_list

    def _build_query_string(self, ip_list, port_list):
        """构建查询字符串"""
        # 构建IP查询条件
        ip_conditions = [CONTENT_IP_FORMAT.format(ip) for ip in ip_list]
        query = "({})".format(" || ".join(ip_conditions))

        # 添加端口查询条件
        if port_list:
            port_conditions = ['port="{}"'.format(port) for port in port_list]
            query += " && ({})".format(" || ".join(port_conditions))

        return query

    def _query_single_batch(self, params_):
        """查询单个批次的资产数据"""
        token_ = self.get_oauth_token()
        headers = self._build_headers(token_)

        response_ = self.get(self.asset_api, params=params_, headers=headers)
        if not response_:
            return []

        if response_.status_code != 200:
            mlog.error(u"资产搜索失败，HTTP状态码：{}，接口结果：{}".format(
                response_.status_code, response_.content))
            return []

        return self._process_response(response_, params_, headers)

    def _build_headers(self, token_):
        """构建请求头"""
        return {
            "Authorization": Content_Bearer + token_.encode("utf-8"),
            "Request-Source": self.request_source,
            "Content-Type": Content_Type
        }

    def _process_response(self, response_, params_, headers):
        """处理API响应"""
        try:
            res = json.loads(response_.text)
        except (ValueError, TypeError) as e:
            mlog.error(u"解析响应JSON失败：{}".format(e))
            return []

        if res.get("code") == 200:
            return self._extract_assets_from_response(res)

        # 处理token过期的情况
        if self._is_token_expired(res):
            return self._retry_with_new_token(params_, headers)

        mlog.error(u"资产搜索失败，接口结果：{} \n 请求参数:{}".format(res, params_))
        return []

    def _is_token_expired(self, response_data):
        """检查token是否过期"""
        msg = response_data.get("msg", "")
        code = response_data.get("code")
        return "expired" in msg or code == 401

    def _retry_with_new_token(self, params_, headers):
        """使用新token重试请求"""
        mlog.warning(u"资产token已失效，重新登录获取token，并再次请求")

        new_token = self.login()
        if not new_token:
            mlog.error(u"重新登录失败")
            return []

        headers.update({"Authorization": Content_Bearer + new_token.encode("utf-8")})
        retry_response = self.get(self.asset_api, params=params_, headers=headers)

        if not retry_response or retry_response.status_code != 200:
            mlog.error(u"重试请求失败")
            return []

        try:
            retry_res = json.loads(retry_response.text)
            if retry_res.get("code") == 200:
                return self._extract_assets_from_response(retry_res)
            else:
                mlog.error(u"重试请求资产搜索失败，接口结果：{} \n 请求参数:{}".format(
                    retry_res, params_))
                return []
        except (ValueError, TypeError) as e:
            mlog.error(u"解析重试响应JSON失败：{}".format(e))
            return []

    def _extract_assets_from_response(self, response_data):
        """从响应中提取资产数据"""
        return response_data.get("data", {}).get("assets", [])

    def _merge_assets_to_result(self, assets, res_dic):
        """将资产数据合并到结果字典中"""
        for asset in assets:
            ip_ = asset.get("ip")
            if ip_:
                res_dic[ip_].append(asset)

    def asset_es_search(self, ip_, token_, port_list=[], size="10"):
        """
            资产搜索接口
            ip_:str
            port_: list
            token_: str
        """
        try:
            if isinstance(ip_, list):
                if len(ip_) > 20:
                    raise ValueError("ip_ 参数为列表时，长度不能超过20")
                ip_str_list = [CONTENT_IP_FORMAT.format(i) for i in ip_]
                q_ = " || ".join(ip_str_list)
                q = "({})".format(q_)
            else:
                q = CONTENT_IP_FORMAT.format(ip_)
            if port_list:
                port_str_list = ['port="{}"'.format(port) for port in port_list]
                q += " && ({})".format(" || ".join(port_str_list))

            qbase64 = base_64_encode(q)
            params_ = {
                "q": q,
                "qbase64": qbase64,
                "full": "true",
                "fraud": "false",
                "page": "1",
                "size": size
            }

            headers = {
                "Authorization": Content_Bearer + token_.encode("utf-8"),
                "Request-Source": self.request_source,
                "Content-Type": Content_Type
            }

            # mlog.info("资产搜索接口请求参数：{} ".format(json.dumps(params_, indent=4, ensure_ascii=False)))

            data = requests.get(self.asset_api, params=params_, headers=headers)
            # mlog.info("请求资产搜索接口结果：{}".format(data.content))

            if data.status_code == 200:
                return data.text
            else:
                mlog.error("资产搜索失败，接口结果：{}".format(data.content))
                return False
        except Exception as e:
            mlog.exception("访问资产搜索接口失败:{}".format(e))
            # mlog.error(traceback.format_exc())
            return False

    def asset_search_next(self, query, scroll_id=""):
        """
            通过scroll id，每次导出limit条数据，直到导出所有数据；该接口有时间限制，每次请求间隔不能超过5分钟。
            query：封装好的fofa查询语句
            token_:token
            scroll_id:滚动ID，首次请求后返回，后续的请求都需要带---scroll_id位置:{"data":{"data":{"scroll_id":"FGluY2x1ZGVfY29u……" ,"assets":[]}}}
        """
        try:
            mlog.info("开始滚动查询...")
            # 验证token
            token_ = self.get_oauth_token()
            qbase64 = base_64_encode(query)
            params_ = {
                "query": query,  # 非必填
                "qbase64": qbase64,
                "full": True,
                "fields": "ip,port,protocol,product,geoip",  # 非必填
                "limit": 10000,
                "scroll_id": scroll_id
            }
            params_log = {k:v for k,v in params_.items() if k!="scroll_id"}
            mlog.info("资产滚动查询请求参数:{}".format(json.dumps(params_log, indent=4, ensure_ascii=False)))
            if token_:
                token_ = token_.encode("utf-8")
            else:
                token_ = ""
            headers = {
                "Authorization": Content_Bearer + token_,
                "Request-Source": self.request_source,
                "Content-Type": Content_Type
            }
            # >> data = requests.post(self.asset_es_search_next_api, json=params_, headers=headers)
            data = self.post(self.asset_es_search_next_api, json=params_, headers=headers)
            # mlog.info("headers:{}".format(data.headers))
            if data:
                if data.status_code == 200:
                    mlog.info("滚动查询完成...")
                    return data.text
                else:
                    mlog.error("资产搜索失败，接口结果：{}".format(data.content))
                    return False
            else:
                mlog.error("资产搜索失败，请检查url是否正确")
                return False

        except Exception as e:
            mlog.exception("访问资产搜索接口失败:{}".format(e))
            # mlog.error(traceback.format_exc())
            return False

    def create_asset_detect_task(self, headers, ip_list, port_list, ip_type=2):
        """
            创建资产探测任务
        """
        task_content = {
            "bandwidth": "300",  # 扫描带宽
            "ipScanType": 1,  # ip探测类型，1-完整，2-部分
            "ipType": ip_type,  # 2 IPV4 3 IPV6 4 域名
            "enableHoneypot": True,  # 是否开启蜜罐黑名单
            "enableTargetCache": True,  # 是否缓存探测目标
            "enableBlackList": True,  # 是否启用黑名单过滤
            "censusScan": False,
            "enableProtocol": False,
            "ipText": ip_list,  # IP地址列表，逗号分隔
            "portText": port_list,  # 端口列表，逗号分隔
            "ipSourceType": 1,  # IP地址输入方式，1 输入IP信息2  ip目标分组 3 经纬度下发 4 地域下发
            "portType": 3,  # 端口参数类型，1-常用端口，2-指定分组端口，3-输入端口
            "scheduleMode": 0,  # 调度策略，1-按IP拆分，2-按端口拆分

            # "ipScanContent": 1,  # 探测内容，1-IP存活探测，2-端口探测
            # "portGroupId": [1],  # 端口分组ID，portType==2时，该值有效
            # "ipGroupId": [1],  # IP分组ID数组,前端回显使用
        }

        params_ = {
            "name": "kj_asset_detect",  # 任务名称
            "isLoop": 1,  # 是否循环，1-单次任务，2-循环任务
            "internetMode": 1,  # 上网方式，1-动态拨号，2-固定专线
            "priority": 1,  # 优先级，1-高，2-中，3-低
            "reissue": 1,  # 失败是否重新调度，1-是，2-否
            "assetType": 1,  # 1:IP资产探测
            "dispatchTimes": 3,  # 重新调度次数，开启失败重新调度时生效
            "source": "api-z",  # 表明通过V、Z网下发任务，请严格按照实际情况填写
            # "isWait": 1,   # 节点不足时是否等待执行，1-立即执行，2-接受等待
            # "passThroughMode": True,   # 通过Z网下发任务时 必须设置为true，V网时设置为false
            "taskContent": task_content,  # 任务参数详情，不同的任务对应不同的数据结构。
            "taskLabels": {"nodeType": 5, "nodeCount": 8},  # 任务标签参数
            # "taskLabels.nodeType": 1,   # 节点参数类型，1-按节点，2-按标签，3-按区域下发
            # "taskLabels.nodeRegion": [1,2],   # 节点所在大区ID标签，nodeType=3时有效
            # "taskLabels.nodeLabel": "Tag-beijing",   # 节点标签数组，nodeType=2时有效
            # "taskLabels.nodeId": "node-1,node-3",   # 节点ID数组，nodeType=1时有效
        }
        headers["Request-Source"] = self.request_source
        try:
            verify_ = False
            data = self.post(self.tasks_url, json=params_, verify=verify_, headers=headers)
            mlog.info("请求资产探测任务创建接口结果：{}".format(data.content))
            if data.status_code == 200:
                if data.text:
                    res = json.loads(data.text)
                    return res
                else:
                    return False
            else:
                return False
        except Exception as e:
            mlog.exception("创建资产探测任务报错:{}".format(e))
            return False

    def do_asset_url(self, ip_, port_list=[], size="10"):
        """
            访问资产接口，判断是否为真实数据库
            http://**********/api/v1/es_search?q=ip="***************"&qbase64=****************************&full=true&page=1&size=10
        """
        try:
            if not self.record_time or (time.time() - self.record_time) > 60 * 5:
                self.do_asset_url_login()

            res_data = self.asset_es_search(ip_, self.token_, port_list, size)
            if res_data:
                res_data = json.loads(res_data)
                # mlog.info("资产搜索接口结果：{}".format(res_data))
                return res_data
            else:
                mlog.info("资产搜索失败：{}".format(ip_))
                return {}
        except Exception as e:
            mlog.exception("资产搜索失败-{}:{}".format(ip_, e))
            return {}

    def do_asset_url_login(self):
        """
            资产系统登录接口
        """
        try:
            token_ = self.login()
            if token_:
                mlog.info("登录成功,token:{}".format(token_))
                self.token_ = token_
                self.record_time = int(time.time())
                return True
            else:
                mlog.info("登录失败")
                return False
        except Exception as e:
            mlog.exception("登录失败:{}".format(e))
            return False


if __name__ == '__main__':
    a = RequestAsset()
    token_ = a.login()
    q = 'ip="*******"'
    qbase64 = base_64_encode(q)
    params_ = {
        "q": q,
        "qbase64": qbase64,
        "full": "true",
        "fraud": "false",
        "page": "1",
        "size": 10
    }
    headers = {
        "Authorization": Content_Bearer + token_.encode("utf-8"),
        "Request-Source": a.request_source,
        "Content-Type": "application/json"
    }
    res = a.get(a.asset_api, params=params_, headers=headers)
    # >>>
    # if res:
    #     print res.text
    # else:
    #     print "请求失败"